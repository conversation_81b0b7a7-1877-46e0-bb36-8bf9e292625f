# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request

class TestTravelPortal(http.Controller):

    @http.route(['/my/travel_test'], type='http', auth="user", website=True)
    def travel_test(self, **kw):
        """Simple test route"""
        return """
        <html>
        <head><title>Travel Test</title></head>
        <body>
            <h1>🎉 Travel Portal Test - SUCCESS!</h1>
            <p>Controller is working correctly!</p>
            <p><a href="/my">← Back to Portal</a></p>
            <hr>
            <p>User: %s</p>
            <p>Partner: %s</p>
        </body>
        </html>
        """ % (request.env.user.name, request.env.user.partner_id.name)

    @http.route(['/my/travel_requests_simple'], type='http', auth="user", website=True)
    def travel_requests_simple(self, **kw):
        """Simple travel requests page"""
        try:
            TravelRequest = request.env['travel.ticket.request']
            partner = request.env.user.partner_id
            
            # Simple domain
            domain = [('customer_id', '=', partner.commercial_partner_id.id)]
            requests = TravelRequest.search(domain, limit=10)
            
            html = """
            <html>
            <head><title>Travel Requests</title></head>
            <body>
                <h1>🛫 Travel Requests</h1>
                <p>Found %d requests</p>
                <ul>
            """ % len(requests)
            
            for req in requests:
                html += f"<li>{req.name} - {req.state}</li>"
            
            html += """
                </ul>
                <p><a href="/my">← Back to Portal</a></p>
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            return f"""
            <html>
            <body>
                <h1>❌ Error</h1>
                <p>Error: {e}</p>
                <p><a href="/my">← Back to Portal</a></p>
            </body>
            </html>
            """
