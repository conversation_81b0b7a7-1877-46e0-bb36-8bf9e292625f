<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Sequence for Travel Request Numbers -->
        <record id="seq_travel_ticket_request" model="ir.sequence">
            <field name="name">Travel Request Sequence</field>
            <field name="code">travel.ticket.request</field>
            <field name="prefix">TR%(year)s%(month)s</field>
            <field name="suffix"></field>
            <field name="padding">4</field>
            <field name="number_increment">1</field>
            <field name="number_next">1</field>
            <field name="implementation">standard</field>
        </record>
        
    </data>
</odoo> 