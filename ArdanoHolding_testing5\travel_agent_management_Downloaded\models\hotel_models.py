# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, timedelta


class HotelBookingRequest(models.Model):
    _name = 'hotel.booking.request'
    _description = 'Hotel Booking Request'
    _order = 'create_date desc'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'portal.mixin']

    # Basic Information
    name = fields.Char('Reference', required=True, copy=False, readonly=True, 
                       default=lambda self: _('New'))
    
    # Customer Information
    customer_id = fields.Many2one('res.partner', string='Customer', required=True,
                                  domain=[('is_company', '=', False)], tracking=True)
    partner_id = fields.Many2one('res.partner', string='Partner', related='customer_id', store=True,
                                 help="Portal access partner (same as customer)")
    
    # Hotel Details
    city_name = fields.Char('City Name', required=True, tracking=True,
                            help="City where the hotel is located")
    hotel_name = fields.Char('Hotel Name', required=True, tracking=True,
                             help="Name of the hotel")
    
    # Booking Dates
    check_in_date = fields.Date('Check In Date', required=True, tracking=True)
    check_out_date = fields.Date('Check Out Date', required=True, tracking=True)
    
    # Booking Details
    number_of_pax = fields.Integer('Number of PAX', required=True, default=1,
                                   tracking=True, help="Number of guests")
    room_type = fields.Char('Room Type', tracking=True,
                            help="Type of room (Single, Double, Suite, etc.)")
    board = fields.Selection([
        ('room_only', 'Room Only'),
        ('breakfast', 'Breakfast'),
        ('half_board', 'Half Board'),
        ('full_board', 'Full Board'),
        ('all_inclusive', 'All Inclusive'),
    ], string='Board', tracking=True, help="Meal plan included")
    
    special_request = fields.Text('Special Request',
                                  help="Any special requests (late check-in, room preferences, etc.)")
    
    # Supplier Information
    supplier_id = fields.Many2one('res.partner', string='Supplier', required=True,
                                  domain=[('is_company', '=', True), ('supplier_rank', '>', 0)],
                                  tracking=True)
    
    # Pricing
    supplier_cost = fields.Monetary('Supplier Cost', currency_field='currency_id', tracking=True,
                                    help="Cost charged by the supplier")
    customer_price = fields.Monetary('Customer Price', currency_field='currency_id', tracking=True,
                                     help="Price charged to the customer")
    profit_amount = fields.Monetary('Profit', currency_field='currency_id', 
                                    compute='_compute_profit', store=True,
                                    help="Profit = Customer Price - Supplier Cost")
    profit_percentage = fields.Float('Profit %', compute='_compute_profit', store=True,
                                     help="Profit percentage based on supplier cost")
    
    currency_id = fields.Many2one('res.currency', string='Currency', 
                                  default=lambda self: self.env.company.currency_id)
    
    # Status and Workflow
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', tracking=True)
    
    # Additional Information
    notes = fields.Text('Internal Notes')
    customer_notes = fields.Text('Customer Notes', help="Notes visible to customer")
    
    # Computed Fields for Display
    booking_summary = fields.Char('Booking Summary', compute='_compute_booking_summary', store=True)
    duration_nights = fields.Integer('Duration (Nights)', compute='_compute_duration', store=True)
    supplier_name = fields.Char('Supplier Name', compute='_compute_supplier_name')
    
    # Company
    company_id = fields.Many2one('res.company', string='Company', 
                                 default=lambda self: self.env.company)
    
    # Related Orders
    sale_order_id = fields.Many2one('sale.order', string='Sales Order', readonly=True,
                                    help="Sales order created for customer invoice")
    purchase_order_id = fields.Many2one('purchase.order', string='Purchase Order', readonly=True,
                                        help="Purchase order created for vendor bill")

    @api.model
    def create(self, vals):
        """Override create to generate sequence number"""
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('hotel.booking.request') or _('New')
        return super().create(vals)

    @api.depends('city_name', 'hotel_name', 'check_in_date', 'check_out_date')
    def _compute_booking_summary(self):
        """Compute booking summary for display"""
        for record in self:
            if record.hotel_name and record.city_name:
                summary = f"{record.hotel_name}, {record.city_name}"
                if record.check_in_date and record.check_out_date:
                    summary += f" ({record.check_in_date} - {record.check_out_date})"
                record.booking_summary = summary
            else:
                record.booking_summary = ""

    @api.depends('check_in_date', 'check_out_date')
    def _compute_duration(self):
        """Compute duration in nights"""
        for record in self:
            if record.check_in_date and record.check_out_date:
                delta = record.check_out_date - record.check_in_date
                record.duration_nights = delta.days
            else:
                record.duration_nights = 0

    @api.depends('supplier_cost', 'customer_price')
    def _compute_profit(self):
        """Compute profit amount and percentage"""
        for record in self:
            record.profit_amount = record.customer_price - record.supplier_cost
            if record.supplier_cost > 0:
                record.profit_percentage = (record.profit_amount / record.supplier_cost) * 100
            else:
                record.profit_percentage = 0.0

    def _compute_supplier_name(self):
        """Safely compute supplier name for portal access"""
        for record in self:
            try:
                record.supplier_name = record.supplier_id.name if record.supplier_id else 'Not specified'
            except:
                record.supplier_name = 'Not specified'

    @api.constrains('check_in_date', 'check_out_date')
    def _check_dates(self):
        """Validate booking dates"""
        for record in self:
            if record.check_in_date and record.check_out_date:
                if record.check_out_date <= record.check_in_date:
                    raise ValidationError(_("Check-out date must be after check-in date."))
                if record.check_in_date < fields.Date.today():
                    raise ValidationError(_("Check-in date cannot be in the past."))

    @api.constrains('number_of_pax')
    def _check_number_of_pax(self):
        """Validate number of guests"""
        for record in self:
            if record.number_of_pax <= 0:
                raise ValidationError(_("Number of guests must be greater than 0."))

    @api.constrains('supplier_cost', 'customer_price')
    def _check_pricing(self):
        """Validate pricing"""
        for record in self:
            if record.supplier_cost < 0:
                raise ValidationError(_("Supplier cost cannot be negative."))
            if record.customer_price < 0:
                raise ValidationError(_("Customer price cannot be negative."))

    def name_get(self):
        """Custom name_get to show reference and booking summary"""
        result = []
        for record in self:
            name = f"{record.name}"
            if record.booking_summary:
                name += f" - {record.booking_summary}"
            result.append((record.id, name))
        return result

    def action_confirm(self):
        """Confirm the hotel booking request"""
        for record in self:
            if record.state != 'draft':
                raise UserError(_("Only draft requests can be confirmed."))
            
            # Validate required fields before confirmation
            record._validate_for_confirmation()
            
            record.state = 'confirmed'
            record.message_post(
                body=_("Hotel booking request confirmed."),
                subject=_("Hotel Booking Request Confirmed")
            )

    def action_cancel(self):
        """Cancel the hotel booking request"""
        for record in self:
            if record.state == 'cancelled':
                raise UserError(_("Request is already cancelled."))
            
            record.state = 'cancelled'
            record.message_post(
                body=_("Hotel booking request cancelled."),
                subject=_("Hotel Booking Request Cancelled")
            )

    def action_set_to_draft(self):
        """Set hotel booking request back to draft"""
        for record in self:
            record.state = 'draft'
            record.message_post(
                body=_("Hotel booking request set to draft."),
                subject=_("Hotel Booking Request Reset")
            )

    def _validate_for_confirmation(self):
        """Validate all required data before confirmation"""
        self.ensure_one()
        errors = []
        
        if not self.customer_id:
            errors.append(_("Customer is required"))
        if not self.supplier_id:
            errors.append(_("Supplier is required"))
        if not self.city_name:
            errors.append(_("City name is required"))
        if not self.hotel_name:
            errors.append(_("Hotel name is required"))
        if not self.check_in_date:
            errors.append(_("Check-in date is required"))
        if not self.check_out_date:
            errors.append(_("Check-out date is required"))
        if self.number_of_pax <= 0:
            errors.append(_("Number of guests must be greater than 0"))
        
        if errors:
            raise ValidationError(_("Cannot confirm request:\n") + "\n".join(errors))

    @api.onchange('check_in_date')
    def _onchange_check_in_date(self):
        """Set check-out date one day after check-in by default"""
        if self.check_in_date and not self.check_out_date:
            self.check_out_date = self.check_in_date + timedelta(days=1)

    @api.onchange('supplier_cost')
    def _onchange_supplier_cost(self):
        """Auto-suggest customer price when supplier cost changes"""
        if self.supplier_cost and not self.customer_price:
            # Default margin of 20%
            self.customer_price = self.supplier_cost * 1.20

    def calculate_suggested_price(self):
        """Calculate suggested customer price based on supplier cost and default margin"""
        for record in self:
            if record.supplier_cost > 0:
                # Default margin of 20%
                default_margin = 0.20
                suggested_price = record.supplier_cost * (1 + default_margin)
                return suggested_price
            return 0.0

    # ===== INVOICING METHODS =====

    def action_create_invoice(self):
        """Create customer sales order and invoice"""
        self.ensure_one()
        if not self.customer_price:
            raise UserError(_("Customer price must be set to create invoice."))
        if not self.customer_id:
            raise UserError(_("Customer must be set to create invoice."))

        # Check if sales order already exists
        if self.sale_order_id:
            return self.action_view_sale_order()

        # Create sales order
        sale_order = self._create_sale_order()

        # Open the created sales order
        return {
            'type': 'ir.actions.act_window',
            'name': _('Sales Order'),
            'view_mode': 'form',
            'res_model': 'sale.order',
            'res_id': sale_order.id,
            'target': 'current',
        }

    def action_create_vendor_bill(self):
        """Create vendor purchase order and bill"""
        self.ensure_one()
        if not self.supplier_cost:
            raise UserError(_("Supplier cost must be set to create vendor bill."))
        if not self.supplier_id:
            raise UserError(_("Supplier must be set to create vendor bill."))

        # Check if purchase order already exists
        if self.purchase_order_id:
            return self.action_view_purchase_order()

        # Create purchase order
        purchase_order = self._create_purchase_order()

        # Open the created purchase order
        return {
            'type': 'ir.actions.act_window',
            'name': _('Purchase Order'),
            'view_mode': 'form',
            'res_model': 'purchase.order',
            'res_id': purchase_order.id,
            'target': 'current',
        }

    def _create_sale_order(self):
        """Create sales order for customer invoice"""
        self.ensure_one()

        # Get or create service product
        service_product = self.env['product.product'].search([
            ('name', '=', 'Hotel Booking Service'),
            ('type', '=', 'service')
        ], limit=1)

        if not service_product:
            service_product = self.env['product.product'].create({
                'name': 'Hotel Booking Service',
                'type': 'service',
                'categ_id': self.env.ref('product.product_category_all').id,
                'sale_ok': True,
                'purchase_ok': False,
                'list_price': 0.0,
            })

        # Create order line description
        description = f"Hotel Booking - {self.booking_summary}"
        if self.duration_nights > 0:
            description += f" for {self.duration_nights} nights"
        if self.number_of_pax > 1:
            description += f" for {self.number_of_pax} guests"
        if self.room_type:
            description += f" ({self.room_type})"
        if self.board:
            description += f" - {dict(self._fields['board'].selection)[self.board]}"

        order_lines = [(0, 0, {
            'product_id': service_product.id,
            'name': description,
            'product_uom_qty': 1,
            'price_unit': self.customer_price,
            'product_uom': service_product.uom_id.id,
        })]

        so_vals = {
            'partner_id': self.customer_id.id,
            'origin': self.name,
            'order_line': order_lines,
            'currency_id': self.currency_id.id,
            'pricelist_id': self.customer_id.property_product_pricelist.id or 1,
        }

        sale_order = self.env['sale.order'].create(so_vals)
        self.sale_order_id = sale_order.id

        self.message_post(
            body=_("Sales Order %s created for customer %s - Amount: %s %s") %
                 (sale_order.name, self.customer_id.name, self.customer_price, self.currency_id.name)
        )

        return sale_order

    def _create_purchase_order(self):
        """Create purchase order for vendor bill"""
        self.ensure_one()

        # Get or create service product
        service_product = self.env['product.product'].search([
            ('name', '=', 'Hotel Booking Service'),
            ('type', '=', 'service')
        ], limit=1)

        if not service_product:
            service_product = self.env['product.product'].create({
                'name': 'Hotel Booking Service',
                'type': 'service',
                'categ_id': self.env.ref('product.product_category_all').id,
                'sale_ok': False,
                'purchase_ok': True,
                'standard_price': 0.0,
            })

        # Create order line description
        description = f"Hotel Booking - {self.booking_summary}"
        if self.duration_nights > 0:
            description += f" for {self.duration_nights} nights"
        if self.number_of_pax > 1:
            description += f" for {self.number_of_pax} guests"
        if self.room_type:
            description += f" ({self.room_type})"
        if self.board:
            description += f" - {dict(self._fields['board'].selection)[self.board]}"

        order_lines = [(0, 0, {
            'product_id': service_product.id,
            'name': description,
            'product_qty': 1,
            'price_unit': self.supplier_cost,
            'product_uom': service_product.uom_po_id.id,
            'date_planned': self.check_in_date or fields.Datetime.now(),
        })]

        po_vals = {
            'partner_id': self.supplier_id.id,
            'origin': self.name,
            'order_line': order_lines,
            'currency_id': self.currency_id.id,
        }

        purchase_order = self.env['purchase.order'].create(po_vals)
        self.purchase_order_id = purchase_order.id

        self.message_post(
            body=_("Purchase Order %s created for supplier %s - Amount: %s %s") %
                 (purchase_order.name, self.supplier_id.name, self.supplier_cost, self.currency_id.name)
        )

        return purchase_order

    def action_view_sale_order(self):
        """Open the related sales order"""
        self.ensure_one()
        if not self.sale_order_id:
            return

        return {
            'type': 'ir.actions.act_window',
            'name': _('Sales Order'),
            'view_mode': 'form',
            'res_model': 'sale.order',
            'res_id': self.sale_order_id.id,
            'target': 'current',
        }

    def action_view_purchase_order(self):
        """Open the related purchase order"""
        self.ensure_one()
        if not self.purchase_order_id:
            return

        return {
            'type': 'ir.actions.act_window',
            'name': _('Purchase Order'),
            'view_mode': 'form',
            'res_model': 'purchase.order',
            'res_id': self.purchase_order_id.id,
            'target': 'current',
        }

    def action_duplicate_request(self):
        """Duplicate hotel booking request with new reference"""
        self.ensure_one()
        new_request = self.copy({
            'name': _('New'),
            'state': 'draft',
        })
        return {
            'type': 'ir.actions.act_window',
            'name': _('Hotel Booking Request'),
            'res_model': 'hotel.booking.request',
            'res_id': new_request.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def _get_portal_return_action(self):
        """Return the action to redirect to after portal form submission"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_url',
            'url': '/my/hotel_bookings/%s' % self.id,
            'target': 'self',
        }
