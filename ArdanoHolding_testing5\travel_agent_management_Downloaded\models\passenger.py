# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class TravelPassenger(models.Model):
    _name = 'travel.passenger'
    _description = 'Travel Passenger'
    _order = 'name'

    # Basic Information
    name = fields.Char(
        string='Full Name',
        required=True,
        help="Passenger full name as it appears on passport"
    )
    
    customer_id = fields.Many2one(
        'res.partner',
        string='Customer Company',
        domain=[('is_company', '=', True)],
        help="Company this passenger belongs to"
    )
    
    contact_id = fields.Many2one(
        'res.partner',
        string='Contact Person',
        domain=[('is_company', '=', False)],
        help="Contact person for this passenger (for portal access)"
    )
    
    employee_id = fields.Char(
        string='Employee ID',
        help="Employee ID in customer company"
    )
    

    
    # Passport Information
    nationality_id = fields.Many2one(
        'res.country',
        string='Nationality',
        required=True
    )
    
    passport_number = fields.Char(
        string='Passport Number',
        required=True
    )
    
    passport_expiration = fields.Date(
        string='Passport Expiration',
        required=True
    )
    
    passport_issue_date = fields.Date(
        string='Passport Issue Date'
    )
    
    passport_issue_place = fields.Char(
        string='Passport Issue Place'
    )
    
    # Personal Information
    date_of_birth = fields.Date(
        string='Date of Birth'
    )
    
    gender = fields.Selection([
        ('male', 'Male'),
        ('female', 'Female'),
        ('other', 'Other')
    ], string='Gender')
    
    # Contact Information
    email = fields.Char(
        string='Email',
        help="Passenger email for communication"
    )
    
    phone = fields.Char(
        string='Phone',
        help="Passenger phone number"
    )
    
    # Travel Preferences
    seat_preference = fields.Selection([
        ('window', 'Window'),
        ('aisle', 'Aisle'),
        ('no_preference', 'No Preference')
    ], string='Seat Preference', default='no_preference')
    
    meal_preference = fields.Selection([
        ('regular', 'Regular'),
        ('vegetarian', 'Vegetarian'),
        ('vegan', 'Vegan'),
        ('halal', 'Halal'),
        ('kosher', 'Kosher'),
        ('special', 'Special Request')
    ], string='Meal Preference', default='regular')
    
    special_requirements = fields.Text(
        string='Special Requirements',
        help="Special needs, medical requirements, etc."
    )
    
    # Travel History
    passenger_line_ids = fields.One2many(
        'travel.passenger.line',
        'passenger_id',
        string='Travel Lines'
    )
    
    travel_request_ids = fields.Many2many(
        'travel.ticket.request',
        compute='_compute_travel_requests',
        string='Travel Requests'
    )
    
    travel_count = fields.Integer(
        string='Number of Travels',
        compute='_compute_travel_count',
        store=True
    )
    
    last_travel_date = fields.Date(
        string='Last Travel Date',
        compute='_compute_last_travel_date',
        store=True
    )
    
    # Document Management
    passport_attachment_ids = fields.Many2many(
        'ir.attachment',
        'passenger_passport_attachment_rel',
        'passenger_id',
        'attachment_id',
        string='Passport Documents'
    )
    
    visa_attachment_ids = fields.Many2many(
        'ir.attachment',
        'passenger_visa_attachment_rel',
        'passenger_id',
        'attachment_id',
        string='Visa Documents'
    )
    
    other_attachment_ids = fields.Many2many(
        'ir.attachment',
        'passenger_other_attachment_rel',
        'passenger_id',
        'attachment_id',
        string='Other Documents'
    )
    
    # Status and Warnings
    passport_status = fields.Selection([
        ('valid', 'Valid'),
        ('expiring', 'Expiring Soon'),
        ('expired', 'Expired')
    ], string='Passport Status', compute='_compute_passport_status', store=True)
    
    days_until_passport_expiry = fields.Integer(
        string='Days Until Passport Expiry',
        compute='_compute_passport_status',
        store=True
    )
    
    # Computed Methods
    @api.depends('passenger_line_ids')
    def _compute_travel_requests(self):
        for passenger in self:
            passenger.travel_request_ids = passenger.passenger_line_ids.mapped('request_id')
    
    @api.depends('passenger_line_ids.request_id.state')
    def _compute_travel_count(self):
        for passenger in self:
            completed_requests = passenger.passenger_line_ids.mapped('request_id').filtered(
                lambda r: r.state in ['delivered', 'invoiced', 'done']
            )
            passenger.travel_count = len(completed_requests)
    
    @api.depends('passenger_line_ids.departure_date', 'passenger_line_ids.request_id.state')
    def _compute_last_travel_date(self):
        for passenger in self:
            completed_lines = passenger.passenger_line_ids.filtered(
                lambda line: line.request_id.state in ['delivered', 'invoiced', 'done'] and line.departure_date
            ).sorted('departure_date', reverse=True)
            
            if completed_lines:
                passenger.last_travel_date = completed_lines[0].departure_date.date()
            else:
                passenger.last_travel_date = False
    
    @api.depends('passport_expiration')
    def _compute_passport_status(self):
        today = fields.Date.context_today(self)
        
        for passenger in self:
            if passenger.passport_expiration:
                days_diff = (passenger.passport_expiration - today).days
                passenger.days_until_passport_expiry = days_diff
                
                if days_diff < 0:
                    passenger.passport_status = 'expired'
                elif days_diff <= 180:  # 6 months warning
                    passenger.passport_status = 'expiring'
                else:
                    passenger.passport_status = 'valid'
            else:
                passenger.passport_status = 'expired'
                passenger.days_until_passport_expiry = 0
    
    # Constraints and Validations
    @api.constrains('passport_expiration', 'passport_issue_date')
    def _check_passport_dates(self):
        for passenger in self:
            if passenger.passport_issue_date and passenger.passport_expiration:
                if passenger.passport_issue_date >= passenger.passport_expiration:
                    raise ValidationError(_("Passport issue date must be before expiration date."))
    
    @api.constrains('date_of_birth')
    def _check_date_of_birth(self):
        today = fields.Date.context_today(self)
        for passenger in self:
            if passenger.date_of_birth:
                if passenger.date_of_birth >= today:
                    raise ValidationError(_("Date of birth cannot be in the future."))
                
                # Check reasonable age limits
                age = (today - passenger.date_of_birth).days / 365.25
                if age > 120:
                    raise ValidationError(_("Please check the date of birth. Age seems unrealistic."))
    
    @api.constrains('email')
    def _check_email(self):
        for passenger in self:
            if passenger.email:
                # Basic email validation
                import re
                if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', passenger.email):
                    raise ValidationError(_("Please enter a valid email address."))
    
    # Name Methods
    def name_get(self):
        result = []
        for passenger in self:
            name = passenger.name
            if passenger.passport_number:
                name += f" ({passenger.passport_number})"
            if passenger.customer_id:
                name += f" - {passenger.customer_id.name}"
            result.append((passenger.id, name))
        return result
    
    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        domain = []
        if name:
            domain = ['|', '|', 
                     ('name', operator, name),
                     ('passport_number', operator, name),
                     ('employee_id', operator, name)]
        return self._search(domain + args, limit=limit, access_rights_uid=name_get_uid)
    
    @api.model
    def name_create(self, name):
        """Enable quick creation of passengers with just a name"""
        # Set default values for required fields to allow creation
        vals = {
            'name': name,
            'passport_number': 'TEMP-' + str(int(datetime.now().timestamp())),  # Temporary passport number
            'nationality_id': self.env.ref('base.us').id,  # Default to US, user can change
            'passport_expiration': fields.Date.today() + timedelta(days=365),  # Default 1 year from now
        }
        
        # If customer context is provided, use it
        if self.env.context.get('default_customer_id'):
            vals['customer_id'] = self.env.context.get('default_customer_id')
        
        passenger = self.create(vals)
        return passenger.name_get()[0]
    
    # Action Methods
    def action_view_travel_history(self):
        """Open travel history for this passenger"""
        request_ids = self.passenger_line_ids.mapped('request_id').ids
        return {
            'name': _('Travel History'),
            'type': 'ir.actions.act_window',
            'res_model': 'travel.ticket.request',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', request_ids)],
            'context': {}
        }
    
    def action_check_passport_validity(self):
        """Check passport validity for upcoming travels"""
        upcoming_travels = self.passenger_line_ids.filtered(
            lambda line: line.request_id.state in ['draft', 'research', 'quotation', 'booking'] and 
                        line.departure_date and line.departure_date.date() > fields.Date.context_today(self)
        )
        
        warnings = []
        
        # Check passport expiry
        if self.passport_status == 'expired':
            warnings.append(_("Passport is expired!"))
        elif self.passport_status == 'expiring':
            warnings.append(_("Passport expires in %d days") % self.days_until_passport_expiry)
        
        # Check if passport is valid for upcoming travels (6 months rule)
        for travel_line in upcoming_travels:
            departure_date = travel_line.departure_date.date()
            if self.passport_expiration:
                days_between = (self.passport_expiration - departure_date).days
                if days_between < 180:  # Many countries require 6 months validity
                    warnings.append(_(
                        "Passport may not be valid for travel on %s to %s (less than 6 months validity)"
                    ) % (departure_date.strftime('%Y-%m-%d'), travel_line.departure_route))
        
        if warnings:
            message = "\n".join(warnings)
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Passport Warning'),
                    'message': message,
                    'type': 'warning',
                    'sticky': True,
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Passport Status'),
                    'message': _('Passport is valid for all upcoming travels'),
                    'type': 'success',
                }
            }

 