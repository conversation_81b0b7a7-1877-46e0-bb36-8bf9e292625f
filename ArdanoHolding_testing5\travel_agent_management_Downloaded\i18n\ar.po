# Arabic translation for travel_agent_management module.
# This file contains translations for the Travel Agent Management System.

msgid ""
msgstr ""
"Project-Id-Version: Travel Agent Management 16.0.1.0.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"PO-Revision-Date: 2024-01-01 12:00+0000\n"
"Last-Translator: Ardano Holdings\n"
"Language-Team: Arabic\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: travel_agent_management
msgid "New"
msgstr "جديد"

#. module: travel_agent_management
msgid "Request Number"
msgstr "رقم الطلب"

#. module: travel_agent_management
msgid "Customer"
msgstr "العميل"

#. module: travel_agent_management
msgid "Customer company requesting the travel service"
msgstr "شركة العميل طالبة خدمة السفر"

#. module: travel_agent_management
msgid "Request Date"
msgstr "تاريخ الطلب"

#. module: travel_agent_management
msgid "Date when the request was manually entered into the system"
msgstr "التاريخ الذي تم فيه إدخال الطلب يدوياً في النظام"

#. module: travel_agent_management
msgid "Assigned To"
msgstr "مخصص إلى"

#. module: travel_agent_management
msgid "Team member responsible for processing this request"
msgstr "عضو الفريق المسؤول عن معالجة هذا الطلب"

#. module: travel_agent_management
msgid "Passengers"
msgstr "المسافرون"

#. module: travel_agent_management
msgid "All passengers for this travel request"
msgstr "جميع المسافرين لطلب السفر هذا"

#. module: travel_agent_management
msgid "Number of Passengers"
msgstr "عدد المسافرين"

#. module: travel_agent_management
msgid "Additional Services"
msgstr "خدمات إضافية"

#. module: travel_agent_management
msgid "Hotel, transportation, insurance, or other services needed for all passengers"
msgstr "الفندق والنقل والتأمين أو الخدمات الأخرى المطلوبة لجميع المسافرين"

#. module: travel_agent_management
msgid "Status"
msgstr "الحالة"

#. module: travel_agent_management
msgid "New Request"
msgstr "طلب جديد"

#. module: travel_agent_management
msgid "Vendor Research"
msgstr "البحث عن المورد"

#. module: travel_agent_management
msgid "Pricing Ready"
msgstr "التسعير جاهز"

#. module: travel_agent_management
msgid "Booking Confirmed"
msgstr "تأكيد الحجز"

#. module: travel_agent_management
msgid "Ticket Delivered"
msgstr "تم تسليم التذكرة"

#. module: travel_agent_management
msgid "Customer Invoiced"
msgstr "تم إصدار فاتورة للعميل"

#. module: travel_agent_management
msgid "Completed"
msgstr "مكتمل"

#. module: travel_agent_management
msgid "Cancelled"
msgstr "ملغي"

#. module: travel_agent_management
msgid "Selected Vendor"
msgstr "المورد المختار"

#. module: travel_agent_management
msgid "Total Vendor Cost"
msgstr "إجمالي تكلفة المورد"

#. module: travel_agent_management
msgid "Total cost from the vendor for all passengers"
msgstr "إجمالي التكلفة من المورد لجميع المسافرين"

#. module: travel_agent_management
msgid "Cost Currency"
msgstr "عملة التكلفة"

#. module: travel_agent_management
msgid "Total Customer Price"
msgstr "إجمالي سعر العميل"

#. module: travel_agent_management
msgid "Total price charged to customer for all passengers"
msgstr "إجمالي السعر المفروض على العميل لجميع المسافرين"

#. module: travel_agent_management
msgid "Price Currency"
msgstr "عملة السعر"

#. module: travel_agent_management
msgid "Total Margin"
msgstr "إجمالي الهامش"

#. module: travel_agent_management
msgid "Margin %"
msgstr "نسبة الهامش %"

#. module: travel_agent_management
msgid "Sales Order"
msgstr "أمر البيع"

#. module: travel_agent_management
msgid "Customer Sales Order generated from this request"
msgstr "أمر بيع العميل المُنشأ من هذا الطلب"

#. module: travel_agent_management
msgid "Purchase Order"
msgstr "أمر الشراء"

#. module: travel_agent_management
msgid "Vendor Purchase Order for this request"
msgstr "أمر شراء المورد لهذا الطلب"

#. module: travel_agent_management
msgid "Internal Notes"
msgstr "ملاحظات داخلية"

#. module: travel_agent_management
msgid "Internal team notes and comments"
msgstr "ملاحظات وتعليقات الفريق الداخلي"

#. module: travel_agent_management
msgid "Customer Notes"
msgstr "ملاحظات العميل"

#. module: travel_agent_management
msgid "Notes from customer communication"
msgstr "ملاحظات من التواصل مع العميل"

#. module: travel_agent_management
msgid "At least one passenger is required."
msgstr "مطلوب مسافر واحد على الأقل."

#. module: travel_agent_management
msgid "Vendor research started."
msgstr "بدأ البحث عن المورد."

#. module: travel_agent_management
msgid "Pricing and quotation ready."
msgstr "التسعير والعرض جاهز."

#. module: travel_agent_management
msgid "Travel Request"
msgstr "طلب السفر"

#. module: travel_agent_management
msgid "Passenger"
msgstr "المسافر"

#. module: travel_agent_management
msgid "Passport Number"
msgstr "رقم جواز السفر"

#. module: travel_agent_management
msgid "Nationality"
msgstr "الجنسية"

#. module: travel_agent_management
msgid "Travel Purpose"
msgstr "الغرض من السفر"

#. module: travel_agent_management
msgid "Business"
msgstr "عمل"

#. module: travel_agent_management
msgid "Personal"
msgstr "شخصي"

#. module: travel_agent_management
msgid "Emergency"
msgstr "طوارئ"

#. module: travel_agent_management
msgid "Medical"
msgstr "طبي"

#. module: travel_agent_management
msgid "Training"
msgstr "تدريب"

#. module: travel_agent_management
msgid "Conference"
msgstr "مؤتمر"

#. module: travel_agent_management
msgid "Other"
msgstr "آخر"

#. module: travel_agent_management
msgid "Departure Route"
msgstr "مسار المغادرة"

#. module: travel_agent_management
msgid "From - To (e.g., New York - London)"
msgstr "من - إلى (مثال، نيويورك - لندن)"

#. module: travel_agent_management
msgid "Departure Date"
msgstr "تاريخ المغادرة"

#. module: travel_agent_management
msgid "Departure date and time for this passenger"
msgstr "تاريخ ووقت المغادرة لهذا المسافر"

#. module: travel_agent_management
msgid "Return Route"
msgstr "مسار العودة"

#. module: travel_agent_management
msgid "From - To for return journey (e.g., London - New York)"
msgstr "من - إلى لرحلة العودة (مثال، لندن - نيويورك)"

#. module: travel_agent_management
msgid "Return Date"
msgstr "تاريخ العودة"

#. module: travel_agent_management
msgid "Return date and time for this passenger (leave empty for one-way)"
msgstr "تاريخ ووقت العودة لهذا المسافر (اتركه فارغاً للرحلة أحادية الاتجاه)"

#. module: travel_agent_management
msgid "Ticket Class"
msgstr "درجة التذكرة"

#. module: travel_agent_management
msgid "Economy"
msgstr "اقتصادية"

#. module: travel_agent_management
msgid "Premium Economy"
msgstr "اقتصادية مميزة"

#. module: travel_agent_management
msgid "Business"
msgstr "رجال أعمال"

#. module: travel_agent_management
msgid "First Class"
msgstr "درجة أولى"

#. module: travel_agent_management
msgid "Seat Preference"
msgstr "تفضيل المقعد"

#. module: travel_agent_management
msgid "Aisle"
msgstr "الممر"

#. module: travel_agent_management
msgid "Window"
msgstr "النافذة"

#. module: travel_agent_management
msgid "Middle"
msgstr "الوسط"

#. module: travel_agent_management
msgid "No Preference"
msgstr "لا تفضيل"

#. module: travel_agent_management
msgid "Meal Preference"
msgstr "تفضيل الوجبة"

#. module: travel_agent_management
msgid "Regular"
msgstr "عادية"

#. module: travel_agent_management
msgid "Vegetarian"
msgstr "نباتية"

#. module: travel_agent_management
msgid "Vegan"
msgstr "نباتية صرفة"

#. module: travel_agent_management
msgid "Halal"
msgstr "حلال"

#. module: travel_agent_management
msgid "Kosher"
msgstr "كوشر"

#. module: travel_agent_management
msgid "Gluten Free"
msgstr "خالية من الجلوتين"

#. module: travel_agent_management
msgid "Diabetic"
msgstr "لمرضى السكري"

#. module: travel_agent_management
msgid "No Meal"
msgstr "بدون وجبة"

#. module: travel_agent_management
msgid "Special Requirements"
msgstr "متطلبات خاصة"

#. module: travel_agent_management
msgid "Wheelchair assistance, medical needs, etc."
msgstr "مساعدة الكرسي المتحرك، الاحتياجات الطبية، إلخ."

#. module: travel_agent_management
msgid "Vendor Cost"
msgstr "تكلفة المورد"

#. module: travel_agent_management
msgid "Cost from vendor for this passenger"
msgstr "التكلفة من المورد لهذا المسافر"

#. module: travel_agent_management
msgid "Customer Price"
msgstr "سعر العميل"

#. module: travel_agent_management
msgid "Price charged to customer for this passenger"
msgstr "السعر المفروض على العميل لهذا المسافر"

#. module: travel_agent_management
msgid "Margin"
msgstr "الهامش"

#. module: travel_agent_management
msgid "Ticket Number"
msgstr "رقم التذكرة"

#. module: travel_agent_management
msgid "Seat Number"
msgstr "رقم المقعد"

#. module: travel_agent_management
msgid "Days Until Departure"
msgstr "أيام حتى المغادرة"

#. module: travel_agent_management
msgid "Full Name"
msgstr "الاسم الكامل"

#. module: travel_agent_management
msgid "Passenger full name as it appears on passport"
msgstr "الاسم الكامل للمسافر كما يظهر في جواز السفر"

#. module: travel_agent_management
msgid "Customer Company"
msgstr "شركة العميل"

#. module: travel_agent_management
msgid "Company this passenger belongs to"
msgstr "الشركة التي ينتمي إليها هذا المسافر"

#. module: travel_agent_management
msgid "Employee ID"
msgstr "رقم الموظف"

#. module: travel_agent_management
msgid "Employee ID in customer company"
msgstr "رقم الموظف في شركة العميل"

#. module: travel_agent_management
msgid "Passport Expiration"
msgstr "انتهاء صلاحية جواز السفر"

#. module: travel_agent_management
msgid "Passport Issue Date"
msgstr "تاريخ إصدار جواز السفر"

#. module: travel_agent_management
msgid "Passport Issue Place"
msgstr "مكان إصدار جواز السفر"

#. module: travel_agent_management
msgid "Date of Birth"
msgstr "تاريخ الميلاد"

#. module: travel_agent_management
msgid "Gender"
msgstr "الجنس"

#. module: travel_agent_management
msgid "Male"
msgstr "ذكر"

#. module: travel_agent_management
msgid "Female"
msgstr "أنثى"

#. module: travel_agent_management
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: travel_agent_management
msgid "Passenger email for communication"
msgstr "بريد المسافر الإلكتروني للتواصل"

#. module: travel_agent_management
msgid "Phone"
msgstr "الهاتف"

#. module: travel_agent_management
msgid "Passenger phone number"
msgstr "رقم هاتف المسافر"

#. module: travel_agent_management
msgid "Special needs, medical requirements, etc."
msgstr "الاحتياجات الخاصة، المتطلبات الطبية، إلخ."

#. module: travel_agent_management
msgid "Travel Lines"
msgstr "خطوط السفر"

#. module: travel_agent_management
msgid "Travel Requests"
msgstr "طلبات السفر"

#. module: travel_agent_management
msgid "Number of Travels"
msgstr "عدد الرحلات"

#. module: travel_agent_management
msgid "Last Travel Date"
msgstr "تاريخ آخر سفر"

#. module: travel_agent_management
msgid "Passport Documents"
msgstr "مستندات جواز السفر"

#. module: travel_agent_management
msgid "Visa Documents"
msgstr "مستندات التأشيرة"

#. module: travel_agent_management
msgid "Other Documents"
msgstr "مستندات أخرى"

#. module: travel_agent_management
msgid "Passport Status"
msgstr "حالة جواز السفر"

#. module: travel_agent_management
msgid "Valid"
msgstr "صالح"

#. module: travel_agent_management
msgid "Expiring Soon"
msgstr "ينتهي قريباً"

#. module: travel_agent_management
msgid "Expired"
msgstr "منتهي الصلاحية"

#. module: travel_agent_management
msgid "Days Until Passport Expiry"
msgstr "أيام حتى انتهاء صلاحية جواز السفر"

#. module: travel_agent_management
msgid "Travel Agent"
msgstr "وكيل السفر"

#. module: travel_agent_management
msgid "Operations"
msgstr "العمليات"

#. module: travel_agent_management
msgid "Configuration"
msgstr "الإعدادات"

#. module: travel_agent_management
msgid "Reports"
msgstr "التقارير"

#. module: travel_agent_management
msgid "Travel Agent Management"
msgstr "إدارة وكيل السفر"

#. module: travel_agent_management
msgid "Travel Ticket Request"
msgstr "طلب تذكرة السفر"

#. module: travel_agent_management
msgid "Travel Passenger"
msgstr "مسافر السفر"

#. module: travel_agent_management
msgid "Travel Request Passenger Line"
msgstr "خط مسافر طلب السفر"

#. module: travel_agent_management
msgid "Sequence"
msgstr "التسلسل"

#. module: travel_agent_management
msgid "Start Research"
msgstr "بدء البحث"

#. module: travel_agent_management
msgid "Quotation Ready"
msgstr "العرض جاهز"

#. module: travel_agent_management
msgid "Confirm Booking"
msgstr "تأكيد الحجز"

#. module: travel_agent_management
msgid "Deliver Ticket"
msgstr "تسليم التذكرة"

#. module: travel_agent_management
msgid "Invoice Customer"
msgstr "إصدار فاتورة للعميل"

#. module: travel_agent_management
msgid "Complete"
msgstr "إكمال"

#. module: travel_agent_management
msgid "Cancel"
msgstr "إلغاء"

#. module: travel_agent_management
msgid "Total Customer Price"
msgstr "إجمالي سعر العميل"

#. module: travel_agent_management
msgid "Total Vendor Cost"
msgstr "إجمالي تكلفة المورد"

#. module: travel_agent_management
msgid "Total Margin"
msgstr "إجمالي الهامش"

#. module: travel_agent_management
msgid "Select Customer..."
msgstr "اختر العميل..."

#. module: travel_agent_management
msgid "passenger(s)"
msgstr "مسافر(ين)"

#. module: travel_agent_management
msgid "Select Vendor..."
msgstr "اختر المورد..."

#. module: travel_agent_management
msgid "Vendor Information"
msgstr "معلومات المورد"

#. module: travel_agent_management
msgid "Customer Pricing"
msgstr "تسعير العميل"

#. module: travel_agent_management
msgid "Passenger Information"
msgstr "معلومات المسافر"

#. module: travel_agent_management
msgid "Travel Details"
msgstr "تفاصيل السفر"

#. module: travel_agent_management
msgid "Ticket Preferences"
msgstr "تفضيلات التذكرة"

#. module: travel_agent_management
msgid "Ticket Information"
msgstr "معلومات التذكرة"

#. module: travel_agent_management
msgid "Pricing"
msgstr "التسعير"

#. module: travel_agent_management
msgid "Special Requirements"
msgstr "المتطلبات الخاصة"

#. module: travel_agent_management
msgid "Wheelchair assistance, medical needs, dietary restrictions, etc."
msgstr "مساعدة الكرسي المتحرك، الاحتياجات الطبية، القيود الغذائية، إلخ."

#. module: travel_agent_management
msgid "Hotel bookings, transportation, insurance, special assistance, etc."
msgstr "حجوزات الفنادق، النقل، التأمين، المساعدة الخاصة، إلخ."

#. module: travel_agent_management
msgid "Internal team notes and processing information"
msgstr "ملاحظات الفريق الداخلي ومعلومات المعالجة"

#. module: travel_agent_management
msgid "Notes from customer communication"
msgstr "ملاحظات من التواصل مع العميل"

#. module: travel_agent_management
msgid "e.g. New York (JFK) → London (LHR)"
msgstr "مثال: نيويورك (JFK) ← لندن (LHR)"

#. module: travel_agent_management
msgid "e.g. London (LHR) → New York (JFK)"
msgstr "مثال: لندن (LHR) ← نيويورك (JFK)"

#. module: travel_agent_management
msgid "Ticket #"
msgstr "رقم التذكرة"

#. module: travel_agent_management
msgid "e.g., New York - London"
msgstr "مثال: نيويورك - لندن"

#. module: travel_agent_management
msgid "e.g., London - New York (optional)"
msgstr "مثال: لندن - نيويورك (اختياري)"

#. module: travel_agent_management
msgid "draft"
msgstr "مسودة"

#. module: travel_agent_management
msgid "research"
msgstr "بحث"

#. module: travel_agent_management
msgid "quotation"
msgstr "عرض سعر"

#. module: travel_agent_management
msgid "booking"
msgstr "حجز"

#. module: travel_agent_management
msgid "delivered"
msgstr "تم التسليم"

#. module: travel_agent_management
msgid "invoiced"
msgstr "تم إصدار الفاتورة"

#. module: travel_agent_management
msgid "done"
msgstr "منجز"

#. module: travel_agent_management
msgid "cancelled"
msgstr "ملغي"

#. module: travel_agent_management
msgid "business"
msgstr "عمل"

#. module: travel_agent_management
msgid "personal"
msgstr "شخصي"

#. module: travel_agent_management
msgid "emergency"
msgstr "طوارئ"

#. module: travel_agent_management
msgid "medical"
msgstr "طبي"

#. module: travel_agent_management
msgid "training"
msgstr "تدريب"

#. module: travel_agent_management
msgid "conference"
msgstr "مؤتمر"

#. module: travel_agent_management
msgid "other"
msgstr "آخر"

#. module: travel_agent_management
msgid "economy"
msgstr "اقتصادية"

#. module: travel_agent_management
msgid "premium_economy"
msgstr "اقتصادية مميزة"

#. module: travel_agent_management
msgid "first"
msgstr "أولى"

#. module: travel_agent_management
msgid "aisle"
msgstr "ممر"

#. module: travel_agent_management
msgid "window"
msgstr "نافذة"

#. module: travel_agent_management
msgid "middle"
msgstr "وسط"

#. module: travel_agent_management
msgid "no_preference"
msgstr "لا تفضيل"

#. module: travel_agent_management
msgid "regular"
msgstr "عادية"

#. module: travel_agent_management
msgid "vegetarian"
msgstr "نباتية"

#. module: travel_agent_management
msgid "vegan"
msgstr "نباتية صرفة"

#. module: travel_agent_management
msgid "halal"
msgstr "حلال"

#. module: travel_agent_management
msgid "kosher"
msgstr "كوشر"

#. module: travel_agent_management
msgid "gluten_free"
msgstr "خالية من الجلوتين"

#. module: travel_agent_management
msgid "diabetic"
msgstr "لمرضى السكري"

#. module: travel_agent_management
msgid "no_meal"
msgstr "بدون وجبة"

#. module: travel_agent_management
msgid "male"
msgstr "ذكر"

#. module: travel_agent_management
msgid "female"
msgstr "أنثى"

#. module: travel_agent_management
msgid "valid"
msgstr "صالح"

#. module: travel_agent_management
msgid "expiring"
msgstr "ينتهي قريباً"

#. module: travel_agent_management
msgid "expired"
msgstr "منتهي الصلاحية"

#. module: travel_agent_management
msgid "booked"
msgstr "محجوز"

#. module: travel_agent_management
msgid "ticketed"
msgstr "تم إصدار التذكرة"

#. module: travel_agent_management
msgid "Request Number"
msgstr "رقم الطلب"

#. module: travel_agent_management
msgid "Passport issue date must be before expiration date."
msgstr "يجب أن يكون تاريخ إصدار جواز السفر قبل تاريخ انتهاء الصلاحية."

#. module: travel_agent_management
msgid "Date of birth cannot be in the future."
msgstr "لا يمكن أن يكون تاريخ الميلاد في المستقبل."

#. module: travel_agent_management
msgid "Please check the date of birth. Age seems unrealistic."
msgstr "يرجى التحقق من تاريخ الميلاد. العمر يبدو غير واقعي."

#. module: travel_agent_management
msgid "Please enter a valid email address."
msgstr "يرجى إدخال عنوان بريد إلكتروني صحيح."

#. module: travel_agent_management
msgid "Please select vendor and ensure all passenger costs are set."
msgstr "يرجى اختيار المورد والتأكد من تحديد تكاليف جميع المسافرين."

#. module: travel_agent_management
msgid "Please set customer prices for all passengers."
msgstr "يرجى تحديد أسعار العملاء لجميع المسافرين."

#. module: travel_agent_management
msgid "Booking confirmed. Purchase order created."
msgstr "تم تأكيد الحجز. تم إنشاء أمر الشراء."

#. module: travel_agent_management
msgid "Tickets delivered. Sales order created."
msgstr "تم تسليم التذاكر. تم إنشاء أمر البيع."

#. module: travel_agent_management
msgid "Customer invoiced."
msgstr "تم إصدار فاتورة للعميل."

#. module: travel_agent_management
msgid "Request completed."
msgstr "تم إكمال الطلب."

#. module: travel_agent_management
msgid "Request cancelled."
msgstr "تم إلغاء الطلب."

#. module: travel_agent_management
msgid "Travel History"
msgstr "تاريخ السفر"

#. module: travel_agent_management
msgid "Passport is expired!"
msgstr "جواز السفر منتهي الصلاحية!"

#. module: travel_agent_management
msgid "Passport expires in %d days"
msgstr "ينتهي جواز السفر خلال %d أيام"

#. module: travel_agent_management
msgid "Passport may not be valid for travel on %s to %s (less than 6 months validity)"
msgstr "قد لا يكون جواز السفر صالحاً للسفر في %s إلى %s (أقل من 6 أشهر صلاحية)"

#. module: travel_agent_management
msgid "Passport Warning"
msgstr "تحذير جواز السفر"

#. module: travel_agent_management
msgid "Passport Status"
msgstr "حالة جواز السفر"

#. module: travel_agent_management
msgid "Passport is valid for all upcoming travels"
msgstr "جواز السفر صالح لجميع الرحلات القادمة"

#. module: travel_agent_management
msgid "Check Passport"
msgstr "فحص جواز السفر"

#. module: travel_agent_management
msgid "Travels"
msgstr "الرحلات"

#. module: travel_agent_management
msgid "Basic Information"
msgstr "المعلومات الأساسية"

#. module: travel_agent_management
msgid "Contact Information"
msgstr "معلومات الاتصال"

#. module: travel_agent_management
msgid "Passport Information"
msgstr "معلومات جواز السفر"

#. module: travel_agent_management
msgid "Travel Preferences"
msgstr "تفضيلات السفر"

#. module: travel_agent_management
msgid "Medical needs, accessibility requirements, etc."
msgstr "الاحتياجات الطبية، متطلبات إمكانية الوصول، إلخ."

#. module: travel_agent_management
msgid "Passport Scans"
msgstr "صور جواز السفر"

#. module: travel_agent_management
msgid "Visa Scans"
msgstr "صور التأشيرة"

#. module: travel_agent_management
msgid "Special Request"
msgstr "طلب خاص"

#. module: travel_agent_management
msgid "Access rights for Travel Agent Management system"
msgstr "حقوق الوصول لنظام إدارة وكيل السفر"

#. module: travel_agent_management
msgid "Travel Agent User"
msgstr "مستخدم وكيل السفر"

#. module: travel_agent_management
msgid "Travel Agent Manager"
msgstr "مدير وكيل السفر"

#. module: travel_agent_management
msgid "Travel Agent Admin"
msgstr "مشرف وكيل السفر"

#. module: travel_agent_management
msgid "Travel Agent Accounting"
msgstr "محاسبة وكيل السفر" 