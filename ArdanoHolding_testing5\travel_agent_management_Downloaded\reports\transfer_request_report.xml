<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Transfer Request Report Template -->
        <record id="report_transfer_request" model="ir.actions.report">
            <field name="name">Transfer Request</field>
            <field name="model">transfer.request</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">travel_agent_management.report_transfer_request_document</field>
            <field name="report_file">travel_agent_management.report_transfer_request_document</field>
            <field name="binding_model_id" ref="model_transfer_request"/>
            <field name="binding_type">report</field>
        </record>

        <!-- Transfer Request Report Template -->
        <template id="report_transfer_request_document">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.external_layout">
                        <div class="page">
                            <div class="oe_structure"/>
                            
                            <!-- Header -->
                            <div class="row">
                                <div class="col-12">
                                    <h2 class="text-center">
                                        <strong>Transfer Request</strong>
                                    </h2>
                                    <h3 class="text-center">
                                        <span t-field="doc.name"/>
                                    </h3>
                                </div>
                            </div>
                            
                            <br/>
                            
                            <!-- Customer and Supplier Information -->
                            <div class="row">
                                <div class="col-6">
                                    <strong>Customer Information:</strong>
                                    <div t-field="doc.customer_id" 
                                         t-options='{"widget": "contact", "fields": ["name", "address", "phone", "email"]}'/>
                                </div>
                                <div class="col-6">
                                    <strong>Supplier Information:</strong>
                                    <div t-field="doc.supplier_id" 
                                         t-options='{"widget": "contact", "fields": ["name", "address", "phone", "email"]}'/>
                                </div>
                            </div>
                            
                            <br/>
                            
                            <!-- Transfer Details -->
                            <div class="row">
                                <div class="col-12">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>From:</strong></td>
                                            <td><span t-field="doc.transfer_from"/></td>
                                            <td><strong>To:</strong></td>
                                            <td><span t-field="doc.transfer_to"/></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Departure Date:</strong></td>
                                            <td><span t-field="doc.departure_date"/></td>
                                            <td><strong>Departure Time:</strong></td>
                                            <td><span t-field="doc.departure_time" t-options='{"widget": "float_time"}'/></td>
                                        </tr>
                                        <tr t-if="doc.arrive_date">
                                            <td><strong>Arrival Date:</strong></td>
                                            <td><span t-field="doc.arrive_date"/></td>
                                            <td><strong>Arrival Time:</strong></td>
                                            <td><span t-field="doc.arrive_time" t-options='{"widget": "float_time"}'/></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Passengers:</strong></td>
                                            <td><span t-field="doc.passenger_count"/></td>
                                            <td><strong>Vehicle Type:</strong></td>
                                            <td><span t-field="doc.vehicle_type"/></td>
                                        </tr>
                                        <tr t-if="doc.flight_number">
                                            <td><strong>Flight Number:</strong></td>
                                            <td><span t-field="doc.flight_number"/></td>
                                            <td><strong>Contact Phone:</strong></td>
                                            <td><span t-field="doc.contact_phone"/></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- Special Requirements -->
                            <div class="row" t-if="doc.special_requirements">
                                <div class="col-12">
                                    <strong>Special Requirements:</strong>
                                    <p t-field="doc.special_requirements"/>
                                </div>
                            </div>
                            
                            <!-- Customer Notes -->
                            <div class="row" t-if="doc.customer_notes">
                                <div class="col-12">
                                    <strong>Customer Notes:</strong>
                                    <p t-field="doc.customer_notes"/>
                                </div>
                            </div>
                            
                            <!-- Pricing Information -->
                            <div class="row">
                                <div class="col-12">
                                    <h4>Pricing Information</h4>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Customer Price:</strong></td>
                                            <td class="text-right">
                                                <span t-field="doc.customer_price" 
                                                      t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Supplier Cost:</strong></td>
                                            <td class="text-right">
                                                <span t-field="doc.supplier_cost" 
                                                      t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Profit:</strong></td>
                                            <td class="text-right">
                                                <span t-field="doc.profit_amount" 
                                                      t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/>
                                                (<span t-field="doc.profit_percentage" t-options='{"widget": "percentage"}'/>)
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- Status -->
                            <div class="row">
                                <div class="col-12">
                                    <p><strong>Status:</strong> 
                                        <span t-field="doc.state" class="badge badge-info"/>
                                    </p>
                                </div>
                            </div>
                            
                            <div class="oe_structure"/>
                        </div>
                    </t>
                </t>
            </t>
        </template>

        <!-- Transfer Requests Summary Report -->
        <record id="action_transfer_requests_report" model="ir.actions.act_window">
            <field name="name">Transfer Requests Analysis</field>
            <field name="res_model">transfer.request</field>
            <field name="view_mode">pivot,graph</field>
            <field name="context">{
                'search_default_confirmed': 1,
                'group_by': ['departure_date:month'],
            }</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No transfer requests to analyze yet!
                </p>
                <p>
                    This report shows transfer requests analysis with profit margins.
                </p>
            </field>
        </record>

        <!-- Transfer Request Pivot View -->
        <record id="view_transfer_request_pivot" model="ir.ui.view">
            <field name="name">transfer.request.pivot</field>
            <field name="model">transfer.request</field>
            <field name="arch" type="xml">
                <pivot string="Transfer Requests Analysis">
                    <field name="departure_date" type="row" interval="month"/>
                    <field name="state" type="col"/>
                    <field name="customer_price" type="measure"/>
                    <field name="supplier_cost" type="measure"/>
                    <field name="profit_amount" type="measure"/>
                    <field name="passenger_count" type="measure"/>
                </pivot>
            </field>
        </record>

        <!-- Transfer Request Graph View -->
        <record id="view_transfer_request_graph" model="ir.ui.view">
            <field name="name">transfer.request.graph</field>
            <field name="model">transfer.request</field>
            <field name="arch" type="xml">
                <graph string="Transfer Requests Analysis" type="line">
                    <field name="departure_date" type="row" interval="month"/>
                    <field name="profit_amount" type="measure"/>
                </graph>
            </field>
        </record>

        <!-- Menu for Reports -->
        <menuitem id="menu_transfer_reports" 
                  name="Reports" 
                  parent="menu_transfer_management" 
                  sequence="90"/>
                  
        <menuitem id="menu_transfer_analysis" 
                  name="Transfer Analysis" 
                  parent="menu_transfer_reports" 
                  action="action_transfer_requests_report" 
                  sequence="10"/>

    </data>
</odoo>
