/** @odoo-module **/

import { registry } from "@web/core/registry";
import { Component, useState, onMounted } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

/**
 * Document Upload Widget
 * Enhanced file upload with drag & drop support
 */
class DocumentUploadWidget extends Component {
    setup() {
        this.notification = useService("notification");
        this.state = useState({
            isDragOver: false,
            isUploading: false,
            uploadProgress: 0
        });

        onMounted(() => {
            this.setupDragAndDrop();
        });
    }

    setupDragAndDrop() {
        const dropArea = this.el.querySelector('.file_upload_area');
        if (!dropArea) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, this.preventDefaults.bind(this), false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, this.highlight.bind(this), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, this.unhighlight.bind(this), false);
        });

        dropArea.addEventListener('drop', this.handleDrop.bind(this), false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    highlight() {
        this.state.isDragOver = true;
    }

    unhighlight() {
        this.state.isDragOver = false;
    }

    handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        this.handleFiles(files);
    }

    async handleFiles(files) {
        if (files.length === 0) return;

        this.state.isUploading = true;
        this.state.uploadProgress = 0;

        try {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                await this.uploadFile(file);
                this.state.uploadProgress = ((i + 1) / files.length) * 100;
            }

            this.notification.add("Files uploaded successfully!", {
                type: "success",
            });

            // Trigger form reload or update
            this.env.model.load();
        } catch (error) {
            this.notification.add("Error uploading files: " + error.message, {
                type: "danger",
            });
        } finally {
            this.state.isUploading = false;
            this.state.uploadProgress = 0;
        }
    }

    async uploadFile(file) {
        // Validate file size (5MB limit by default)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
            throw new Error(`File ${file.name} is too large. Maximum size is 5MB.`);
        }

        // Validate file type if needed
        const allowedTypes = ['image/*', 'application/pdf', '.doc', '.docx'];
        // Add validation logic here if needed

        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                // Here you would typically make an RPC call to upload the file
                // For now, we'll just resolve after a short delay
                setTimeout(resolve, 500);
            };
            reader.onerror = () => reject(new Error(`Failed to read file ${file.name}`));
            reader.readAsDataURL(file);
        });
    }
}

DocumentUploadWidget.template = "travel_agent_management.DocumentUploadWidget";

/**
 * Document Preview Widget
 * Enhanced preview with support for different file types
 */
class DocumentPreviewWidget extends Component {
    setup() {
        this.state = useState({
            isLoading: false,
            previewUrl: null,
            fileType: null
        });

        onMounted(() => {
            this.loadPreview();
        });
    }

    async loadPreview() {
        if (!this.props.attachmentId) return;

        this.state.isLoading = true;
        try {
            // Get file info and generate preview URL
            const attachment = await this.env.services.orm.read(
                "ir.attachment",
                [this.props.attachmentId],
                ["name", "mimetype", "datas"]
            );

            if (attachment.length > 0) {
                const file = attachment[0];
                this.state.fileType = this.getFileType(file.mimetype);
                this.state.previewUrl = `/web/content/${this.props.attachmentId}`;
            }
        } catch (error) {
            console.error("Error loading preview:", error);
        } finally {
            this.state.isLoading = false;
        }
    }

    getFileType(mimetype) {
        if (!mimetype) return 'unknown';
        
        if (mimetype.startsWith('image/')) return 'image';
        if (mimetype === 'application/pdf') return 'pdf';
        return 'document';
    }

    openFullPreview() {
        if (!this.state.previewUrl) return;

        // Open in new window for full preview
        window.open(this.state.previewUrl, '_blank');
    }

    downloadFile() {
        if (!this.state.previewUrl) return;

        // Trigger download
        const link = document.createElement('a');
        link.href = this.state.previewUrl + '?download=true';
        link.download = '';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

DocumentPreviewWidget.template = "travel_agent_management.DocumentPreviewWidget";

/**
 * Progress Bar Component
 * Animated progress bar for document completion
 */
class DocumentProgressBar extends Component {
    setup() {
        onMounted(() => {
            this.animateProgress();
        });
    }

    animateProgress() {
        const progressBar = this.el.querySelector('.progress-bar');
        if (!progressBar) return;

        const targetWidth = this.props.percentage || 0;
        progressBar.style.setProperty('--progress-width', targetWidth + '%');
        progressBar.classList.add('animated');

        // Update color based on percentage
        progressBar.classList.remove('bg-success', 'bg-warning', 'bg-danger');
        if (targetWidth >= 80) {
            progressBar.classList.add('bg-success');
        } else if (targetWidth >= 50) {
            progressBar.classList.add('bg-warning');
        } else {
            progressBar.classList.add('bg-danger');
        }
    }

    get progressText() {
        const percentage = this.props.percentage || 0;
        return `${Math.round(percentage)}%`;
    }
}

DocumentProgressBar.template = "travel_agent_management.DocumentProgressBar";

// Register components
registry.category("fields").add("document_upload", DocumentUploadWidget);
registry.category("fields").add("document_preview", DocumentPreviewWidget);
registry.category("fields").add("document_progress", DocumentProgressBar);

/**
 * Utility functions for document management
 */
export const DocumentUtils = {
    /**
     * Format file size in human readable format
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Get file type icon class
     */
    getFileTypeIcon(mimetype) {
        if (!mimetype) return 'fa-file-o';
        
        if (mimetype.startsWith('image/')) return 'fa-file-image-o';
        if (mimetype === 'application/pdf') return 'fa-file-pdf-o';
        if (mimetype.includes('word')) return 'fa-file-word-o';
        if (mimetype.includes('excel') || mimetype.includes('spreadsheet')) return 'fa-file-excel-o';
        
        return 'fa-file-o';
    },

    /**
     * Validate file before upload
     */
    validateFile(file, maxSize = 5 * 1024 * 1024, allowedTypes = []) {
        const errors = [];

        if (file.size > maxSize) {
            errors.push(`File size (${this.formatFileSize(file.size)}) exceeds maximum allowed size (${this.formatFileSize(maxSize)})`);
        }

        if (allowedTypes.length > 0) {
            const isAllowed = allowedTypes.some(type => {
                if (type.endsWith('/*')) {
                    return file.type.startsWith(type.slice(0, -1));
                }
                return file.type === type;
            });

            if (!isAllowed) {
                errors.push(`File type ${file.type} is not allowed`);
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
};
