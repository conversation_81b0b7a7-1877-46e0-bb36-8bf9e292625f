<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>



        <!-- Visa Request Tree View -->
        <record id="view_visa_request_tree" model="ir.ui.view">
            <field name="name">visa.request.tree</field>
            <field name="model">visa.request</field>
            <field name="arch" type="xml">
                <tree string="Visa Requests" decoration-info="state=='draft'" 
                      decoration-warning="state=='in_progress'" decoration-success="state=='done'">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="visa_type_id"/>
                    <field name="country_id"/>
                    <field name="price" widget="monetary"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="request_date"/>
                    <field name="state" widget="badge" 
                           decoration-info="state=='draft'" 
                           decoration-warning="state=='in_progress'" 
                           decoration-success="state=='done'"/>
                </tree>
            </field>
        </record>

        <!-- Visa Request Form View -->
        <record id="view_visa_request_form" model="ir.ui.view">
            <field name="name">visa.request.form</field>
            <field name="model">visa.request</field>
            <field name="arch" type="xml">
                <form string="Visa Request">
                    <header>
                        <button name="action_start_processing" type="object"
                                string="Start Processing" class="btn-primary"
                                attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                        <button name="action_complete" type="object"
                                string="Mark as Complete" class="btn-success"
                                attrs="{'invisible': [('state', '!=', 'in_progress')]}"/>
                        <button name="action_reset_to_draft" type="object"
                                string="Reset to Draft" class="btn-secondary"
                                attrs="{'invisible': [('state', '==', 'draft')]}"/>
                        <button name="action_create_documents" type="object"
                                string="Create Required Documents" class="btn-warning"
                                attrs="{'invisible': [('visa_type_id', '=', False)]}"/>
                        <button name="action_check_document_completeness" type="object"
                                string="Check Documents" class="btn-info"
                                attrs="{'invisible': [('visa_type_id', '=', False)]}"/>
                        <button name="refresh_document_stats" type="object"
                                string="Refresh Stats" class="btn-secondary"
                                attrs="{'invisible': [('visa_type_id', '=', False)]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,in_progress,done"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="partner_id"/>
                                <field name="visa_type_id"/>
                                <field name="country_id"/>
                            </group>
                            <group>
                                <field name="price" widget="monetary"/>
                                <field name="currency_id"/>
                                <field name="request_date"/>
                                <field name="completion_date" attrs="{'invisible': [('state', '!=', 'done')]}"/>
                            </group>
                        </group>

                        <!-- Document Progress -->
                        <div class="alert alert-info" role="status" attrs="{'invisible': [('total_documents', '=', 0)]}">
                            <h4>Document Progress</h4>
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>Total Documents:</strong> <field name="total_documents" readonly="1"/>
                                </div>
                                <div class="col-md-3">
                                    <strong>Uploaded:</strong> <field name="uploaded_documents" readonly="1"/>
                                </div>
                                <div class="col-md-3">
                                    <strong>Approved:</strong> <field name="approved_documents" readonly="1"/>
                                </div>
                                <div class="col-md-3">
                                    <strong>Completion:</strong> <field name="document_completion_rate" readonly="1" widget="percentage"/>
                                </div>
                            </div>
                            <div class="progress" style="margin-top: 10px;">
                                <div class="progress-bar" role="progressbar"
                                     style="width: 0%"
                                     aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                        
                        <notebook>
                            <page string="Documents" name="documents">
                                <!-- Documents List -->
                                <field name="document_ids">
                                    <tree editable="bottom" decoration-success="is_approved==True" decoration-warning="attachment_id==False">
                                        <field name="document_type_id"/>
                                        <field name="datas" widget="binary" filename="filename"/>
                                        <field name="filename"/>
                                        <field name="file_type"/>
                                        <field name="file_size" string="Size (MB)" widget="float" digits="[16,2]"/>
                                        <field name="is_approved" widget="boolean_toggle"/>
                                        <field name="notes"/>
                                        <!-- Action buttons -->
                                        <button name="action_preview_file" type="object"
                                                string="Preview" icon="fa-eye"
                                                attrs="{'invisible': [('attachment_id', '=', False)]}"
                                                class="btn-primary"/>
                                        <button name="action_download_file" type="object"
                                                string="Download" icon="fa-download"
                                                attrs="{'invisible': [('attachment_id', '=', False)]}"
                                                class="btn-secondary"/>
                                        <!-- Hidden fields for decorations -->
                                        <field name="attachment_id" invisible="1"/>
                                        <field name="is_image" invisible="1"/>
                                        <field name="is_pdf" invisible="1"/>
                                    </tree>
                                    <form>
                                        <sheet>
                                            <div class="oe_title">
                                                <h1>
                                                    <field name="document_type_id" readonly="1"/>
                                                </h1>
                                            </div>

                                            <group>
                                                <group string="File Information">
                                                    <field name="datas" widget="binary" filename="filename"/>
                                                    <field name="filename" readonly="1"/>
                                                    <field name="file_type" readonly="1"/>
                                                    <field name="file_size" readonly="1" string="Size (MB)" widget="float" digits="[16,2]"/>
                                                </group>
                                                <group string="Status">
                                                    <field name="is_approved"/>
                                                    <field name="notes"/>
                                                </group>
                                            </group>

                                            <!-- File Preview Section -->
                                            <notebook attrs="{'invisible': [('attachment_id', '=', False)]}">
                                                <page string="Preview" name="preview">
                                                    <!-- Image Preview -->
                                                    <div attrs="{'invisible': [('is_image', '=', False)]}" class="text-center">
                                                        <h4>Image Preview</h4>
                                                        <field name="attachment_id" widget="image" options="{'size': [400, 300]}" nolabel="1"/>
                                                    </div>

                                                    <!-- PDF Preview -->
                                                    <div attrs="{'invisible': [('is_pdf', '=', False)]}" class="text-center">
                                                        <h4>PDF Document</h4>
                                                        <field name="attachment_id" widget="pdf_viewer" nolabel="1"/>
                                                    </div>

                                                    <!-- Other Files -->
                                                    <div attrs="{'invisible': ['|', ('is_image', '=', True), ('is_pdf', '=', True)]}" class="text-center">
                                                        <h4>Document File</h4>
                                                        <p>Click the download button above to view this file.</p>
                                                        <field name="attachment_id" widget="binary" nolabel="1"/>
                                                    </div>

                                                    <!-- Hidden fields for attrs -->
                                                    <field name="is_image" invisible="1"/>
                                                    <field name="is_pdf" invisible="1"/>
                                                </page>
                                            </notebook>
                                        </sheet>
                                    </form>
                                </field>
                            </page>

                            <page string="Notes" name="notes">
                                <group>
                                    <field name="notes" nolabel="1" placeholder="Customer notes..."/>
                                </group>
                                <group string="Internal Notes" groups="travel_agent_management.group_travel_agent_manager">
                                    <field name="internal_notes" nolabel="1" placeholder="Internal notes for staff..."/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Visa Request Kanban View -->
        <record id="view_visa_request_kanban" model="ir.ui.view">
            <field name="name">visa.request.kanban</field>
            <field name="model">visa.request</field>
            <field name="arch" type="xml">
                <kanban default_group_by="state">
                    <field name="id"/>
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="visa_type_id"/>
                    <field name="country_id"/>
                    <field name="price"/>
                    <field name="currency_id"/>
                    <field name="request_date"/>
                    <field name="state"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="oe_kanban_content">
                                    <div class="o_kanban_record_top">
                                        <div class="o_kanban_record_headings">
                                            <strong class="o_kanban_record_title">
                                                <field name="name"/>
                                            </strong>
                                            <span class="o_kanban_record_subtitle">
                                                <field name="partner_id"/>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div>
                                            <strong><field name="visa_type_id"/></strong>
                                        </div>
                                        <div>
                                            <field name="country_id"/> • <field name="price" widget="monetary"/>
                                        </div>
                                        <div class="o_kanban_record_bottom">
                                            <div class="oe_kanban_bottom_left">
                                                <span class="o_kanban_record_subtitle">
                                                    <field name="request_date"/>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Visa Request Search View -->
        <record id="view_visa_request_search" model="ir.ui.view">
            <field name="name">visa.request.search</field>
            <field name="model">visa.request</field>
            <field name="arch" type="xml">
                <search string="Search Visa Requests">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="visa_type_id"/>
                    <field name="country_id"/>
                    <separator/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="In Progress" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                    <filter string="Done" name="done" domain="[('state', '=', 'done')]"/>
                    <separator/>
                    <filter string="This Month" name="this_month" 
                            domain="[('request_date', '>=', (context_today() - relativedelta(day=1)).strftime('%Y-%m-%d')),
                                     ('request_date', '&lt;', (context_today() + relativedelta(months=1, day=1)).strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Country" name="group_country" context="{'group_by': 'country_id'}"/>
                        <filter string="Visa Type" name="group_visa_type" context="{'group_by': 'visa_type_id'}"/>
                        <filter string="Customer" name="group_customer" context="{'group_by': 'partner_id'}"/>
                        <filter string="Request Date" name="group_request_date" context="{'group_by': 'request_date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Visa Request Document Preview Form -->
        <record id="view_visa_request_document_preview_form" model="ir.ui.view">
            <field name="name">visa.request.document.preview.form</field>
            <field name="model">visa.request.document</field>
            <field name="arch" type="xml">
                <form string="Document Preview">
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="document_type_id" readonly="1"/>
                            </h1>
                            <h2>
                                <field name="filename" readonly="1"/>
                            </h2>
                        </div>

                        <group>
                            <group string="File Information">
                                <field name="file_type" readonly="1"/>
                                <field name="file_size" readonly="1" string="Size (MB)" widget="float" digits="[16,2]"/>
                                <field name="is_approved" readonly="1"/>
                            </group>
                        </group>

                        <!-- Large Preview Section -->
                        <div class="text-center" style="margin-top: 20px;">
                            <!-- Image Preview -->
                            <div attrs="{'invisible': [('is_image', '=', False)]}">
                                <field name="datas" widget="image" options="{'size': [800, 600]}" nolabel="1"/>
                            </div>

                            <!-- PDF Preview -->
                            <div attrs="{'invisible': [('is_pdf', '=', False)]}">
                                <field name="datas" widget="pdf_viewer" nolabel="1" options="{'height': 600}"/>
                            </div>

                            <!-- Other Files -->
                            <div attrs="{'invisible': ['|', ('is_image', '=', True), ('is_pdf', '=', True)]}">
                                <h3>Document File</h3>
                                <p>This file type cannot be previewed. Click the button below to download.</p>
                                <field name="datas" widget="binary" nolabel="1"/>
                            </div>
                        </div>

                        <!-- Hidden fields -->
                        <field name="is_image" invisible="1"/>
                        <field name="is_pdf" invisible="1"/>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Visa Request Action -->
        <record id="action_visa_request" model="ir.actions.act_window">
            <field name="name">Visa Requests</field>
            <field name="res_model">visa.request</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="search_view_id" ref="view_visa_request_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No visa requests yet!
                </p>
                <p>
                    Visa requests will appear here when customers submit applications
                    through the portal or when created manually.
                </p>
            </field>
        </record>


    </data>
</odoo>
