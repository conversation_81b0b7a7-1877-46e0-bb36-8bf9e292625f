<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <!-- Document Upload Widget Template -->
    <t t-name="travel_agent_management.DocumentUploadWidget">
        <div class="document_upload_widget">
            <div t-attf-class="file_upload_area #{state.isDragOver ? 'dragover' : ''}" 
                 t-if="!state.isUploading">
                <i class="fa fa-cloud-upload fa-3x text-muted mb-3"></i>
                <h4>Drag &amp; Drop Files Here</h4>
                <p class="text-muted">or click to browse</p>
                <input type="file" class="d-none" multiple="true" 
                       t-on-change="handleFileSelect"/>
                <button class="btn btn-primary" t-on-click="triggerFileSelect">
                    <i class="fa fa-folder-open"></i> Browse Files
                </button>
            </div>
            
            <div class="upload_progress" t-if="state.isUploading">
                <h4>Uploading Files...</h4>
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" 
                         t-attf-style="width: #{state.uploadProgress}%"
                         t-attf-aria-valuenow="#{state.uploadProgress}"
                         aria-valuemin="0" aria-valuemax="100">
                        <t t-esc="Math.round(state.uploadProgress)"/>%
                    </div>
                </div>
            </div>
        </div>
    </t>

    <!-- Document Preview Widget Template -->
    <t t-name="travel_agent_management.DocumentPreviewWidget">
        <div class="document_preview_widget">
            <div class="preview_loading text-center" t-if="state.isLoading">
                <i class="fa fa-spinner fa-spin fa-2x"></i>
                <p>Loading preview...</p>
            </div>
            
            <div class="preview_content" t-if="!state.isLoading and state.previewUrl">
                <!-- Image Preview -->
                <div t-if="state.fileType === 'image'" class="image_preview text-center">
                    <img t-attf-src="#{state.previewUrl}" 
                         class="img-fluid" 
                         style="max-height: 400px; border: 1px solid #ddd; border-radius: 4px;"/>
                </div>
                
                <!-- PDF Preview -->
                <div t-if="state.fileType === 'pdf'" class="pdf_preview text-center">
                    <iframe t-attf-src="#{state.previewUrl}" 
                            style="width: 100%; height: 400px; border: 1px solid #ddd;"></iframe>
                </div>
                
                <!-- Other Files -->
                <div t-if="state.fileType === 'document'" class="document_preview text-center">
                    <i class="fa fa-file-o fa-5x text-muted mb-3"></i>
                    <h4>Document File</h4>
                    <p class="text-muted">Preview not available for this file type</p>
                </div>
                
                <!-- Action Buttons -->
                <div class="preview_actions text-center mt-3">
                    <button class="btn btn-primary me-2" t-on-click="openFullPreview">
                        <i class="fa fa-expand"></i> Full Preview
                    </button>
                    <button class="btn btn-secondary" t-on-click="downloadFile">
                        <i class="fa fa-download"></i> Download
                    </button>
                </div>
            </div>
            
            <div class="no_preview text-center" t-if="!state.isLoading and !state.previewUrl">
                <i class="fa fa-file-o fa-3x text-muted mb-3"></i>
                <h4>No File Available</h4>
                <p class="text-muted">Please upload a file to see preview</p>
            </div>
        </div>
    </t>

    <!-- Document Progress Bar Template -->
    <t t-name="travel_agent_management.DocumentProgressBar">
        <div class="document_progress_widget">
            <div class="progress document_progress_bar">
                <div class="progress-bar" 
                     role="progressbar" 
                     t-attf-style="width: #{props.percentage || 0}%"
                     t-attf-aria-valuenow="#{props.percentage || 0}"
                     aria-valuemin="0" 
                     aria-valuemax="100">
                    <span t-esc="progressText"></span>
                </div>
            </div>
            <small class="text-muted" t-if="props.label">
                <t t-esc="props.label"/>
            </small>
        </div>
    </t>

    <!-- Document Status Badge Template -->
    <t t-name="travel_agent_management.DocumentStatusBadge">
        <span t-attf-class="badge #{getBadgeClass()}">
            <i t-attf-class="fa #{getStatusIcon()}"></i>
            <t t-esc="getStatusText()"/>
        </span>
    </t>

    <!-- File Type Icon Template -->
    <t t-name="travel_agent_management.FileTypeIcon">
        <i t-attf-class="fa #{getFileTypeIcon()} file_type_icon #{getFileTypeClass()}"></i>
    </t>

    <!-- Document List Item Template -->
    <t t-name="travel_agent_management.DocumentListItem">
        <div class="document_list_item d-flex align-items-center p-2 border-bottom">
            <div class="document_icon me-3">
                <t t-call="travel_agent_management.FileTypeIcon">
                    <t t-set="mimetype" t-value="props.document.mimetype"/>
                </t>
            </div>
            
            <div class="document_info flex-grow-1">
                <h6 class="mb-1">
                    <t t-esc="props.document.document_type_name"/>
                </h6>
                <p class="mb-1 text-muted" t-if="props.document.filename">
                    <small><t t-esc="props.document.filename"/></small>
                </p>
                <div class="document_meta">
                    <small class="text-muted">
                        <t t-if="props.document.file_size">
                            Size: <t t-esc="formatFileSize(props.document.file_size)"/>
                        </t>
                        <t t-if="props.document.file_type">
                            | Type: <t t-esc="props.document.file_type"/>
                        </t>
                    </small>
                </div>
            </div>
            
            <div class="document_status me-3">
                <t t-call="travel_agent_management.DocumentStatusBadge">
                    <t t-set="status" t-value="getDocumentStatus(props.document)"/>
                </t>
            </div>
            
            <div class="document_actions">
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" 
                            t-if="props.document.attachment_id"
                            t-on-click="previewDocument">
                        <i class="fa fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-secondary" 
                            t-if="props.document.attachment_id"
                            t-on-click="downloadDocument">
                        <i class="fa fa-download"></i>
                    </button>
                    <button class="btn btn-outline-success" 
                            t-if="props.document.attachment_id and !props.document.is_approved"
                            t-on-click="approveDocument">
                        <i class="fa fa-check"></i>
                    </button>
                </div>
            </div>
        </div>
    </t>

    <!-- Document Statistics Template -->
    <t t-name="travel_agent_management.DocumentStatistics">
        <div class="document_statistics">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat_item text-center">
                        <h3 class="stat_number text-primary">
                            <t t-esc="props.stats.total"/>
                        </h3>
                        <p class="stat_label">Total Documents</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat_item text-center">
                        <h3 class="stat_number text-info">
                            <t t-esc="props.stats.uploaded"/>
                        </h3>
                        <p class="stat_label">Uploaded</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat_item text-center">
                        <h3 class="stat_number text-success">
                            <t t-esc="props.stats.approved"/>
                        </h3>
                        <p class="stat_label">Approved</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat_item text-center">
                        <h3 class="stat_number">
                            <t t-esc="Math.round(props.stats.completion_rate)"/>%
                        </h3>
                        <p class="stat_label">Completion</p>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <t t-call="travel_agent_management.DocumentProgressBar">
                    <t t-set="percentage" t-value="props.stats.completion_rate"/>
                    <t t-set="label" t-value="'Document Completion Progress'"/>
                </t>
            </div>
        </div>
    </t>

    <!-- File Upload Drop Zone Template -->
    <t t-name="travel_agent_management.FileUploadDropZone">
        <div class="file_upload_drop_zone">
            <div class="drop_zone_content text-center">
                <i class="fa fa-cloud-upload fa-4x text-primary mb-3"></i>
                <h4>Drop files here to upload</h4>
                <p class="text-muted">Supported formats: Images, PDF, Documents</p>
                <p class="text-muted">Maximum file size: 5MB</p>
            </div>
        </div>
    </t>

</templates>
