<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Medical Insurance Request Tree View -->
        <record id="view_medical_insurance_request_tree" model="ir.ui.view">
            <field name="name">medical.insurance.request.tree</field>
            <field name="model">medical.insurance.request</field>
            <field name="arch" type="xml">
                <tree decoration-info="state=='draft'" decoration-success="state=='confirmed'" decoration-muted="state=='cancelled'">
                    <field name="name"/>
                    <field name="customer_id"/>
                    <field name="insurance_summary"/>
                    <field name="arrive_date"/>
                    <field name="departure_date"/>
                    <field name="duration"/>
                    <field name="insurance_type"/>
                    <field name="supplier_id"/>
                    <field name="customer_price" widget="monetary"/>
                    <field name="profit_amount" widget="monetary"/>
                    <field name="sale_order_id" optional="hide"/>
                    <field name="purchase_order_id" optional="hide"/>
                    <field name="state" widget="badge" decoration-info="state=='draft'" decoration-success="state=='confirmed'" decoration-muted="state=='cancelled'"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <!-- Medical Insurance Request Form View -->
        <record id="view_medical_insurance_request_form" model="ir.ui.view">
            <field name="name">medical.insurance.request.form</field>
            <field name="model">medical.insurance.request</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_confirm" type="object" string="Confirm" 
                                class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                        <button name="action_create_invoice" type="object" string="Create Invoice" 
                                class="btn-primary" 
                                attrs="{'invisible': ['|', '|', ('customer_price', '=', 0), ('id', '=', False), ('sale_order_id', '!=', False)]}"/>
                        <button name="action_create_vendor_bill" type="object" string="Create Bill" 
                                class="btn-primary" 
                                attrs="{'invisible': ['|', '|', ('supplier_cost', '=', 0), ('id', '=', False), ('purchase_order_id', '!=', False)]}"/>
                        <button name="action_cancel" type="object" string="Cancel" 
                                class="btn-secondary" attrs="{'invisible': [('state', '=', 'cancelled')]}"/>
                        <button name="action_set_to_draft" type="object" string="Set to Draft" 
                                class="btn-secondary" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,cancelled"/>
                    </header>
                    
                    <sheet>
                        <!-- Stat Buttons -->
                        <div class="oe_button_box" name="button_box">
                            <!-- Sales Order Button -->
                            <button name="action_view_sale_order" type="object" 
                                    class="oe_stat_button" icon="fa-file-text-o"
                                    attrs="{'invisible': [('sale_order_id', '=', False)]}">
                                <field name="sale_order_id" widget="statinfo" string="Sales Order"/>
                            </button>
                            
                            <!-- Purchase Order Button -->
                            <button name="action_view_purchase_order" type="object" 
                                    class="oe_stat_button" icon="fa-file-o"
                                    attrs="{'invisible': [('purchase_order_id', '=', False)]}">
                                <field name="purchase_order_id" widget="statinfo" string="Purchase Order"/>
                            </button>
                        </div>

                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group string="Travel Details">
                                <field name="customer_id" options="{'no_create': True}"/>
                                <field name="travel_country"/>
                                <field name="nationality"/>
                                <field name="insurance_summary" readonly="1"/>
                                <field name="insurance_type"/>
                                <field name="coverage_amount" widget="monetary"/>
                            </group>
                        </group>
                        
                        <group>
                            <group string="Arrival">
                                <field name="arrive_date"/>
                            </group>
                            
                            <group string="Departure">
                                <field name="departure_date"/>
                                <field name="duration" readonly="1"/>
                            </group>
                        </group>
                        
                        <group>
                            <group string="Personal Information">
                                <field name="passport_no"/>
                                <field name="tel_no"/>
                                <field name="address"/>
                            </group>
                            
                            <group string="Pricing">
                                <field name="currency_id" invisible="1"/>
                                <field name="supplier_id" options="{'no_create': True}"/>
                                <field name="supplier_cost" widget="monetary"/>
                                <field name="customer_price" widget="monetary"/>
                                <field name="profit_amount" widget="monetary" readonly="1" 
                                       decoration-success="profit_amount &gt; 0" 
                                       decoration-danger="profit_amount &lt; 0"/>
                                <field name="profit_percentage" readonly="1" widget="percentage"
                                       decoration-success="profit_percentage &gt; 15" 
                                       decoration-warning="profit_percentage &gt;= 5 and profit_percentage &lt;= 15"
                                       decoration-danger="profit_percentage &lt; 5"/>
                            </group>
                        </group>
                        
                        <group string="Related Orders" attrs="{'invisible': [('sale_order_id', '=', False), ('purchase_order_id', '=', False)]}">
                            <group>
                                <field name="sale_order_id" readonly="1" attrs="{'invisible': [('sale_order_id', '=', False)]}"/>
                            </group>
                            <group>
                                <field name="purchase_order_id" readonly="1" attrs="{'invisible': [('purchase_order_id', '=', False)]}"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Special Conditions">
                                <field name="special_conditions" placeholder="Any special conditions or requirements for the insurance coverage"/>
                            </page>
                            
                            <page string="Notes">
                                <group>
                                    <group string="Customer Notes">
                                        <field name="customer_notes" nolabel="1" placeholder="Notes visible to customer"/>
                                    </group>
                                    <group string="Internal Notes">
                                        <field name="notes" nolabel="1" placeholder="Internal notes for staff"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Medical Insurance Request Search View -->
        <record id="view_medical_insurance_request_search" model="ir.ui.view">
            <field name="name">medical.insurance.request.search</field>
            <field name="model">medical.insurance.request</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="Reference"/>
                    <field name="customer_id" string="Customer"/>
                    <field name="travel_country" string="Travel Country"/>
                    <field name="nationality" string="Nationality"/>
                    <field name="supplier_id" string="Insurance Provider"/>
                    <field name="passport_no" string="Passport No"/>
                    <field name="insurance_type" string="Insurance Type"/>
                    
                    <filter name="draft" string="Draft" domain="[('state', '=', 'draft')]"/>
                    <filter name="confirmed" string="Confirmed" domain="[('state', '=', 'confirmed')]"/>
                    <filter name="cancelled" string="Cancelled" domain="[('state', '=', 'cancelled')]"/>
                    
                    <separator/>
                    <filter name="today" string="Arriving Today" domain="[('arrive_date', '=', context_today())]"/>
                    <filter name="this_week" string="Arriving This Week" domain="[('arrive_date', '&gt;=', (context_today() - datetime.timedelta(days=7)))]"/>
                    <filter name="this_month" string="Arriving This Month" domain="[('arrive_date', '&gt;=', (context_today() - datetime.timedelta(days=30)))]"/>
                    
                    <separator/>
                    <filter name="profitable" string="Profitable" domain="[('profit_amount', '&gt;', 0)]"/>
                    <filter name="loss" string="Loss" domain="[('profit_amount', '&lt;', 0)]"/>
                    
                    <group expand="0" string="Group By">
                        <filter name="group_customer" string="Customer" context="{'group_by': 'customer_id'}"/>
                        <filter name="group_supplier" string="Insurance Provider" context="{'group_by': 'supplier_id'}"/>
                        <filter name="group_state" string="Status" context="{'group_by': 'state'}"/>
                        <filter name="group_travel_country" string="Travel Country" context="{'group_by': 'travel_country'}"/>
                        <filter name="group_nationality" string="Nationality" context="{'group_by': 'nationality'}"/>
                        <filter name="group_insurance_type" string="Insurance Type" context="{'group_by': 'insurance_type'}"/>
                        <filter name="group_arrive_date" string="Arrival Date" context="{'group_by': 'arrive_date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Medical Insurance Request Kanban View -->
        <record id="view_medical_insurance_request_kanban" model="ir.ui.view">
            <field name="name">medical.insurance.request.kanban</field>
            <field name="model">medical.insurance.request</field>
            <field name="arch" type="xml">
                <kanban default_group_by="state" class="o_kanban_small_column">
                    <field name="name"/>
                    <field name="customer_id"/>
                    <field name="insurance_summary"/>
                    <field name="arrive_date"/>
                    <field name="departure_date"/>
                    <field name="duration"/>
                    <field name="insurance_type"/>
                    <field name="customer_price"/>
                    <field name="profit_amount"/>
                    <field name="state"/>
                    <field name="currency_id"/>
                    
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <br/>
                                        <span class="o_kanban_record_subtitle">
                                            <field name="customer_id"/>
                                        </span>
                                    </div>
                                    <div class="o_kanban_record_top_right">
                                        <span class="badge badge-pill" t-attf-class="badge-#{record.state.raw_value == 'confirmed' ? 'success' : record.state.raw_value == 'cancelled' ? 'secondary' : 'info'}">
                                            <field name="state"/>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="o_kanban_record_body">
                                    <div>
                                        <i class="fa fa-shield"/> <field name="insurance_summary"/>
                                    </div>
                                    <div>
                                        <i class="fa fa-calendar"/> <field name="arrive_date"/> - <field name="departure_date"/>
                                    </div>
                                    <div>
                                        <i class="fa fa-clock-o"/> <field name="duration"/> days
                                        <t t-if="record.insurance_type.raw_value">
                                            <i class="fa fa-tag"/> <field name="insurance_type"/>
                                        </t>
                                    </div>
                                </div>
                                
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span>Price: <field name="customer_price" widget="monetary"/></span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span t-attf-class="#{record.profit_amount.raw_value >= 0 ? 'text-success' : 'text-danger'}">
                                            Profit: <field name="profit_amount" widget="monetary"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Medical Insurance Request Action -->
        <record id="action_medical_insurance_request" model="ir.actions.act_window">
            <field name="name">Medical Insurance Requests</field>
            <field name="res_model">medical.insurance.request</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="search_view_id" ref="view_medical_insurance_request_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No medical insurance requests yet!
                </p>
                <p>
                    Create your first medical insurance request to manage travel insurance.
                </p>
            </field>
        </record>

        <!-- Menu Items -->
        <menuitem id="menu_insurance_management" 
                  name="Insurance Management" 
                  parent="menu_travel_agent_main" 
                  sequence="50"/>
                  
        <menuitem id="menu_medical_insurance_requests" 
                  name="Medical Insurance Requests" 
                  parent="menu_insurance_management" 
                  action="action_medical_insurance_request" 
                  sequence="10"/>

    </data>
</odoo>
