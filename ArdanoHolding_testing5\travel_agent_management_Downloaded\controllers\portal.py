# -*- coding: utf-8 -*-

from collections import OrderedDict
from datetime import datetime
from odoo import http, _
from odoo.exceptions import AccessError, MissingError, UserError
from odoo.http import request
from odoo.addons.portal.controllers.portal import CustomerPortal, pager as portal_pager
import base64
from odoo.tools import groupby as groupbyelem
from odoo.osv.expression import OR
import base64


class TravelPortal(CustomerPortal):

    def _prepare_home_portal_values(self, counters):
        values = super()._prepare_home_portal_values(counters)
        partner = request.env.user.partner_id

        # Always add travel request count
        TravelRequest = request.env['travel.ticket.request']
        travel_request_count = TravelRequest.search_count(self._get_travel_requests_domain()) \
            if TravelRequest.check_access_rights('read', raise_exception=False) else 0
        values['travel_request_count'] = travel_request_count

        # Always add passenger count
        Passenger = request.env['travel.passenger']
        passenger_count = Passenger.search_count(self._get_passengers_domain()) \
            if Passenger.check_access_rights('read', raise_exception=False) else 0
        values['passenger_count'] = passenger_count

        # Always add visa request count
        VisaRequest = request.env['visa.request']
        visa_request_count = VisaRequest.search_count(self._get_visa_requests_domain()) \
            if VisaRequest.check_access_rights('read', raise_exception=False) else 0
        values['visa_request_count'] = visa_request_count

        # Always add transfer request count
        TransferRequest = request.env['transfer.request']
        transfer_request_count = TransferRequest.search_count(self._get_transfer_requests_domain()) \
            if TransferRequest.check_access_rights('read', raise_exception=False) else 0
        values['transfer_request_count'] = transfer_request_count

        # Always add hotel booking request count
        HotelBookingRequest = request.env['hotel.booking.request']
        hotel_booking_count = HotelBookingRequest.search_count(self._get_hotel_booking_requests_domain()) \
            if HotelBookingRequest.check_access_rights('read', raise_exception=False) else 0
        values['hotel_booking_count'] = hotel_booking_count

        # Always add medical insurance request count
        MedicalInsuranceRequest = request.env['medical.insurance.request']
        medical_insurance_count = MedicalInsuranceRequest.search_count(self._get_medical_insurance_requests_domain()) \
            if MedicalInsuranceRequest.check_access_rights('read', raise_exception=False) else 0
        values['medical_insurance_count'] = medical_insurance_count

        return values

    def _get_travel_requests_domain(self):
        return [
            ('customer_id', '=', request.env.user.partner_id.commercial_partner_id.id)
        ]

    def _get_passengers_domain(self):
        return [
            '|',
            ('contact_id', '=', request.env.user.partner_id.id),
            ('contact_id', 'child_of', request.env.user.partner_id.commercial_partner_id.id)
        ]

    def _get_visa_requests_domain(self):
        return [
            ('partner_id', '=', request.env.user.partner_id.commercial_partner_id.id)
        ]

    def _get_transfer_requests_domain(self):
        return [
            ('partner_id', '=', request.env.user.partner_id.commercial_partner_id.id)
        ]

    def _get_hotel_booking_requests_domain(self):
        return [
            ('partner_id', '=', request.env.user.partner_id.commercial_partner_id.id)
        ]

    def _get_medical_insurance_requests_domain(self):
        return [
            ('partner_id', '=', request.env.user.partner_id.commercial_partner_id.id)
        ]

    def _prepare_travel_requests_domain(self, partner):
        return [
            ('customer_id', '=', partner.commercial_partner_id.id)
        ]

    def _prepare_passengers_domain(self, partner):
        return [
            '|',
            ('contact_id', '=', partner.id),
            ('contact_id', 'child_of', partner.commercial_partner_id.id)
        ]



    @http.route(['/my/counters'], type='json', auth="user")
    def portal_my_counters(self, counters, **kw):
        """Override counters route to include travel counters"""
        values = super()._prepare_home_portal_values(counters)
        # Always include travel counters if requested
        if 'travel_request_count' in counters:
            values.update(self._prepare_home_portal_values(['travel_request_count']))
        if 'passenger_count' in counters:
            values.update(self._prepare_home_portal_values(['passenger_count']))
        return values

    @http.route(['/my/passengers/test'], type='http', auth="user", website=True)
    def portal_passengers_test(self, **kw):
        """Simple test route for passengers"""
        return "<h1>Passengers Test - Working!</h1><p><a href='/my'>Back to Portal</a></p>"

    def _get_travel_requests_domain(self):
        return [
            ('customer_id', '=', request.env.user.partner_id.commercial_partner_id.id)
        ]

    def _prepare_travel_requests_domain(self, partner):
        return [
            ('customer_id', '=', partner.commercial_partner_id.id)
        ]

    def _get_passengers_domain(self):
        return [
            '|',
            ('contact_id', '=', request.env.user.partner_id.id),
            ('contact_id', 'child_of', request.env.user.partner_id.commercial_partner_id.id)
        ]

    def _prepare_passengers_domain(self, partner):
        return [
            '|',
            ('contact_id', '=', partner.id),
            ('contact_id', 'child_of', partner.commercial_partner_id.id)
        ]

    @http.route(['/my/travel_requests', '/my/travel_requests/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_travel_requests(self, page=1, date_begin=None, date_end=None, sortby=None, search=None, search_in='content', **kw):
        try:
            values = self._prepare_portal_layout_values()
            partner = request.env.user.partner_id
            TravelRequest = request.env['travel.ticket.request']

            # Check access rights
            if not TravelRequest.check_access_rights('read', raise_exception=False):
                return request.redirect('/my')

            domain = self._prepare_travel_requests_domain(partner)
        except Exception as e:
            # Log error and redirect to portal home
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error in portal_my_travel_requests: {e}")
            return request.redirect('/my')

        searchbar_sortings = {
            'date': {'label': _('Request Date'), 'order': 'request_date desc'},
            'name': {'label': _('Reference'), 'order': 'name'},
            'state': {'label': _('Status'), 'order': 'state'},
        }

        searchbar_inputs = {
            'content': {'input': 'content', 'label': _('All Fields')},
            'name': {'input': 'name', 'label': _('Name')},
            'passport': {'input': 'passport', 'label': _('Passport Number')},
            'email': {'input': 'email', 'label': _('Email')},
        }

        # default sort order
        if not sortby:
            sortby = 'date'
        order = searchbar_sortings[sortby]['order']

        # date filtering
        if date_begin and date_end:
            domain += [('create_date', '>', date_begin), ('create_date', '<=', date_end)]

        # search
        if search and search_in:
            search_domain = []
            if search_in in ('content', 'all'):
                search_domain = OR([search_domain, [('name', 'ilike', search)]])
            if search_in in ('customer', 'all'):
                search_domain = OR([search_domain, [('customer_id', 'ilike', search)]])
            if search_in in ('message', 'all'):
                search_domain = OR([search_domain, [('message_ids.body', 'ilike', search)]])
            domain += search_domain

        # travel request count
        travel_request_count = TravelRequest.search_count(domain)
        # pager
        pager = portal_pager(
            url="/my/travel_requests",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby},
            total=travel_request_count,
            page=page,
            step=self._items_per_page
        )
        # content according to pager and archive selected
        travel_requests = TravelRequest.search(domain, order=order, limit=self._items_per_page, offset=pager['offset'])
        request.session['my_travel_requests_history'] = travel_requests.ids[:100]

        values.update({
            'date': date_begin,
            'date_end': date_end,
            'travel_requests': travel_requests,
            'page_name': 'travel_request',
            'pager': pager,
            'default_url': '/my/travel_requests',
            'searchbar_sortings': searchbar_sortings,
            'searchbar_inputs': searchbar_inputs,
            'search_in': search_in,
            'search': search,
            'sortby': sortby,
            'archive_groups': [],
        })

        return request.render("travel_agent_management.portal_my_travel_requests", values)

    @http.route(['/my/travel_request/<int:travel_request_id>'], type='http', auth="public", website=True)
    def portal_my_travel_request(self, travel_request_id=None, access_token=None, **kw):
        try:
            travel_request_sudo = self._document_check_access('travel.ticket.request', travel_request_id, access_token)
        except (AccessError, MissingError):
            return request.redirect('/my')

        values = self._travel_request_get_page_view_values(travel_request_sudo, access_token, **kw)
        return request.render("travel_agent_management.portal_my_travel_request", values)

    def _travel_request_get_page_view_values(self, travel_request, access_token, **kwargs):
        values = {
            'page_name': 'travel_request',
            'travel_request': travel_request,
        }
        return self._get_page_view_values(travel_request, access_token, values, 'my_travel_requests_history', False, **kwargs)

    @http.route(['/my/travel_request/new'], type='http', auth="user", website=True)
    def portal_travel_request_new(self, **kw):
        """Display form to create new travel request"""
        values = self._prepare_portal_layout_values()

        # Get passengers for current user's company
        partner = request.env.user.partner_id
        passengers = request.env['travel.passenger'].search([
            '|',
            ('contact_id', '=', partner.id),
            ('contact_id', 'child_of', partner.commercial_partner_id.id)
        ])

        values.update({
            'page_name': 'travel_request_new',
            'passengers': passengers,
            'partner': partner,
        })
        return request.render("travel_agent_management.portal_travel_request_new", values)

    @http.route(['/my/travel_request/create'], type='http', auth="user", methods=['POST'], csrf=False, website=True)
    def portal_travel_request_create(self, **post):
        """Process travel request creation from portal"""
        partner = request.env.user.partner_id

        # Create travel request
        vals = {
            'customer_id': partner.commercial_partner_id.id,
            'request_date': datetime.now().date(),
            'assigned_to': False,  # Will be assigned by internal team
            'state': 'draft',
            'customer_notes': post.get('customer_notes', ''),
            'additional_services': post.get('additional_services', ''),
        }

        travel_request = request.env['travel.ticket.request'].sudo().create(vals)

        # Process passenger lines
        passenger_count = int(post.get('passenger_count', 0))
        for i in range(passenger_count):
            passenger_id = post.get(f'passenger_id_{i}')
            if passenger_id:
                # Handle date fields properly
                departure_date = post.get(f'departure_date_{i}')
                return_date = post.get(f'return_date_{i}')

                # Convert date strings to proper format or False if empty
                if departure_date:
                    try:
                        departure_date = datetime.strptime(departure_date, '%Y-%m-%d').date()
                    except (ValueError, TypeError):
                        departure_date = False
                else:
                    departure_date = False

                if return_date:
                    try:
                        return_date = datetime.strptime(return_date, '%Y-%m-%d').date()
                    except (ValueError, TypeError):
                        return_date = False
                else:
                    return_date = False

                passenger_vals = {
                    'request_id': travel_request.id,
                    'passenger_id': int(passenger_id),
                    'travel_purpose': post.get(f'travel_purpose_{i}', 'business'),
                    'departure_route': post.get(f'departure_route_{i}', ''),
                    'departure_date': departure_date,
                    'return_route': post.get(f'return_route_{i}', ''),
                    'return_date': return_date,
                    'ticket_class': post.get(f'ticket_class_{i}', 'economy'),
                    'seat_preference': post.get(f'seat_preference_{i}', 'no_preference'),
                    'meal_preference': post.get(f'meal_preference_{i}', 'regular'),
                    'special_requirements': post.get(f'special_requirements_{i}', ''),
                }
                request.env['travel.passenger.line'].sudo().create(passenger_vals)

        # Send notification message
        travel_request.message_post(
            body=_("Travel request submitted by customer via portal."),
            message_type='notification',
            subtype_xmlid='mail.mt_comment'
        )

        return request.redirect(f'/my/travel_request/{travel_request.id}?message=created')

    @http.route(['/my/travel_request/<int:travel_request_id>/message'], type='http', auth="user", methods=['POST'], csrf=False, website=True)
    def portal_travel_request_message(self, travel_request_id, **post):
        """Allow customer to post messages on their travel request"""
        try:
            travel_request = self._document_check_access('travel.ticket.request', travel_request_id)
        except (AccessError, MissingError):
            return request.redirect('/my')

        message = post.get('message', '').strip()
        if message:
            travel_request.message_post(
                body=message,
                message_type='comment',
                subtype_xmlid='mail.mt_comment'
            )

        return request.redirect(f'/my/travel_request/{travel_request_id}?message=sent')

    # Passenger Management Routes
    @http.route(['/my/passengers', '/my/passengers/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_passengers(self, page=1, search=None, search_in='content', view_type='grid', group_by=None, sortby=None, filterby=None, **kw):
        values = self._prepare_portal_layout_values()
        partner = request.env.user.partner_id
        Passenger = request.env['travel.passenger']

        domain = self._prepare_passengers_domain(partner)

        # Define searchbar options
        searchbar_sortings = {
            'name': {'label': _('Name'), 'order': 'name'},
            'passport_expiry': {'label': _('Passport Expiry'), 'order': 'passport_expiration'},
            'nationality': {'label': _('Nationality'), 'order': 'nationality_id'},
            'create_date': {'label': _('Recently Added'), 'order': 'create_date desc'},
        }

        searchbar_filters = {
            'all': {'label': _('All'), 'domain': []},
            'valid': {'label': _('Valid Passport'), 'domain': [('passport_status', '=', 'valid')]},
            'expiring': {'label': _('Expiring Soon'), 'domain': [('passport_status', '=', 'expiring')]},
            'expired': {'label': _('Expired'), 'domain': [('passport_status', '=', 'expired')]},
        }

        searchbar_groupby = {
            'none': {'input': 'none', 'label': _('None')},
            'nationality': {'input': 'nationality', 'label': _('Nationality')},
            'passport_status': {'input': 'passport_status', 'label': _('Passport Status')},
            'travel_frequency': {'input': 'travel_frequency', 'label': _('Travel Frequency')},
        }

        searchbar_inputs = {
            'content': {'input': 'content', 'label': _('All Fields')},
            'name': {'input': 'name', 'label': _('Name')},
            'passport': {'input': 'passport', 'label': _('Passport Number')},
            'email': {'input': 'email', 'label': _('Email')},
        }

        # Default values with proper fallbacks
        if not sortby or sortby not in searchbar_sortings:
            sortby = 'name'
        if not filterby or filterby not in searchbar_filters:
            filterby = 'all'
        if not group_by or group_by not in searchbar_groupby:
            group_by = 'none'
        if not search_in or search_in not in searchbar_inputs:
            search_in = 'content'
        if not view_type or view_type not in ['grid', 'list']:
            view_type = 'grid'

        order = searchbar_sortings[sortby]['order']

        # Apply filters
        domain += searchbar_filters[filterby]['domain']

        # Apply search
        if search and search_in:
            search_domain = []
            if search_in in ('content', 'all'):
                search_domain = [
                    '|', '|', '|', '|',
                    ('name', 'ilike', search),
                    ('passport_number', 'ilike', search),
                    ('employee_id', 'ilike', search),
                    ('email', 'ilike', search),
                    ('nationality_id.name', 'ilike', search)
                ]
            elif search_in == 'name':
                search_domain = [('name', 'ilike', search)]
            elif search_in == 'passport':
                search_domain = [('passport_number', 'ilike', search)]
            elif search_in == 'email':
                search_domain = [('email', 'ilike', search)]

            domain += search_domain

        passenger_count = Passenger.search_count(domain)
        pager = portal_pager(
            url="/my/passengers",
            url_args={'search_in': search_in, 'search': search, 'sortby': sortby, 'filterby': filterby, 'group_by': group_by, 'view_type': view_type},
            total=passenger_count,
            page=page,
            step=self._items_per_page
        )

        passengers = Passenger.search(domain, order=order, limit=self._items_per_page, offset=pager['offset'])

        # Handle grouping
        grouped_passengers = []
        if group_by and group_by != 'none':
            if group_by == 'nationality':
                # Group by nationality
                nationality_groups = {}
                for passenger in passengers:
                    nationality = passenger.nationality_id.name if passenger.nationality_id else _('No Nationality')
                    if nationality not in nationality_groups:
                        nationality_groups[nationality] = []
                    nationality_groups[nationality].append(passenger)

                for nationality, group_passengers in nationality_groups.items():
                    grouped_passengers.append({
                        'name': nationality,
                        'passengers': group_passengers,
                        'count': len(group_passengers)
                    })
            elif group_by == 'passport_status':
                # Group by passport status
                status_groups = {'valid': [], 'expiring': [], 'expired': []}
                for passenger in passengers:
                    status_groups[passenger.passport_status].append(passenger)

                for status, group_passengers in status_groups.items():
                    if group_passengers:
                        status_labels = {'valid': _('Valid Passport'), 'expiring': _('Expiring Soon'), 'expired': _('Expired')}
                        grouped_passengers.append({
                            'name': status_labels[status],
                            'passengers': group_passengers,
                            'count': len(group_passengers)
                        })
            elif group_by == 'travel_frequency':
                # Group by travel frequency (based on travel count)
                frequent = passengers.filtered(lambda p: p.travel_count >= 5)
                occasional = passengers.filtered(lambda p: 1 <= p.travel_count < 5)
                new_travelers = passengers.filtered(lambda p: p.travel_count == 0)

                if frequent:
                    grouped_passengers.append({'name': _('Frequent Travelers (5+ trips)'), 'passengers': frequent, 'count': len(frequent)})
                if occasional:
                    grouped_passengers.append({'name': _('Occasional Travelers (1-4 trips)'), 'passengers': occasional, 'count': len(occasional)})
                if new_travelers:
                    grouped_passengers.append({'name': _('New Travelers'), 'passengers': new_travelers, 'count': len(new_travelers)})

        values.update({
            'passengers': passengers,
            'grouped_passengers': grouped_passengers,
            'passenger_count': passenger_count,
            'page_name': 'passengers',
            'pager': pager,
            'default_url': '/my/passengers',
            'searchbar_sortings': searchbar_sortings,
            'searchbar_filters': searchbar_filters,
            'searchbar_groupby': searchbar_groupby,
            'searchbar_inputs': searchbar_inputs,
            'search_in': search_in,
            'search': search or '',
            'sortby': sortby,
            'filterby': filterby,
            'group_by': group_by,
            'view_type': view_type,
        })

        return request.render("travel_agent_management.portal_my_passengers", values)

    @http.route(['/my/passenger/<int:passenger_id>'], type='http', auth="user", website=True)
    def portal_my_passenger(self, passenger_id=None, **kw):
        try:
            passenger = request.env['travel.passenger'].browse(passenger_id)
            # Check access
            if not passenger.exists():
                raise MissingError("Passenger not found")

            # Check if user has access to this passenger
            partner = request.env.user.partner_id
            if passenger.contact_id != partner and passenger.contact_id.id not in partner.commercial_partner_id.child_ids.ids:
                raise AccessError("Access denied")
        except (AccessError, MissingError):
            return request.redirect('/my/passengers')

        # Safely get attachment information
        passport_attachments = []
        visa_attachments = []
        other_attachments = []

        try:
            for att in passenger.sudo().passport_attachment_ids:
                passport_attachments.append({
                    'id': att.id,
                    'name': att.name,
                    'public': att.public
                })
        except:
            pass

        try:
            for att in passenger.sudo().visa_attachment_ids:
                visa_attachments.append({
                    'id': att.id,
                    'name': att.name,
                    'public': att.public
                })
        except:
            pass

        try:
            for att in passenger.sudo().other_attachment_ids:
                other_attachments.append({
                    'id': att.id,
                    'name': att.name,
                    'public': att.public
                })
        except:
            pass

        values = {
            'passenger': passenger,
            'page_name': 'passenger_detail',
            'passport_attachments': passport_attachments,
            'visa_attachments': visa_attachments,
            'other_attachments': other_attachments,
        }

        return request.render("travel_agent_management.portal_passenger_detail", values)

    @http.route(['/my/passenger/new'], type='http', auth="user", website=True)
    def portal_passenger_new(self, **kw):
        values = self._prepare_portal_layout_values()

        # Get list of countries for nationality selection
        countries = request.env['res.country'].search([])

        values.update({
            'page_name': 'passenger_new',
            'countries': countries,
        })
        return request.render("travel_agent_management.portal_passenger_new", values)

    @http.route(['/my/passenger/create'], type='http', auth="user", methods=['POST'], csrf=False, website=True)
    def portal_passenger_create(self, **post):
        partner = request.env.user.partner_id

        # Prepare values for passenger creation
        vals = {
            'name': post.get('name'),
            'customer_id': partner.commercial_partner_id.id,
            'contact_id': partner.id,
            'employee_id': post.get('employee_id'),
            'email': post.get('email'),
            'phone': post.get('phone'),
            'date_of_birth': post.get('date_of_birth') if post.get('date_of_birth') else False,
            'gender': post.get('gender') if post.get('gender') else False,
            'passport_number': post.get('passport_number'),
            'nationality_id': int(post.get('nationality_id')) if post.get('nationality_id') else False,
            'passport_expiration': post.get('passport_expiration'),
            'passport_issue_date': post.get('passport_issue_date') if post.get('passport_issue_date') else False,
            'passport_issue_place': post.get('passport_issue_place'),
            'seat_preference': post.get('seat_preference', 'no_preference'),
            'meal_preference': post.get('meal_preference', 'regular'),
            'special_requirements': post.get('special_requirements'),
        }

        try:
            passenger = request.env['travel.passenger'].sudo().create(vals)

            # Handle file uploads
            def handle_file_uploads(file_field, attachment_field):
                files = request.httprequest.files.getlist(file_field)
                attachment_ids = []
                for file in files:
                    if file and file.filename:
                        attachment_vals = {
                            'name': file.filename,
                            'datas': base64.b64encode(file.read()),
                            'res_model': 'travel.passenger',
                            'res_id': passenger.id,
                            'res_field': attachment_field,
                            'public': True,  # Make accessible to portal users
                            'access_token': request.env['ir.attachment']._generate_access_token(),
                        }
                        attachment = request.env['ir.attachment'].sudo().create(attachment_vals)
                        attachment_ids.append(attachment.id)
                return attachment_ids

            # Handle passport documents
            if request.httprequest.files.getlist('passport_files'):
                passport_ids = handle_file_uploads('passport_files', 'passport_attachment_ids')
                passenger.sudo().passport_attachment_ids = [(6, 0, passport_ids)]

            # Handle visa documents
            if request.httprequest.files.getlist('visa_files'):
                visa_ids = handle_file_uploads('visa_files', 'visa_attachment_ids')
                passenger.sudo().visa_attachment_ids = [(6, 0, visa_ids)]

            # Handle other documents
            if request.httprequest.files.getlist('other_files'):
                other_ids = handle_file_uploads('other_files', 'other_attachment_ids')
                passenger.sudo().other_attachment_ids = [(6, 0, other_ids)]

            return request.redirect(f'/my/passenger/{passenger.id}?message=created')
        except Exception as e:
            # If creation fails, redirect back with error
            return request.redirect('/my/passenger/new?error=creation_failed')

    @http.route(['/my/passenger/<int:passenger_id>/upload'], type='http', auth="user", methods=['POST'], csrf=False, website=True)
    def portal_passenger_upload(self, passenger_id, **post):
        """Upload additional documents to existing passenger"""
        try:
            passenger = request.env['travel.passenger'].browse(passenger_id)
            # Check access
            if not passenger.exists():
                raise MissingError("Passenger not found")

            # Check if user has access to this passenger
            partner = request.env.user.partner_id
            if passenger.contact_id != partner and passenger.contact_id.id not in partner.commercial_partner_id.child_ids.ids:
                raise AccessError("Access denied")
        except (AccessError, MissingError):
            return request.redirect('/my/passengers')

        document_type = post.get('document_type')
        files = request.httprequest.files.getlist('files')

        if document_type and files:
            attachment_ids = []
            for file in files:
                if file and file.filename:
                    attachment_vals = {
                        'name': file.filename,
                        'datas': base64.b64encode(file.read()),
                        'res_model': 'travel.passenger',
                        'res_id': passenger.id,
                        'public': True,  # Make accessible to portal users
                        'access_token': request.env['ir.attachment']._generate_access_token(),
                    }
                    attachment = request.env['ir.attachment'].sudo().create(attachment_vals)
                    attachment_ids.append(attachment.id)

            # Add to appropriate attachment field
            if document_type == 'passport':
                passenger.sudo().passport_attachment_ids = [(4, aid) for aid in attachment_ids]
            elif document_type == 'visa':
                passenger.sudo().visa_attachment_ids = [(4, aid) for aid in attachment_ids]
            elif document_type == 'other':
                passenger.sudo().other_attachment_ids = [(4, aid) for aid in attachment_ids]

        return request.redirect(f'/my/passenger/{passenger_id}?message=uploaded')

    # ========================================
    # Visa Request Portal Routes
    # ========================================

    @http.route(['/my/visa_requests', '/my/visa_requests/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_visa_requests(self, page=1, date_begin=None, date_end=None, sortby=None, search=None, search_in='content', **kw):
        try:
            values = self._prepare_portal_layout_values()
            partner = request.env.user.partner_id
            VisaRequest = request.env['visa.request']

            # Check access rights
            if not VisaRequest.check_access_rights('read', raise_exception=False):
                return request.redirect('/my')

            domain = [('partner_id', '=', partner.commercial_partner_id.id)]

            # Search functionality
            if search and search_in:
                search_domain = []
                if search_in in ('content', 'all'):
                    search_domain = OR([
                        [('name', 'ilike', search)],
                        [('notes', 'ilike', search)],
                        [('visa_type_id.name', 'ilike', search)],
                        [('country_id.name', 'ilike', search)]
                    ])
                domain += search_domain

            # Date filtering
            if date_begin and date_end:
                domain += [('request_date', '>=', date_begin), ('request_date', '<=', date_end)]

            # Sorting
            searchbar_sortings = {
                'date': {'label': _('Request Date'), 'order': 'request_date desc'},
                'name': {'label': _('Reference'), 'order': 'name'},
                'state': {'label': _('Status'), 'order': 'state'},
                'country': {'label': _('Country'), 'order': 'country_id'},
            }
            if not sortby:
                sortby = 'date'
            order = searchbar_sortings[sortby]['order']

            # Count for pager
            visa_request_count = VisaRequest.search_count(domain)

            # Pager
            pager = portal_pager(
                url="/my/visa_requests",
                url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby, 'search_in': search_in, 'search': search},
                total=visa_request_count,
                page=page,
                step=self._items_per_page
            )

            # Get visa requests
            visa_requests = VisaRequest.search(domain, order=order, limit=self._items_per_page, offset=pager['offset'])

            values.update({
                'date': date_begin,
                'date_end': date_end,
                'visa_requests': visa_requests,
                'page_name': 'visa_request',
                'archive_groups': [],
                'default_url': '/my/visa_requests',
                'pager': pager,
                'searchbar_sortings': searchbar_sortings,
                'sortby': sortby,
                'search_in': search_in,
                'search': search,
                'searchbar_inputs': {
                    'content': {'input': 'content', 'label': _('Search <span class="nolabel"> (in Content)</span>')},
                    'all': {'input': 'all', 'label': _('Search in All')},
                },
            })

            return request.render("travel_agent_management.portal_my_visa_requests", values)

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error in portal_my_visa_requests: {e}")
            return request.redirect('/my')

    @http.route(['/my/visa_request/<int:visa_request_id>'], type='http', auth="public", website=True)
    def portal_my_visa_request(self, visa_request_id=None, access_token=None, **kw):
        try:
            visa_request_sudo = self._document_check_access('visa.request', visa_request_id, access_token)
        except (AccessError, MissingError):
            return request.redirect('/my')

        values = {
            'visa_request': visa_request_sudo,
            'page_name': 'visa_request',
            'user': request.env.user,
        }

        return request.render("travel_agent_management.portal_my_visa_request", values)

    @http.route(['/my/visa_request/new'], type='http', auth="user", website=True)
    def portal_visa_request_new(self, **kw):
        """Display form to create new visa request"""
        values = self._prepare_portal_layout_values()

        # Get available visa types and countries
        visa_types = request.env['visa.type'].search([])
        countries = request.env['visa.country'].search([])

        # Group visa types by country for the dropdown
        visa_types_grouped = []
        for country in countries:
            country_visa_types = visa_types.filtered(lambda vt: vt.country_id.id == country.id)
            if country_visa_types:
                visa_types_grouped.append({
                    'country': country,
                    'visa_types': country_visa_types
                })

        # Prepare visa types data for JavaScript (for document preview)
        visa_types_data = []
        for vt in visa_types:
            doc_types = []
            for doc in vt.document_type_ids:
                doc_types.append({
                    'id': doc.id,
                    'name': doc.name,
                    'description': doc.description or '',
                    'file_type': doc.file_type,
                    'max_file_size': doc.max_file_size,
                    'is_required': doc.is_required,
                })

            visa_types_data.append({
                'id': vt.id,
                'name': vt.name,
                'country_id': vt.country_id.id,
                'visa_category': vt.visa_category,
                'price': vt.price,
                'processing_days': vt.processing_days,
                'document_types': doc_types,
            })

        import json
        values.update({
            'page_name': 'visa_request_new',
            'visa_types': visa_types,
            'visa_types_grouped': visa_types_grouped,
            'countries': countries,
            'visa_types_json': json.dumps(visa_types_data),
            'partner': request.env.user.partner_id,
        })
        return request.render("travel_agent_management.portal_visa_request_new", values)

    # ===== TRANSFER REQUEST PORTAL ROUTES =====

    @http.route(['/my/transfer_request', '/my/transfer_request/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_transfer_requests(self, page=1, date_begin=None, date_end=None, sortby=None, search=None, search_in='content', **kw):
        """Display transfer requests for portal user"""
        values = self._prepare_portal_layout_values()
        TransferRequest = request.env['transfer.request']

        domain = self._get_transfer_requests_domain()

        searchbar_sortings = {
            'date': {'label': _('Newest'), 'order': 'create_date desc'},
            'name': {'label': _('Reference'), 'order': 'name'},
            'departure_date': {'label': _('Departure Date'), 'order': 'departure_date desc'},
        }

        searchbar_inputs = {
            'content': {'input': 'content', 'label': _('Search <span class="nolabel"> (in Content)</span>')},
            'reference': {'input': 'reference', 'label': _('Search in Reference')},
            'route': {'input': 'route', 'label': _('Search in Route')},
        }

        # Default sort order
        if not sortby:
            sortby = 'date'
        order = searchbar_sortings[sortby]['order']

        # Search
        if search and search_in:
            search_domain = []
            if search_in == 'content':
                search_domain = ['|', '|', ('name', 'ilike', search), ('transfer_route', 'ilike', search), ('notes', 'ilike', search)]
            elif search_in == 'reference':
                search_domain = [('name', 'ilike', search)]
            elif search_in == 'route':
                search_domain = [('transfer_route', 'ilike', search)]
            domain += search_domain

        # Date filtering
        if date_begin and date_end:
            domain += [('create_date', '>', date_begin), ('create_date', '<=', date_end)]

        # Count for pager
        transfer_count = TransferRequest.search_count(domain)

        # Pager
        pager = portal_pager(
            url="/my/transfer_request",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby, 'search_in': search_in, 'search': search},
            total=transfer_count,
            page=page,
            step=self._items_per_page
        )

        # Content according to pager and archive selected
        transfers = TransferRequest.search(domain, order=order, limit=self._items_per_page, offset=pager['offset'])
        request.session['my_transfer_requests_history'] = transfers.ids[:100]

        values.update({
            'date': date_begin,
            'date_end': date_end,
            'transfers': transfers,
            'page_name': 'transfer_request',
            'archive_groups': [],
            'default_url': '/my/transfer_request',
            'pager': pager,
            'searchbar_sortings': searchbar_sortings,
            'searchbar_inputs': searchbar_inputs,
            'search_in': search_in,
            'search': search,
            'sortby': sortby,
        })
        return request.render("travel_agent_management.portal_my_transfer_requests", values)

    @http.route(['/my/transfer_request/<int:transfer_id>'], type='http', auth="user", website=True)
    def portal_transfer_request_detail(self, transfer_id, access_token=None, **kw):
        """Display individual transfer request"""
        try:
            transfer_sudo = self._document_check_access('transfer.request', transfer_id, access_token)
        except (AccessError, MissingError):
            return request.redirect('/my')

        values = {
            'transfer': transfer_sudo,
            'page_name': 'transfer_request',
            'user': request.env.user,
        }

        # Handle messages
        message = kw.get('message')
        if message == 'created':
            values['success_message'] = 'Transfer request created successfully!'

        return request.render("travel_agent_management.portal_transfer_request_detail", values)

    @http.route(['/my/transfer_request/new'], type='http', auth="user", website=True)
    def portal_transfer_request_new(self, **kw):
        """Display form to create new transfer request"""
        values = self._prepare_portal_layout_values()

        # Get suppliers for dropdown (use sudo for portal access)
        suppliers = request.env['res.partner'].sudo().search([
            ('is_company', '=', True),
            ('supplier_rank', '>', 0)
        ])

        values.update({
            'page_name': 'transfer_request_new',
            'suppliers': suppliers,
            'partner': request.env.user.partner_id,
        })

        # Handle error messages and preserve form data
        error = kw.get('error')
        if error == 'creation_failed':
            values['error_message'] = 'Failed to create transfer request. Please try again.'
        elif error == 'user_error':
            values['error_message'] = kw.get('message', 'An error occurred. Please try again.')

        # Preserve form data from previous submission
        for field in ['transfer_from', 'transfer_to', 'departure_date', 'departure_time',
                      'arrive_date', 'arrive_time', 'passenger_count', 'vehicle_type',
                      'contact_phone', 'flight_number', 'special_requirements', 'customer_notes', 'supplier_id']:
            if kw.get(field):
                values[field] = kw.get(field)

        return request.render("travel_agent_management.portal_transfer_request_new", values)

    @http.route(['/my/transfer_request/create'], type='http', auth="user", methods=['POST'], csrf=False, website=True)
    def portal_transfer_request_create(self, **post):
        """Create new transfer request from portal"""
        try:
            partner = request.env.user.partner_id

            # Validate required fields
            required_fields = ['transfer_from', 'transfer_to', 'departure_date', 'contact_phone']
            missing_fields = []
            for field in required_fields:
                if not post.get(field):
                    missing_fields.append(field.replace('_', ' ').title())

            if missing_fields:
                raise UserError(_("Please fill in all required fields: %s") % ', '.join(missing_fields))

            # Handle departure time conversion from HH:MM to float
            departure_time = 0.0
            if post.get('departure_time'):
                try:
                    time_str = post.get('departure_time')
                    if ':' in time_str:
                        hours, minutes = time_str.split(':')
                        departure_time = float(hours) + float(minutes) / 60.0
                    else:
                        departure_time = float(time_str)
                except (ValueError, TypeError):
                    departure_time = 0.0

            # Handle arrival time conversion from HH:MM to float
            arrive_time = 0.0
            if post.get('arrive_time'):
                try:
                    time_str = post.get('arrive_time')
                    if ':' in time_str:
                        hours, minutes = time_str.split(':')
                        arrive_time = float(hours) + float(minutes) / 60.0
                    else:
                        arrive_time = float(time_str)
                except (ValueError, TypeError):
                    arrive_time = 0.0

            # Handle supplier - if none selected, find a default one
            supplier_id = False
            if post.get('supplier_id'):
                supplier_id = int(post.get('supplier_id'))
            else:
                # Find a default supplier if none selected
                default_supplier = request.env['res.partner'].sudo().search([
                    ('is_company', '=', True),
                    ('supplier_rank', '>', 0)
                ], limit=1)
                if default_supplier:
                    supplier_id = default_supplier.id
                else:
                    # If no suppliers exist, we need to handle this case
                    raise UserError(_("No suppliers found. Please contact administrator to add suppliers."))

            # Prepare values
            vals = {
                'customer_id': partner.commercial_partner_id.id,
                'transfer_from': post.get('transfer_from'),
                'transfer_to': post.get('transfer_to'),
                'departure_date': post.get('departure_date'),
                'departure_time': departure_time,
                'arrive_date': post.get('arrive_date') if post.get('arrive_date') else False,
                'arrive_time': arrive_time if post.get('arrive_time') else False,
                'passenger_count': int(post.get('passenger_count', 1)),
                'vehicle_type': post.get('vehicle_type'),
                'contact_phone': post.get('contact_phone'),
                'flight_number': post.get('flight_number'),
                'special_requirements': post.get('special_requirements'),
                'supplier_id': supplier_id,
                'customer_notes': post.get('customer_notes', ''),
            }

            # Create transfer request
            transfer_request = request.env['transfer.request'].sudo().create(vals)

            # Redirect with success message
            return request.redirect(f'/my/transfer_request/{transfer_request.id}?message=created')

        except UserError as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"User error creating transfer request: {e}")
            # Build query string with form data to preserve it
            query_params = ['error=user_error', f'message={str(e)}']
            for key, value in post.items():
                if value and key not in ['csrf_token']:
                    query_params.append(f'{key}={value}')
            return request.redirect(f'/my/transfer_request/new?{"&".join(query_params)}')
        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error creating transfer request: {e}")
            # Build query string with form data to preserve it
            query_params = ['error=creation_failed']
            for key, value in post.items():
                if value and key not in ['csrf_token']:
                    query_params.append(f'{key}={value}')
            return request.redirect(f'/my/transfer_request/new?{"&".join(query_params)}')

    @http.route(['/my/visa_request/create'], type='http', auth="user", methods=['POST'], csrf=False, website=True)
    def portal_visa_request_create(self, **post):
        """Create new visa request from portal"""
        try:
            partner = request.env.user.partner_id

            # Prepare values
            vals = {
                'partner_id': partner.commercial_partner_id.id,
                'visa_type_id': int(post.get('visa_type_id')),
                'notes': post.get('notes', ''),
            }

            # Create visa request
            visa_request = request.env['visa.request'].sudo().create(vals)

            # Auto-create required document placeholders
            visa_request.action_create_documents()

            # Process uploaded files
            uploaded_files = 0
            for field_name, field_value in post.items():
                if field_name.startswith('document_') and hasattr(field_value, 'filename'):
                    try:
                        # Extract document type ID from field name
                        doc_type_id = int(field_name.replace('document_', ''))

                        # Read file content
                        file_content = field_value.read()
                        filename = field_value.filename

                        if file_content and filename:
                            # Find existing document record and update it
                            existing_doc = request.env['visa.request.document'].sudo().search([
                                ('request_id', '=', visa_request.id),
                                ('document_type_id', '=', doc_type_id)
                            ], limit=1)

                            if existing_doc:
                                # Update existing document
                                existing_doc.write({
                                    'datas': base64.b64encode(file_content),
                                    'filename': filename,
                                })
                                uploaded_files += 1
                            else:
                                # Create new document record if not found
                                document_vals = {
                                    'request_id': visa_request.id,
                                    'document_type_id': doc_type_id,
                                    'datas': base64.b64encode(file_content),
                                    'filename': filename,
                                }
                                request.env['visa.request.document'].sudo().create(document_vals)
                                uploaded_files += 1

                    except Exception as file_error:
                        import logging
                        _logger = logging.getLogger(__name__)
                        _logger.warning(f"Error uploading file {field_name}: {file_error}")
                        continue

            # Redirect with success message
            if uploaded_files > 0:
                return request.redirect(f'/my/visa_request/{visa_request.id}?message=created_with_files&files={uploaded_files}')
            else:
                return request.redirect(f'/my/visa_request/{visa_request.id}?message=created')

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error creating visa request: {e}")
            return request.redirect('/my/visa_request/new?error=creation_failed')

    # ===== HOTEL BOOKING REQUEST PORTAL ROUTES =====

    @http.route(['/my/hotel_booking', '/my/hotel_booking/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_hotel_bookings(self, page=1, date_begin=None, date_end=None, sortby=None, search=None, search_in='content', **kw):
        """Display hotel booking requests for portal user"""
        values = self._prepare_portal_layout_values()
        HotelBookingRequest = request.env['hotel.booking.request']

        domain = self._get_hotel_booking_requests_domain()

        searchbar_sortings = {
            'date': {'label': _('Newest'), 'order': 'create_date desc'},
            'name': {'label': _('Reference'), 'order': 'name'},
            'check_in': {'label': _('Check-in Date'), 'order': 'check_in_date desc'},
        }

        searchbar_inputs = {
            'content': {'input': 'content', 'label': _('Search <span class="nolabel"> (in Content)</span>')},
            'reference': {'input': 'reference', 'label': _('Search in Reference')},
            'hotel': {'input': 'hotel', 'label': _('Search in Hotel')},
        }

        # Default sort order
        if not sortby:
            sortby = 'date'
        order = searchbar_sortings[sortby]['order']

        # Search
        if search and search_in:
            search_domain = []
            if search_in == 'content':
                search_domain = ['|', '|', ('name', 'ilike', search), ('hotel_name', 'ilike', search), ('notes', 'ilike', search)]
            elif search_in == 'reference':
                search_domain = [('name', 'ilike', search)]
            elif search_in == 'hotel':
                search_domain = [('hotel_name', 'ilike', search)]
            domain += search_domain

        # Date filtering
        if date_begin and date_end:
            domain += [('create_date', '>', date_begin), ('create_date', '<=', date_end)]

        # Count for pager
        hotel_count = HotelBookingRequest.search_count(domain)

        # Pager
        pager = portal_pager(
            url="/my/hotel_booking",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby, 'search_in': search_in, 'search': search},
            total=hotel_count,
            page=page,
            step=self._items_per_page
        )

        # Content according to pager and archive selected
        hotel_bookings = HotelBookingRequest.search(domain, order=order, limit=self._items_per_page, offset=pager['offset'])
        request.session['my_hotel_bookings_history'] = hotel_bookings.ids[:100]

        values.update({
            'date': date_begin,
            'date_end': date_end,
            'hotel_bookings': hotel_bookings,
            'page_name': 'hotel_booking',
            'archive_groups': [],
            'default_url': '/my/hotel_booking',
            'pager': pager,
            'searchbar_sortings': searchbar_sortings,
            'searchbar_inputs': searchbar_inputs,
            'search_in': search_in,
            'search': search,
            'sortby': sortby,
        })
        return request.render("travel_agent_management.portal_my_hotel_bookings", values)

    @http.route(['/my/hotel_booking/<int:booking_id>'], type='http', auth="user", website=True)
    def portal_hotel_booking_detail(self, booking_id, access_token=None, **kw):
        """Display individual hotel booking request"""
        try:
            booking_sudo = self._document_check_access('hotel.booking.request', booking_id, access_token)
        except (AccessError, MissingError):
            return request.redirect('/my')

        values = {
            'booking': booking_sudo,
            'page_name': 'hotel_booking',
            'user': request.env.user,
        }

        # Handle messages
        message = kw.get('message')
        if message == 'created':
            values['success_message'] = 'Hotel booking request created successfully!'

        return request.render("travel_agent_management.portal_hotel_booking_detail", values)

    @http.route(['/my/hotel_booking/new'], type='http', auth="user", website=True)
    def portal_hotel_booking_new(self, **kw):
        """Display form to create new hotel booking request"""
        values = self._prepare_portal_layout_values()

        # Get suppliers for dropdown (use sudo for portal access)
        suppliers = request.env['res.partner'].sudo().search([
            ('is_company', '=', True),
            ('supplier_rank', '>', 0)
        ])

        values.update({
            'page_name': 'hotel_booking_new',
            'suppliers': suppliers,
            'partner': request.env.user.partner_id,
        })

        # Handle error messages and preserve form data
        error = kw.get('error')
        if error == 'creation_failed':
            values['error_message'] = 'Failed to create hotel booking request. Please try again.'
        elif error == 'user_error':
            values['error_message'] = kw.get('message', 'An error occurred. Please try again.')

        # Preserve form data from previous submission
        for field in ['city_name', 'hotel_name', 'check_in_date', 'check_out_date',
                      'number_of_pax', 'room_type', 'board', 'special_request',
                      'customer_notes', 'supplier_id']:
            if kw.get(field):
                values[field] = kw.get(field)

        return request.render("travel_agent_management.portal_hotel_booking_new", values)

    @http.route(['/my/hotel_booking/create'], type='http', auth="user", methods=['POST'], csrf=False, website=True)
    def portal_hotel_booking_create(self, **post):
        """Create new hotel booking request from portal"""
        try:
            partner = request.env.user.partner_id

            # Validate required fields
            required_fields = ['city_name', 'hotel_name', 'check_in_date', 'check_out_date', 'number_of_pax']
            missing_fields = []
            for field in required_fields:
                if not post.get(field):
                    missing_fields.append(field.replace('_', ' ').title())

            if missing_fields:
                raise UserError(_("Please fill in all required fields: %s") % ', '.join(missing_fields))

            # Handle supplier - if none selected, find a default one
            supplier_id = False
            if post.get('supplier_id'):
                supplier_id = int(post.get('supplier_id'))
            else:
                # Find a default supplier if none selected
                default_supplier = request.env['res.partner'].sudo().search([
                    ('is_company', '=', True),
                    ('supplier_rank', '>', 0)
                ], limit=1)
                if default_supplier:
                    supplier_id = default_supplier.id
                else:
                    # If no suppliers exist, we need to handle this case
                    raise UserError(_("No suppliers found. Please contact administrator to add suppliers."))

            # Prepare values
            vals = {
                'customer_id': partner.commercial_partner_id.id,
                'city_name': post.get('city_name'),
                'hotel_name': post.get('hotel_name'),
                'check_in_date': post.get('check_in_date'),
                'check_out_date': post.get('check_out_date'),
                'number_of_pax': int(post.get('number_of_pax', 1)),
                'room_type': post.get('room_type'),
                'board': post.get('board'),
                'special_request': post.get('special_request'),
                'supplier_id': supplier_id,
                'customer_notes': post.get('customer_notes', ''),
            }

            # Create hotel booking request
            booking_request = request.env['hotel.booking.request'].sudo().create(vals)

            # Redirect with success message
            return request.redirect(f'/my/hotel_booking/{booking_request.id}?message=created')

        except UserError as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"User error creating hotel booking request: {e}")
            # Build query string with form data to preserve it
            query_params = ['error=user_error', f'message={str(e)}']
            for key, value in post.items():
                if value and key not in ['csrf_token']:
                    query_params.append(f'{key}={value}')
            return request.redirect(f'/my/hotel_booking/new?{"&".join(query_params)}')
        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error creating hotel booking request: {e}")
            # Build query string with form data to preserve it
            query_params = ['error=creation_failed']
            for key, value in post.items():
                if value and key not in ['csrf_token']:
                    query_params.append(f'{key}={value}')
            return request.redirect(f'/my/hotel_booking/new?{"&".join(query_params)}')

    @http.route(['/my/visa_request/<int:visa_request_id>/upload'], type='http', auth="user", methods=['POST'], csrf=False, website=True)
    def portal_visa_request_upload(self, visa_request_id, **post):
        """Upload documents to existing visa request"""
        try:
            visa_request = request.env['visa.request'].browse(visa_request_id)

            # Check access
            if not visa_request.exists():
                return request.redirect('/my/visa_requests?error=not_found')

            partner = request.env.user.partner_id
            if visa_request.partner_id.commercial_partner_id.id != partner.commercial_partner_id.id:
                return request.redirect('/my/visa_requests?error=access_denied')

            # Process file uploads
            uploaded_count = 0
            for field_name, field_value in post.items():
                if field_name.startswith('document_') and hasattr(field_value, 'filename'):
                    # Extract document ID from field name (document_123)
                    try:
                        document_id = int(field_name.replace('document_', ''))
                        document = request.env['visa.request.document'].browse(document_id)

                        if document.exists() and document.request_id.id == visa_request_id:
                            # Read file content
                            file_content = field_value.read()
                            filename = field_value.filename

                            # Update document with file
                            document.sudo().write({
                                'datas': base64.b64encode(file_content),
                                'filename': filename,
                            })
                            uploaded_count += 1

                    except (ValueError, Exception) as e:
                        import logging
                        _logger = logging.getLogger(__name__)
                        _logger.error(f"Error uploading document {field_name}: {e}")
                        continue

            if uploaded_count > 0:
                return request.redirect(f'/my/visa_request/{visa_request_id}?message=uploaded&count={uploaded_count}')
            else:
                return request.redirect(f'/my/visa_request/{visa_request_id}?error=no_files')

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error in visa request upload: {e}")
            return request.redirect(f'/my/visa_request/{visa_request_id}?error=upload_failed')

    @http.route(['/my/visa_request/debug'], type='http', auth="user", website=True)
    def portal_visa_request_debug(self, **kw):
        """Debug route to check visa data"""
        visa_types = request.env['visa.type'].search([])
        countries = request.env['visa.country'].search([])

        debug_info = {
            'countries_count': len(countries),
            'visa_types_count': len(visa_types),
            'countries': [(c.id, c.name) for c in countries],
            'visa_types': [(vt.id, vt.name, vt.country_id.name) for vt in visa_types],
        }

        import json
        return f"<pre>{json.dumps(debug_info, indent=2)}</pre>"

    @http.route(['/my/visa_request/get_visa_types'], type='json', auth="user", website=True)
    def get_visa_types_for_country(self, country_id=None):
        """AJAX endpoint to get visa types for a country"""
        if not country_id:
            return {'visa_types': []}

        visa_types = request.env['visa.type'].search([('country_id', '=', int(country_id))])

        result = []
        for vt in visa_types:
            doc_types = []
            for doc in vt.document_type_ids:
                doc_types.append({
                    'id': doc.id,
                    'name': doc.name,
                    'description': doc.description or '',
                    'file_type': doc.file_type,
                    'max_file_size': doc.max_file_size,
                    'is_required': doc.is_required,
                })

            result.append({
                'id': vt.id,
                'name': vt.name,
                'country_id': vt.country_id.id,
                'visa_category': vt.visa_category,
                'price': vt.price,
                'processing_days': vt.processing_days,
                'document_types': doc_types,
            })

        return {'visa_types': result}

    # ===== MEDICAL INSURANCE REQUEST PORTAL ROUTES =====

    @http.route(['/my/medical_insurance', '/my/medical_insurance/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_medical_insurance(self, page=1, date_begin=None, date_end=None, sortby=None, search=None, search_in='content', **kw):
        """Display medical insurance requests for portal user"""
        values = self._prepare_portal_layout_values()
        MedicalInsuranceRequest = request.env['medical.insurance.request']

        domain = self._get_medical_insurance_requests_domain()

        searchbar_sortings = {
            'date': {'label': _('Newest'), 'order': 'create_date desc'},
            'name': {'label': _('Reference'), 'order': 'name'},
            'arrive_date': {'label': _('Arrival Date'), 'order': 'arrive_date desc'},
        }

        searchbar_inputs = {
            'content': {'input': 'content', 'label': _('Search <span class="nolabel"> (in Content)</span>')},
            'reference': {'input': 'reference', 'label': _('Search in Reference')},
            'country': {'input': 'country', 'label': _('Search in Country')},
        }

        # Default sort order
        if not sortby:
            sortby = 'date'
        order = searchbar_sortings[sortby]['order']

        # Search
        if search and search_in:
            search_domain = []
            if search_in == 'content':
                search_domain = ['|', '|', ('name', 'ilike', search), ('travel_country', 'ilike', search), ('customer_notes', 'ilike', search)]
            elif search_in == 'reference':
                search_domain = [('name', 'ilike', search)]
            elif search_in == 'country':
                search_domain = [('travel_country', 'ilike', search)]
            domain += search_domain

        # Date filtering
        if date_begin and date_end:
            domain += [('create_date', '>', date_begin), ('create_date', '<=', date_end)]

        # Count for pager
        insurance_count = MedicalInsuranceRequest.search_count(domain)

        # Pager
        pager = portal_pager(
            url="/my/medical_insurance",
            url_args={'date_begin': date_begin, 'date_end': date_end, 'sortby': sortby, 'search_in': search_in, 'search': search},
            total=insurance_count,
            page=page,
            step=self._items_per_page
        )

        # Content according to pager and archive selected
        insurance_requests = MedicalInsuranceRequest.search(domain, order=order, limit=self._items_per_page, offset=pager['offset'])
        request.session['my_medical_insurance_history'] = insurance_requests.ids[:100]

        values.update({
            'date': date_begin,
            'date_end': date_end,
            'insurance_requests': insurance_requests,
            'page_name': 'medical_insurance',
            'archive_groups': [],
            'default_url': '/my/medical_insurance',
            'pager': pager,
            'searchbar_sortings': searchbar_sortings,
            'searchbar_inputs': searchbar_inputs,
            'search_in': search_in,
            'search': search,
            'sortby': sortby,
        })
        return request.render("travel_agent_management.portal_my_medical_insurance", values)

    @http.route(['/my/medical_insurance/<int:insurance_id>'], type='http', auth="user", website=True)
    def portal_medical_insurance_detail(self, insurance_id, access_token=None, **kw):
        """Display individual medical insurance request"""
        try:
            insurance_sudo = self._document_check_access('medical.insurance.request', insurance_id, access_token)
        except (AccessError, MissingError):
            return request.redirect('/my')

        values = {
            'insurance': insurance_sudo,
            'page_name': 'medical_insurance',
            'user': request.env.user,
        }

        # Handle messages
        message = kw.get('message')
        if message == 'created':
            values['success_message'] = 'Medical insurance request created successfully!'

        return request.render("travel_agent_management.portal_medical_insurance_detail", values)

    @http.route(['/my/medical_insurance/new'], type='http', auth="user", website=True)
    def portal_medical_insurance_new(self, **kw):
        """Display form to create new medical insurance request"""
        values = self._prepare_portal_layout_values()

        # Get suppliers for dropdown (use sudo for portal access)
        suppliers = request.env['res.partner'].sudo().search([
            ('is_company', '=', True),
            ('supplier_rank', '>', 0)
        ])

        values.update({
            'page_name': 'medical_insurance_new',
            'suppliers': suppliers,
            'partner': request.env.user.partner_id,
        })

        # Handle error messages and preserve form data
        error = kw.get('error')
        if error == 'creation_failed':
            values['error_message'] = 'Failed to create medical insurance request. Please try again.'
        elif error == 'user_error':
            values['error_message'] = kw.get('message', 'An error occurred. Please try again.')

        # Preserve form data from previous submission
        for field in ['travel_country', 'nationality', 'arrive_date', 'departure_date', 'passport_no', 'tel_no',
                      'address', 'insurance_type', 'coverage_amount', 'special_conditions', 'customer_notes', 'supplier_id']:
            if kw.get(field):
                values[field] = kw.get(field)

        return request.render("travel_agent_management.portal_medical_insurance_new", values)

    @http.route(['/my/medical_insurance/create'], type='http', auth="user", methods=['POST'], csrf=False, website=True)
    def portal_medical_insurance_create(self, **post):
        """Create new medical insurance request from portal"""
        try:
            partner = request.env.user.partner_id

            # Validate required fields
            required_fields = ['travel_country', 'nationality', 'arrive_date', 'departure_date', 'passport_no', 'tel_no']
            missing_fields = []
            for field in required_fields:
                if not post.get(field):
                    missing_fields.append(field.replace('_', ' ').title())

            if missing_fields:
                raise UserError(_("Please fill in all required fields: %s") % ', '.join(missing_fields))

            # Handle supplier - if none selected, find a default one
            supplier_id = False
            if post.get('supplier_id'):
                supplier_id = int(post.get('supplier_id'))
            else:
                # Find a default supplier if none selected
                default_supplier = request.env['res.partner'].sudo().search([
                    ('is_company', '=', True),
                    ('supplier_rank', '>', 0)
                ], limit=1)
                if default_supplier:
                    supplier_id = default_supplier.id
                else:
                    # If no suppliers exist, we need to handle this case
                    raise UserError(_("No suppliers found. Please contact administrator to add suppliers."))

            # Prepare values
            vals = {
                'customer_id': partner.commercial_partner_id.id,
                'travel_country': post.get('travel_country'),
                'nationality': post.get('nationality'),
                'arrive_date': post.get('arrive_date'),
                'departure_date': post.get('departure_date'),
                'passport_no': post.get('passport_no'),
                'tel_no': post.get('tel_no'),
                'address': post.get('address', ''),
                'insurance_type': post.get('insurance_type'),
                'coverage_amount': float(post.get('coverage_amount', 0)) if post.get('coverage_amount') else 0.0,
                'special_conditions': post.get('special_conditions'),
                'customer_notes': post.get('customer_notes', ''),
                'supplier_id': supplier_id,
            }

            # Create medical insurance request
            insurance_request = request.env['medical.insurance.request'].sudo().create(vals)

            # Redirect with success message
            return request.redirect(f'/my/medical_insurance/{insurance_request.id}?message=created')

        except UserError as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"User error creating medical insurance request: {e}")
            # Build query string with form data to preserve it
            query_params = ['error=user_error', f'message={str(e)}']
            for key, value in post.items():
                if value and key not in ['csrf_token']:
                    query_params.append(f'{key}={value}')
            return request.redirect(f'/my/medical_insurance/new?{"&".join(query_params)}')
        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error creating medical insurance request: {e}")
            # Build query string with form data to preserve it
            query_params = ['error=creation_failed']
            for key, value in post.items():
                if value and key not in ['csrf_token']:
                    query_params.append(f'{key}={value}')
            return request.redirect(f'/my/medical_insurance/new?{"&".join(query_params)}')