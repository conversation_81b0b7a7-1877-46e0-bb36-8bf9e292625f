# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, timedelta


class MedicalInsuranceRequest(models.Model):
    _name = 'medical.insurance.request'
    _description = 'Medical Insurance Request'
    _order = 'create_date desc'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'portal.mixin']

    # Basic Information
    name = fields.Char('Reference', required=True, copy=False, readonly=True, 
                       default=lambda self: _('New'))
    
    # Customer Information
    customer_id = fields.Many2one('res.partner', string='Customer', required=True,
                                  domain=[('is_company', '=', False)], tracking=True)
    partner_id = fields.Many2one('res.partner', string='Partner', related='customer_id', store=True,
                                 help="Portal access partner (same as customer)")
    
    # Travel Details
    travel_country = fields.Char('Travel Country', required=True, tracking=True,
                                 help="Country of travel destination")
    nationality = fields.Char('Nationality', required=True, tracking=True,
                              help="Nationality of the traveler")
    
    # Travel Dates
    arrive_date = fields.Date('Arrive Date', required=True, tracking=True,
                              help="Date of arrival to destination country")
    departure_date = fields.Date('Departure Date', required=True, tracking=True,
                                 help="Date of departure from destination country")
    
    # Personal Information
    passport_no = fields.Char('Passport No', required=True, tracking=True,
                              help="Passport number of the traveler")
    tel_no = fields.Char('Tel No', required=True, tracking=True,
                         help="Contact telephone number")
    
    # Additional Details
    duration = fields.Integer('Duration (Days)', compute='_compute_duration', store=True,
                              help="Duration of travel in days")
    address = fields.Text('Address', help="Address of the traveler")
    
    # Insurance Details
    insurance_type = fields.Selection([
        ('basic', 'Basic Coverage'),
        ('comprehensive', 'Comprehensive Coverage'),
        ('premium', 'Premium Coverage'),
        ('family', 'Family Coverage'),
        ('group', 'Group Coverage'),
    ], string='Insurance Type', tracking=True, help="Type of insurance coverage")
    
    coverage_amount = fields.Monetary('Coverage Amount', currency_field='currency_id',
                                      help="Maximum coverage amount")
    
    # Supplier Information
    supplier_id = fields.Many2one('res.partner', string='Insurance Provider', required=True,
                                  domain=[('is_company', '=', True), ('supplier_rank', '>', 0)],
                                  tracking=True)
    
    # Pricing
    supplier_cost = fields.Monetary('Supplier Cost', currency_field='currency_id', tracking=True,
                                    help="Cost charged by the insurance provider")
    customer_price = fields.Monetary('Customer Price', currency_field='currency_id', tracking=True,
                                     help="Price charged to the customer")
    profit_amount = fields.Monetary('Profit', currency_field='currency_id', 
                                    compute='_compute_profit', store=True,
                                    help="Profit = Customer Price - Supplier Cost")
    profit_percentage = fields.Float('Profit %', compute='_compute_profit', store=True,
                                     help="Profit percentage based on supplier cost")
    
    currency_id = fields.Many2one('res.currency', string='Currency', 
                                  default=lambda self: self.env.company.currency_id)
    
    # Status and Workflow
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', tracking=True)
    
    # Additional Information
    notes = fields.Text('Internal Notes')
    customer_notes = fields.Text('Customer Notes', help="Notes visible to customer")
    special_conditions = fields.Text('Special Conditions',
                                     help="Any special conditions or requirements for the insurance")
    
    # Computed Fields for Display
    insurance_summary = fields.Char('Insurance Summary', compute='_compute_insurance_summary', store=True)
    supplier_name = fields.Char('Supplier Name', compute='_compute_supplier_name')
    
    # Company
    company_id = fields.Many2one('res.company', string='Company', 
                                 default=lambda self: self.env.company)
    
    # Related Orders
    sale_order_id = fields.Many2one('sale.order', string='Sales Order', readonly=True,
                                    help="Sales order created for customer invoice")
    purchase_order_id = fields.Many2one('purchase.order', string='Purchase Order', readonly=True,
                                        help="Purchase order created for vendor bill")

    @api.model
    def create(self, vals):
        """Override create to generate sequence number"""
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('medical.insurance.request') or _('New')
        return super().create(vals)

    @api.depends('travel_country', 'nationality', 'arrive_date', 'departure_date')
    def _compute_insurance_summary(self):
        """Compute insurance summary for display"""
        for record in self:
            if record.travel_country and record.nationality:
                summary = f"{record.nationality} → {record.travel_country}"
                if record.arrive_date and record.departure_date:
                    summary += f" ({record.arrive_date} - {record.departure_date})"
                record.insurance_summary = summary
            else:
                record.insurance_summary = ""

    @api.depends('arrive_date', 'departure_date')
    def _compute_duration(self):
        """Compute duration in days"""
        for record in self:
            if record.arrive_date and record.departure_date:
                delta = record.departure_date - record.arrive_date
                record.duration = delta.days + 1  # Include both start and end dates
            else:
                record.duration = 0

    @api.depends('supplier_cost', 'customer_price')
    def _compute_profit(self):
        """Compute profit amount and percentage"""
        for record in self:
            record.profit_amount = record.customer_price - record.supplier_cost
            if record.supplier_cost > 0:
                record.profit_percentage = (record.profit_amount / record.supplier_cost) * 100
            else:
                record.profit_percentage = 0.0

    def _compute_supplier_name(self):
        """Safely compute supplier name for portal access"""
        for record in self:
            try:
                record.supplier_name = record.supplier_id.name if record.supplier_id else 'Not specified'
            except:
                record.supplier_name = 'Not specified'

    @api.constrains('arrive_date', 'departure_date')
    def _check_dates(self):
        """Validate travel dates"""
        for record in self:
            if record.arrive_date and record.departure_date:
                if record.departure_date <= record.arrive_date:
                    raise ValidationError(_("Departure date must be after arrival date."))
                if record.arrive_date < fields.Date.today():
                    raise ValidationError(_("Arrival date cannot be in the past."))

    @api.constrains('supplier_cost', 'customer_price')
    def _check_pricing(self):
        """Validate pricing"""
        for record in self:
            if record.supplier_cost < 0:
                raise ValidationError(_("Supplier cost cannot be negative."))
            if record.customer_price < 0:
                raise ValidationError(_("Customer price cannot be negative."))

    @api.constrains('coverage_amount')
    def _check_coverage_amount(self):
        """Validate coverage amount"""
        for record in self:
            if record.coverage_amount and record.coverage_amount <= 0:
                raise ValidationError(_("Coverage amount must be greater than 0."))

    def name_get(self):
        """Custom name_get to show reference and insurance summary"""
        result = []
        for record in self:
            name = f"{record.name}"
            if record.insurance_summary:
                name += f" - {record.insurance_summary}"
            result.append((record.id, name))
        return result

    def action_confirm(self):
        """Confirm the medical insurance request"""
        for record in self:
            if record.state != 'draft':
                raise UserError(_("Only draft requests can be confirmed."))
            
            # Validate required fields before confirmation
            record._validate_for_confirmation()
            
            record.state = 'confirmed'
            record.message_post(
                body=_("Medical insurance request confirmed."),
                subject=_("Medical Insurance Request Confirmed")
            )

    def action_cancel(self):
        """Cancel the medical insurance request"""
        for record in self:
            if record.state == 'cancelled':
                raise UserError(_("Request is already cancelled."))
            
            record.state = 'cancelled'
            record.message_post(
                body=_("Medical insurance request cancelled."),
                subject=_("Medical Insurance Request Cancelled")
            )

    def action_set_to_draft(self):
        """Set medical insurance request back to draft"""
        for record in self:
            record.state = 'draft'
            record.message_post(
                body=_("Medical insurance request set to draft."),
                subject=_("Medical Insurance Request Reset")
            )

    def _validate_for_confirmation(self):
        """Validate all required data before confirmation"""
        self.ensure_one()
        errors = []
        
        if not self.customer_id:
            errors.append(_("Customer is required"))
        if not self.supplier_id:
            errors.append(_("Insurance provider is required"))
        if not self.travel_country:
            errors.append(_("Travel country is required"))
        if not self.nationality:
            errors.append(_("Nationality is required"))
        if not self.arrive_date:
            errors.append(_("Arrival date is required"))
        if not self.departure_date:
            errors.append(_("Departure date is required"))
        if not self.passport_no:
            errors.append(_("Passport number is required"))
        if not self.tel_no:
            errors.append(_("Telephone number is required"))
        
        if errors:
            raise ValidationError(_("Cannot confirm request:\n") + "\n".join(errors))

    @api.onchange('arrive_date')
    def _onchange_arrive_date(self):
        """Set departure date one day after arrival by default"""
        if self.arrive_date and not self.departure_date:
            self.departure_date = self.arrive_date + timedelta(days=1)

    @api.onchange('supplier_cost')
    def _onchange_supplier_cost(self):
        """Auto-suggest customer price when supplier cost changes"""
        if self.supplier_cost and not self.customer_price:
            # Default margin of 20%
            self.customer_price = self.supplier_cost * 1.20

    def calculate_suggested_price(self):
        """Calculate suggested customer price based on supplier cost and default margin"""
        for record in self:
            if record.supplier_cost > 0:
                # Default margin of 20%
                default_margin = 0.20
                suggested_price = record.supplier_cost * (1 + default_margin)
                return suggested_price
            return 0.0

    # ===== INVOICING METHODS =====

    def action_create_invoice(self):
        """Create customer sales order and invoice"""
        self.ensure_one()
        if not self.customer_price:
            raise UserError(_("Customer price must be set to create invoice."))
        if not self.customer_id:
            raise UserError(_("Customer must be set to create invoice."))

        # Check if sales order already exists
        if self.sale_order_id:
            return self.action_view_sale_order()

        # Create sales order
        sale_order = self._create_sale_order()

        # Open the created sales order
        return {
            'type': 'ir.actions.act_window',
            'name': _('Sales Order'),
            'view_mode': 'form',
            'res_model': 'sale.order',
            'res_id': sale_order.id,
            'target': 'current',
        }

    def action_create_vendor_bill(self):
        """Create vendor purchase order and bill"""
        self.ensure_one()
        if not self.supplier_cost:
            raise UserError(_("Supplier cost must be set to create vendor bill."))
        if not self.supplier_id:
            raise UserError(_("Insurance provider must be set to create vendor bill."))

        # Check if purchase order already exists
        if self.purchase_order_id:
            return self.action_view_purchase_order()

        # Create purchase order
        purchase_order = self._create_purchase_order()

        # Open the created purchase order
        return {
            'type': 'ir.actions.act_window',
            'name': _('Purchase Order'),
            'view_mode': 'form',
            'res_model': 'purchase.order',
            'res_id': purchase_order.id,
            'target': 'current',
        }

    def _create_sale_order(self):
        """Create sales order for customer invoice"""
        self.ensure_one()

        # Get or create service product
        service_product = self.env['product.product'].search([
            ('name', '=', 'Medical Insurance Service'),
            ('type', '=', 'service')
        ], limit=1)

        if not service_product:
            service_product = self.env['product.product'].create({
                'name': 'Medical Insurance Service',
                'type': 'service',
                'categ_id': self.env.ref('product.product_category_all').id,
                'sale_ok': True,
                'purchase_ok': False,
                'list_price': 0.0,
            })

        # Create order line description
        description = f"Medical Insurance - {self.insurance_summary}"
        if self.duration > 0:
            description += f" for {self.duration} days"
        if self.insurance_type:
            description += f" ({dict(self._fields['insurance_type'].selection)[self.insurance_type]})"
        if self.coverage_amount:
            description += f" - Coverage: {self.coverage_amount:,.0f} {self.currency_id.name}"

        order_lines = [(0, 0, {
            'product_id': service_product.id,
            'name': description,
            'product_uom_qty': 1,
            'price_unit': self.customer_price,
            'product_uom': service_product.uom_id.id,
        })]

        so_vals = {
            'partner_id': self.customer_id.id,
            'origin': self.name,
            'order_line': order_lines,
            'currency_id': self.currency_id.id,
            'pricelist_id': self.customer_id.property_product_pricelist.id or 1,
        }

        sale_order = self.env['sale.order'].create(so_vals)
        self.sale_order_id = sale_order.id

        self.message_post(
            body=_("Sales Order %s created for customer %s - Amount: %s %s") %
                 (sale_order.name, self.customer_id.name, self.customer_price, self.currency_id.name)
        )

        return sale_order

    def _create_purchase_order(self):
        """Create purchase order for vendor bill"""
        self.ensure_one()

        # Get or create service product
        service_product = self.env['product.product'].search([
            ('name', '=', 'Medical Insurance Service'),
            ('type', '=', 'service')
        ], limit=1)

        if not service_product:
            service_product = self.env['product.product'].create({
                'name': 'Medical Insurance Service',
                'type': 'service',
                'categ_id': self.env.ref('product.product_category_all').id,
                'sale_ok': False,
                'purchase_ok': True,
                'standard_price': 0.0,
            })

        # Create order line description
        description = f"Medical Insurance - {self.insurance_summary}"
        if self.duration > 0:
            description += f" for {self.duration} days"
        if self.insurance_type:
            description += f" ({dict(self._fields['insurance_type'].selection)[self.insurance_type]})"
        if self.coverage_amount:
            description += f" - Coverage: {self.coverage_amount:,.0f} {self.currency_id.name}"

        order_lines = [(0, 0, {
            'product_id': service_product.id,
            'name': description,
            'product_qty': 1,
            'price_unit': self.supplier_cost,
            'product_uom': service_product.uom_po_id.id,
            'date_planned': self.arrive_date or fields.Datetime.now(),
        })]

        po_vals = {
            'partner_id': self.supplier_id.id,
            'origin': self.name,
            'order_line': order_lines,
            'currency_id': self.currency_id.id,
        }

        purchase_order = self.env['purchase.order'].create(po_vals)
        self.purchase_order_id = purchase_order.id

        self.message_post(
            body=_("Purchase Order %s created for insurance provider %s - Amount: %s %s") %
                 (purchase_order.name, self.supplier_id.name, self.supplier_cost, self.currency_id.name)
        )

        return purchase_order

    def action_view_sale_order(self):
        """Open the related sales order"""
        self.ensure_one()
        if not self.sale_order_id:
            return

        return {
            'type': 'ir.actions.act_window',
            'name': _('Sales Order'),
            'view_mode': 'form',
            'res_model': 'sale.order',
            'res_id': self.sale_order_id.id,
            'target': 'current',
        }

    def action_view_purchase_order(self):
        """Open the related purchase order"""
        self.ensure_one()
        if not self.purchase_order_id:
            return

        return {
            'type': 'ir.actions.act_window',
            'name': _('Purchase Order'),
            'view_mode': 'form',
            'res_model': 'purchase.order',
            'res_id': self.purchase_order_id.id,
            'target': 'current',
        }

    def action_duplicate_request(self):
        """Duplicate medical insurance request with new reference"""
        self.ensure_one()
        new_request = self.copy({
            'name': _('New'),
            'state': 'draft',
        })
        return {
            'type': 'ir.actions.act_window',
            'name': _('Medical Insurance Request'),
            'res_model': 'medical.insurance.request',
            'res_id': new_request.id,
            'view_mode': 'form',
            'target': 'current',
        }
