# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, timedelta


class TransferRequest(models.Model):
    _name = 'transfer.request'
    _description = 'Transfer Request'
    _order = 'create_date desc'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'portal.mixin']

    # Basic Information
    name = fields.Char('Reference', required=True, copy=False, readonly=True, 
                       default=lambda self: _('New'))
    
    # Customer Information
    customer_id = fields.Many2one('res.partner', string='Customer', required=True,
                                  domain=[('is_company', '=', False)], tracking=True)
    partner_id = fields.Many2one('res.partner', string='Partner', related='customer_id', store=True,
                                 help="Portal access partner (same as customer)")
    customer_phone = fields.Char('Customer Phone', related='customer_id.phone', readonly=True)
    customer_email = fields.Char('Customer Email', related='customer_id.email', readonly=True)
    
    # Transfer Details
    transfer_from = fields.Char('From', required=True, tracking=True,
                                help="Starting location (Airport, Hotel, Address, etc.)")
    transfer_to = fields.Char('To', required=True, tracking=True,
                              help="Destination location (Airport, Hotel, Address, etc.)")
    
    # Date and Time
    departure_date = fields.Date('Departure Date', required=True, tracking=True)
    departure_time = fields.Float('Departure Time', required=True, tracking=True,
                                  help="Time in 24-hour format (e.g., 14.5 for 14:30)")
    arrive_date = fields.Date('Arrival Date', tracking=True)
    arrive_time = fields.Float('Arrival Time', tracking=True,
                               help="Expected arrival time in 24-hour format")
    
    # Flight Information (Optional)
    flight_number = fields.Char('Flight Number', tracking=True,
                                help="Flight number if transfer is airport-related")
    
    # Contact Information
    contact_phone = fields.Char('Contact Phone', required=True, tracking=True,
                                help="Phone number for coordination during transfer")
    
    # Passenger Information
    passenger_count = fields.Integer('Number of Passengers', required=True, default=1,
                                     tracking=True)
    
    # Vehicle Information
    vehicle_type = fields.Char('Vehicle Type', tracking=True,
                               help="Type of vehicle required (Car, Van, Bus, VIP, etc.)")
    special_requirements = fields.Text('Special Requirements',
                                       help="Any special requirements (Child seats, wheelchair access, etc.)")
    
    # Supplier Information
    supplier_id = fields.Many2one('res.partner', string='Supplier', required=True,
                                  domain=[('is_company', '=', True), ('supplier_rank', '>', 0)],
                                  tracking=True)
    supplier_phone = fields.Char('Supplier Phone', related='supplier_id.phone', readonly=True)
    supplier_email = fields.Char('Supplier Email', related='supplier_id.email', readonly=True)
    
    # Pricing
    supplier_cost = fields.Monetary('Supplier Cost', currency_field='currency_id', tracking=True,
                                    help="Cost charged by the supplier")
    customer_price = fields.Monetary('Customer Price', currency_field='currency_id', tracking=True,
                                     help="Price charged to the customer")
    profit_amount = fields.Monetary('Profit', currency_field='currency_id', 
                                    compute='_compute_profit', store=True,
                                    help="Profit = Customer Price - Supplier Cost")
    profit_percentage = fields.Float('Profit %', compute='_compute_profit', store=True,
                                     help="Profit percentage based on supplier cost")
    
    currency_id = fields.Many2one('res.currency', string='Currency', 
                                  default=lambda self: self.env.company.currency_id)
    
    # Status and Workflow
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', tracking=True)
    
    # Additional Information
    notes = fields.Text('Internal Notes')
    customer_notes = fields.Text('Customer Notes', help="Notes visible to customer")
    
    # Computed Fields for Display
    transfer_route = fields.Char('Route', compute='_compute_transfer_route', store=True)
    departure_datetime = fields.Datetime('Departure DateTime', compute='_compute_departure_datetime', store=True)
    supplier_name = fields.Char('Supplier Name', compute='_compute_supplier_name')
    
    # Company
    company_id = fields.Many2one('res.company', string='Company',
                                 default=lambda self: self.env.company)

    # Related Orders
    sale_order_id = fields.Many2one('sale.order', string='Sales Order', readonly=True,
                                    help="Sales order created for customer invoice")
    purchase_order_id = fields.Many2one('purchase.order', string='Purchase Order', readonly=True,
                                        help="Purchase order created for vendor bill")

    @api.model
    def create(self, vals):
        """Override create to generate sequence number"""
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('transfer.request') or _('New')
        return super().create(vals)

    @api.depends('transfer_from', 'transfer_to')
    def _compute_transfer_route(self):
        """Compute transfer route for display"""
        for record in self:
            if record.transfer_from and record.transfer_to:
                record.transfer_route = f"{record.transfer_from} → {record.transfer_to}"
            else:
                record.transfer_route = ""

    @api.depends('departure_date', 'departure_time')
    def _compute_departure_datetime(self):
        """Compute departure datetime for sorting and filtering"""
        for record in self:
            if record.departure_date and record.departure_time:
                # Convert float time to hours and minutes
                hours = int(record.departure_time)
                minutes = int((record.departure_time - hours) * 60)
                record.departure_datetime = datetime.combine(
                    record.departure_date,
                    datetime.min.time().replace(hour=hours, minute=minutes)
                )
            else:
                record.departure_datetime = False

    def _compute_supplier_name(self):
        """Safely compute supplier name for portal access"""
        for record in self:
            try:
                record.supplier_name = record.supplier_id.name if record.supplier_id else 'Not specified'
            except:
                record.supplier_name = 'Not specified'

    @api.depends('supplier_cost', 'customer_price')
    def _compute_profit(self):
        """Compute profit amount and percentage"""
        for record in self:
            record.profit_amount = record.customer_price - record.supplier_cost
            if record.supplier_cost > 0:
                record.profit_percentage = (record.profit_amount / record.supplier_cost) * 100
            else:
                record.profit_percentage = 0.0

    @api.constrains('departure_date', 'departure_time')
    def _check_departure_datetime(self):
        """Validate departure date and time"""
        for record in self:
            if record.departure_date and record.departure_date < fields.Date.today():
                raise ValidationError(_("Departure date cannot be in the past."))
            
            if record.departure_time < 0 or record.departure_time >= 24:
                raise ValidationError(_("Departure time must be between 0:00 and 23:59."))

    @api.constrains('arrive_time')
    def _check_arrive_time(self):
        """Validate arrival time"""
        for record in self:
            if record.arrive_time and (record.arrive_time < 0 or record.arrive_time >= 24):
                raise ValidationError(_("Arrival time must be between 0:00 and 23:59."))

    @api.constrains('passenger_count')
    def _check_passenger_count(self):
        """Validate passenger count"""
        for record in self:
            if record.passenger_count <= 0:
                raise ValidationError(_("Number of passengers must be greater than 0."))

    @api.constrains('supplier_cost', 'customer_price')
    def _check_pricing(self):
        """Validate pricing"""
        for record in self:
            if record.supplier_cost < 0:
                raise ValidationError(_("Supplier cost cannot be negative."))
            if record.customer_price < 0:
                raise ValidationError(_("Customer price cannot be negative."))

    def name_get(self):
        """Custom name_get to show reference and route"""
        result = []
        for record in self:
            name = f"{record.name}"
            if record.transfer_route:
                name += f" - {record.transfer_route}"
            result.append((record.id, name))
        return result

    def action_confirm(self):
        """Confirm the transfer request"""
        for record in self:
            if record.state != 'draft':
                raise UserError(_("Only draft requests can be confirmed."))

            # Validate required fields before confirmation
            record._validate_for_confirmation()

            record.state = 'confirmed'
            record.message_post(
                body=_("Transfer request confirmed."),
                subject=_("Transfer Request Confirmed")
            )

            # Send notification to supplier if email exists
            if record.supplier_id.email:
                record._send_supplier_notification()

    def action_cancel(self):
        """Cancel the transfer request"""
        for record in self:
            if record.state == 'cancelled':
                raise UserError(_("Request is already cancelled."))

            record.state = 'cancelled'
            record.message_post(
                body=_("Transfer request cancelled."),
                subject=_("Transfer Request Cancelled")
            )

            # Send cancellation notification
            if record.supplier_id.email:
                record._send_cancellation_notification()

    def action_set_to_draft(self):
        """Set transfer request back to draft"""
        for record in self:
            record.state = 'draft'
            record.message_post(
                body=_("Transfer request set to draft."),
                subject=_("Transfer Request Reset")
            )

    def _validate_for_confirmation(self):
        """Validate all required data before confirmation"""
        self.ensure_one()
        errors = []

        if not self.customer_id:
            errors.append(_("Customer is required"))
        if not self.supplier_id:
            errors.append(_("Supplier is required"))
        if not self.transfer_from:
            errors.append(_("From location is required"))
        if not self.transfer_to:
            errors.append(_("To location is required"))
        if not self.departure_date:
            errors.append(_("Departure date is required"))
        if not self.departure_time:
            errors.append(_("Departure time is required"))
        if not self.contact_phone:
            errors.append(_("Contact phone is required"))
        if self.passenger_count <= 0:
            errors.append(_("Number of passengers must be greater than 0"))
        if not self.supplier_cost:
            errors.append(_("Supplier cost is required"))
        if not self.customer_price:
            errors.append(_("Customer price is required"))

        if errors:
            raise ValidationError(_("Cannot confirm request:\n") + "\n".join(errors))

    def _send_supplier_notification(self):
        """Send email notification to supplier"""
        self.ensure_one()
        # This would integrate with email templates
        # For now, just log the action
        self.message_post(
            body=_("Notification sent to supplier: %s") % self.supplier_id.name,
            subject=_("Supplier Notification Sent")
        )

    def _send_cancellation_notification(self):
        """Send cancellation notification to supplier"""
        self.ensure_one()
        # This would integrate with email templates
        # For now, just log the action
        self.message_post(
            body=_("Cancellation notification sent to supplier: %s") % self.supplier_id.name,
            subject=_("Cancellation Notification Sent")
        )

    @api.onchange('customer_id')
    def _onchange_customer_id(self):
        """Update contact phone when customer changes"""
        if self.customer_id and self.customer_id.phone:
            self.contact_phone = self.customer_id.phone

    @api.onchange('departure_date')
    def _onchange_departure_date(self):
        """Set arrival date same as departure date by default"""
        if self.departure_date and not self.arrive_date:
            self.arrive_date = self.departure_date

    def _get_time_display(self, time_float):
        """Convert float time to display format (HH:MM)"""
        if time_float:
            hours = int(time_float)
            minutes = int((time_float - hours) * 60)
            return f"{hours:02d}:{minutes:02d}"
        return ""

    # ===== PRICING AND PROFIT METHODS =====

    def calculate_suggested_price(self):
        """Calculate suggested customer price based on supplier cost and default margin"""
        for record in self:
            if record.supplier_cost > 0:
                # Default margin of 20%
                default_margin = 0.20
                suggested_price = record.supplier_cost * (1 + default_margin)
                return suggested_price
            return 0.0

    @api.onchange('supplier_cost')
    def _onchange_supplier_cost(self):
        """Auto-suggest customer price when supplier cost changes"""
        if self.supplier_cost and not self.customer_price:
            self.customer_price = self.calculate_suggested_price()

    def action_calculate_profit_report(self):
        """Generate profit analysis report"""
        profit_data = {
            'supplier_cost': self.supplier_cost,
            'customer_price': self.customer_price,
            'profit_amount': self.profit_amount,
            'profit_percentage': self.profit_percentage,
            'margin_analysis': self._get_margin_analysis(),
        }
        return profit_data

    def _get_margin_analysis(self):
        """Get margin analysis compared to industry standards"""
        if self.profit_percentage >= 25:
            return "Excellent margin"
        elif self.profit_percentage >= 15:
            return "Good margin"
        elif self.profit_percentage >= 5:
            return "Acceptable margin"
        elif self.profit_percentage >= 0:
            return "Low margin"
        else:
            return "Loss"

    # ===== BUSINESS LOGIC METHODS =====

    def action_duplicate_request(self):
        """Duplicate transfer request with new reference"""
        self.ensure_one()
        new_request = self.copy({
            'name': _('New'),
            'state': 'draft',
        })
        return {
            'type': 'ir.actions.act_window',
            'name': _('Transfer Request'),
            'res_model': 'transfer.request',
            'res_id': new_request.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_create_invoice(self):
        """Create customer sales order and invoice"""
        self.ensure_one()
        if not self.customer_price:
            raise UserError(_("Customer price must be set to create invoice."))
        if not self.customer_id:
            raise UserError(_("Customer must be set to create invoice."))

        # Check if sales order already exists
        if self.sale_order_id:
            return self.action_view_sale_order()

        # Create sales order
        sale_order = self._create_sale_order()

        # Open the created sales order
        return {
            'type': 'ir.actions.act_window',
            'name': _('Sales Order'),
            'view_mode': 'form',
            'res_model': 'sale.order',
            'res_id': sale_order.id,
            'target': 'current',
        }

    def action_create_vendor_bill(self):
        """Create vendor purchase order and bill"""
        self.ensure_one()
        if not self.supplier_cost:
            raise UserError(_("Supplier cost must be set to create vendor bill."))
        if not self.supplier_id:
            raise UserError(_("Supplier must be set to create vendor bill."))

        # Check if purchase order already exists
        if self.purchase_order_id:
            return self.action_view_purchase_order()

        # Create purchase order
        purchase_order = self._create_purchase_order()

        # Open the created purchase order
        return {
            'type': 'ir.actions.act_window',
            'name': _('Purchase Order'),
            'view_mode': 'form',
            'res_model': 'purchase.order',
            'res_id': purchase_order.id,
            'target': 'current',
        }

    def _create_sale_order(self):
        """Create sales order for customer invoice"""
        self.ensure_one()

        # Get or create service product
        service_product = self.env['product.product'].search([
            ('name', '=', 'Transfer Service'),
            ('type', '=', 'service')
        ], limit=1)

        if not service_product:
            service_product = self.env['product.product'].create({
                'name': 'Transfer Service',
                'type': 'service',
                'categ_id': self.env.ref('product.product_category_all').id,
                'sale_ok': True,
                'purchase_ok': False,
                'list_price': 0.0,
            })

        # Create order line description
        description = f"Transfer Service - {self.transfer_route}"
        if self.departure_date:
            description += f" on {self.departure_date.strftime('%Y-%m-%d')}"
        if self.passenger_count > 1:
            description += f" for {self.passenger_count} passengers"
        if self.vehicle_type:
            description += f" ({self.vehicle_type})"

        order_lines = [(0, 0, {
            'product_id': service_product.id,
            'name': description,
            'product_uom_qty': 1,
            'price_unit': self.customer_price,
            'product_uom': service_product.uom_id.id,
        })]

        so_vals = {
            'partner_id': self.customer_id.id,
            'origin': self.name,
            'order_line': order_lines,
            'currency_id': self.currency_id.id,
            'pricelist_id': self.customer_id.property_product_pricelist.id or 1,
        }

        sale_order = self.env['sale.order'].create(so_vals)
        self.sale_order_id = sale_order.id

        self.message_post(
            body=_("Sales Order %s created for customer %s - Amount: %s %s") %
                 (sale_order.name, self.customer_id.name, self.customer_price, self.currency_id.name)
        )

        return sale_order

    def _create_purchase_order(self):
        """Create purchase order for vendor bill"""
        self.ensure_one()

        # Get or create service product
        service_product = self.env['product.product'].search([
            ('name', '=', 'Transfer Service'),
            ('type', '=', 'service')
        ], limit=1)

        if not service_product:
            service_product = self.env['product.product'].create({
                'name': 'Transfer Service',
                'type': 'service',
                'categ_id': self.env.ref('product.product_category_all').id,
                'sale_ok': False,
                'purchase_ok': True,
                'standard_price': 0.0,
            })

        # Create order line description
        description = f"Transfer Service - {self.transfer_route}"
        if self.departure_date:
            description += f" on {self.departure_date.strftime('%Y-%m-%d')}"
        if self.passenger_count > 1:
            description += f" for {self.passenger_count} passengers"
        if self.vehicle_type:
            description += f" ({self.vehicle_type})"

        order_lines = [(0, 0, {
            'product_id': service_product.id,
            'name': description,
            'product_qty': 1,
            'price_unit': self.supplier_cost,
            'product_uom': service_product.uom_po_id.id,
            'date_planned': self.departure_datetime or fields.Datetime.now(),
        })]

        po_vals = {
            'partner_id': self.supplier_id.id,
            'origin': self.name,
            'order_line': order_lines,
            'currency_id': self.currency_id.id,
        }

        purchase_order = self.env['purchase.order'].create(po_vals)
        self.purchase_order_id = purchase_order.id

        self.message_post(
            body=_("Purchase Order %s created for supplier %s - Amount: %s %s") %
                 (purchase_order.name, self.supplier_id.name, self.supplier_cost, self.currency_id.name)
        )

        return purchase_order

    def action_view_sale_order(self):
        """Open the related sales order"""
        self.ensure_one()
        if not self.sale_order_id:
            return

        return {
            'type': 'ir.actions.act_window',
            'name': _('Sales Order'),
            'view_mode': 'form',
            'res_model': 'sale.order',
            'res_id': self.sale_order_id.id,
            'target': 'current',
        }

    def action_view_purchase_order(self):
        """Open the related purchase order"""
        self.ensure_one()
        if not self.purchase_order_id:
            return

        return {
            'type': 'ir.actions.act_window',
            'name': _('Purchase Order'),
            'view_mode': 'form',
            'res_model': 'purchase.order',
            'res_id': self.purchase_order_id.id,
            'target': 'current',
        }
