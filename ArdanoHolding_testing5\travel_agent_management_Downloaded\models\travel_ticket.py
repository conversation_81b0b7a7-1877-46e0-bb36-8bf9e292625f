# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class TravelTicket(models.Model):
    _name = 'travel.ticket'
    _description = 'Travel Ticket - Simplified'
    _order = 'passenger_line_id, sequence, travel_date'

    # الربط مع المسافر
    passenger_line_id = fields.Many2one(
        'travel.passenger.line',
        string='Passenger Line',
        required=True,
        ondelete='cascade'
    )


    
    sequence = fields.Integer(
        string='Sequence', 
        default=10,
        help="ترتيب التذكرة في الرحلة"
    )
    
    # نوع التذكرة
    ticket_type = fields.Selection([
        ('oneway', 'One Way'),
        ('roundtrip', 'Round Trip'),
        ('multicity', 'Multi City')
    ], string='Ticket Type', default='oneway', required=True)

    # المعلومات الأساسية التي يحتاجها قسم الحجز
    route = fields.Char(
        string='Route',
        required=True,
        placeholder="القاهرة → دبي",
        help="المسار من → إلى"
    )

    # تفاصيل الرحلة
    departure_city = fields.Char(
        string='Departure City',
        help="مدينة المغادرة"
    )

    arrival_city = fields.Char(
        string='Arrival City',
        help="مدينة الوصول"
    )

    travel_date = fields.Date(
        string='Departure Date',
        required=True,
        help="تاريخ المغادرة"
    )

    # حقول الإياب للرحلات ذهاب وعودة
    return_departure_city = fields.Char(
        string='Return From',
        help="مدينة المغادرة للعودة"
    )

    return_arrival_city = fields.Char(
        string='Return To',
        help="مدينة الوصول للعودة"
    )

    return_travel_date = fields.Date(
        string='Return Date',
        help="تاريخ العودة"
    )

    return_route = fields.Char(
        string='Return Route',
        help="مسار العودة"
    )



    departure_date = fields.Datetime(
        string='Departure Date',
        help="تاريخ ووقت المغادرة"
    )

    return_date = fields.Datetime(
        string='Return Date',
        help="تاريخ ووقت العودة (للرحلات ذهاب وعودة)"
    )

    return_departure_date = fields.Datetime(
        string='Return Departure Date',
        help="تاريخ ووقت مغادرة العودة"
    )

    flight_number = fields.Char(
        string='Flight Number',
        help="رقم الرحلة"
    )

    airline = fields.Char(
        string='Airline',
        help="شركة الطيران"
    )

    # تفاصيل رحلة العودة
    return_flight_number = fields.Char(
        string='Return Flight Number',
        help="رقم رحلة العودة"
    )

    return_airline = fields.Char(
        string='Return Airline',
        help="شركة طيران العودة"
    )

    class_type = fields.Selection([
        ('economy', 'Economy'),
        ('premium_economy', 'Premium Economy'),
        ('business', 'Business'),
        ('first', 'First Class')
    ], string='Class Type', default='economy')
    
    vendor_id = fields.Many2one(
        'res.partner',
        string='Vendor',
        domain=[('is_company', '=', True), ('supplier_rank', '>', 0)],
        required=True,
        help="المورد الذي سيصدر التذكرة"
    )
    
    vendor_cost = fields.Monetary(
        string='Vendor Cost',
        currency_field='cost_currency_id',
        help="سعر المورد"
    )
    
    customer_price = fields.Monetary(
        string='Customer Price', 
        currency_field='price_currency_id',
        help="السعر للعميل"
    )
    
    ticket_reference = fields.Char(
        string='Ticket Reference',
        help="رقم التذكرة من المورد"
    )
    
    # حقول مساعدة
    cost_currency_id = fields.Many2one(
        'res.currency',
        related='passenger_line_id.cost_currency_id',
        store=True
    )
    
    price_currency_id = fields.Many2one(
        'res.currency', 
        related='passenger_line_id.price_currency_id',
        store=True
    )
    
    margin = fields.Monetary(
        string='Margin',
        compute='_compute_margin',
        store=True,
        currency_field='price_currency_id'
    )
    
    status = fields.Selection([
        ('draft', 'Draft'),
        ('quoted', 'Quoted'),
        ('booked', 'Booked'),
        ('ticketed', 'Ticketed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft')
    
    # حقول مرتبطة للسهولة
    passenger_id = fields.Many2one(
        'travel.passenger',
        related='passenger_line_id.passenger_id',
        string='Passenger',
        store=True
    )
    
    request_id = fields.Many2one(
        'travel.ticket.request',
        related='passenger_line_id.request_id',
        string='Travel Request',
        store=True
    )
    
    # حقول إضافية مفيدة
    notes = fields.Text(
        string='Notes',
        help="ملاحظات إضافية حول التذكرة"
    )
    
    days_until_travel = fields.Integer(
        string='Days Until Travel',
        compute='_compute_days_until_travel'
    )

    # حقول محسوبة لعرض معلومات الرحلة
    trip_summary = fields.Char(
        string='Trip Summary',
        compute='_compute_trip_summary',
        help="ملخص الرحلة مع نوع التذكرة"
    )


    
    # الحسابات
    @api.depends('vendor_cost', 'customer_price')
    def _compute_margin(self):
        for ticket in self:
            ticket.margin = ticket.customer_price - ticket.vendor_cost
    
    @api.depends('departure_date', 'travel_date')
    def _compute_days_until_travel(self):
        today = fields.Date.context_today(self)
        for ticket in self:
            # Use departure_date if available, otherwise fall back to travel_date
            date_to_use = ticket.departure_date.date() if ticket.departure_date else ticket.travel_date
            if date_to_use:
                ticket.days_until_travel = (date_to_use - today).days
            else:
                ticket.days_until_travel = 0
    
    @api.depends('ticket_type', 'route', 'return_route')
    def _compute_trip_summary(self):
        for ticket in self:
            if ticket.ticket_type == 'roundtrip':
                if ticket.return_route:
                    ticket.trip_summary = f"Round Trip: {ticket.route} | {ticket.return_route}"
                else:
                    ticket.trip_summary = f"Round Trip: {ticket.route}"
            elif ticket.ticket_type == 'multicity':
                ticket.trip_summary = f"Multi-City: {ticket.route}"
            else:
                ticket.trip_summary = f"One Way: {ticket.route}"

    @api.onchange('departure_city', 'arrival_city')
    def _onchange_cities(self):
        """تحديث المسار تلقائياً عند تغيير المدن"""
        if self.departure_city and self.arrival_city:
            self.route = f"{self.departure_city} → {self.arrival_city}"

            # للرحلات ذهاب وعودة، اقترح مسار العودة العكسي
            if self.ticket_type == 'roundtrip':
                if not self.return_departure_city:
                    self.return_departure_city = self.arrival_city
                if not self.return_arrival_city:
                    self.return_arrival_city = self.departure_city

    @api.onchange('return_departure_city', 'return_arrival_city')
    def _onchange_return_cities(self):
        """تحديث مسار العودة تلقائياً"""
        if self.return_departure_city and self.return_arrival_city:
            self.return_route = f"{self.return_departure_city} → {self.return_arrival_city}"

    @api.onchange('ticket_type')
    def _onchange_ticket_type(self):
        """تحديث الحقول عند تغيير نوع التذكرة"""
        if self.ticket_type == 'oneway':
            # مسح بيانات العودة
            self.return_departure_city = False
            self.return_arrival_city = False
            self.return_travel_date = False
            self.return_route = False
        elif self.ticket_type == 'roundtrip':
            # اقتراح بيانات العودة العكسية
            if self.departure_city and self.arrival_city:
                if not self.return_departure_city:
                    self.return_departure_city = self.arrival_city
                if not self.return_arrival_city:
                    self.return_arrival_city = self.departure_city
                if not self.return_route:
                    self.return_route = f"{self.arrival_city} → {self.departure_city}"

    # التحقق من صحة البيانات
    @api.constrains('travel_date', 'return_travel_date', 'ticket_type')
    def _check_travel_dates(self):
        for ticket in self:
            today = fields.Date.context_today(ticket)

            # تحقق من تاريخ الذهاب
            if ticket.travel_date and ticket.travel_date < today:
                raise ValidationError(_("تاريخ السفر لا يمكن أن يكون في الماضي."))

            # تحقق من تاريخ العودة للرحلات ذهاب وعودة
            if ticket.ticket_type == 'roundtrip':
                if not ticket.return_travel_date:
                    raise ValidationError(_("تاريخ العودة مطلوب للرحلات ذهاب وعودة."))

                if ticket.return_travel_date <= ticket.travel_date:
                    raise ValidationError(_("تاريخ العودة يجب أن يكون بعد تاريخ الذهاب."))
    
    @api.constrains('vendor_cost', 'customer_price')
    def _check_prices(self):
        for ticket in self:
            if ticket.vendor_cost < 0:
                raise ValidationError(_("سعر المورد لا يمكن أن يكون سالباً."))
            if ticket.customer_price < 0:
                raise ValidationError(_("سعر العميل لا يمكن أن يكون سالباً."))
    
    # عرض الاسم
    def name_get(self):
        result = []
        for ticket in self:
            name = f"Ticket {ticket.sequence}"
            if ticket.route:
                name = f"{name}: {ticket.route}"
            if ticket.travel_date:
                name = f"{name} ({ticket.travel_date.strftime('%Y-%m-%d')})"
            result.append((ticket.id, name))
        return result
    
    # إجراءات سريعة
    def action_set_quoted(self):
        """تحديد حالة التذكرة كمسعرة"""
        for ticket in self:
            if ticket.status != 'draft':
                raise ValidationError(_("Only draft tickets can be set to quoted."))
            ticket.write({'status': 'quoted'})
            # Post message to the passenger line
            if ticket.passenger_line_id:
                ticket.passenger_line_id.message_post(
                    body=_("Ticket %s (%s) status changed to Quoted.") %
                    (ticket.sequence, ticket.route)
                )

    def action_set_booked(self):
        """تحديد حالة التذكرة كمحجوزة"""
        for ticket in self:
            if ticket.status not in ['draft', 'quoted']:
                raise ValidationError(_("Only draft or quoted tickets can be booked."))
            ticket.write({'status': 'booked'})
            # Post message to the passenger line
            if ticket.passenger_line_id:
                ticket.passenger_line_id.message_post(
                    body=_("Ticket %s (%s) status changed to Booked.") %
                    (ticket.sequence, ticket.route)
                )

    def action_set_ticketed(self):
        """تحديد حالة التذكرة كمصدرة"""
        for ticket in self:
            if ticket.status != 'booked':
                raise ValidationError(_("Only booked tickets can be set to ticketed."))
            if not ticket.ticket_reference:
                raise ValidationError(_("Please enter ticket reference number before setting as ticketed."))
            ticket.write({'status': 'ticketed'})
            # Post message to the passenger line
            if ticket.passenger_line_id:
                ticket.passenger_line_id.message_post(
                    body=_("Ticket %s (%s) status changed to Ticketed. Reference: %s") %
                    (ticket.sequence, ticket.route, ticket.ticket_reference)
                )

    def action_cancel(self):
        """إلغاء التذكرة"""
        for ticket in self:
            if ticket.status == 'cancelled':
                raise ValidationError(_("Ticket is already cancelled."))
            old_status = ticket.status
            ticket.write({'status': 'cancelled'})
            # Post message to the passenger line
            if ticket.passenger_line_id:
                ticket.passenger_line_id.message_post(
                    body=_("Ticket %s (%s) cancelled (was %s).") %
                    (ticket.sequence, ticket.route, old_status)
                )

    @api.model
    def create(self, vals):
        """Override create to set sequence and update passenger line"""
        if 'sequence' not in vals or not vals['sequence']:
            passenger_line = self.env['travel.passenger.line'].browse(vals.get('passenger_line_id'))
            if passenger_line:
                existing_tickets = passenger_line.ticket_ids
                if existing_tickets:
                    max_sequence = max(existing_tickets.mapped('sequence') or [0])
                    vals['sequence'] = max_sequence + 10
                else:
                    vals['sequence'] = 10

        ticket = super().create(vals)
        # Update passenger line travel summary
        if ticket.passenger_line_id:
            ticket.passenger_line_id._update_travel_summary_from_tickets()
        return ticket

    def write(self, vals):
        """Override write to update passenger line when route or date changes"""
        result = super().write(vals)
        if 'route' in vals or 'travel_date' in vals:
            for ticket in self:
                if ticket.passenger_line_id:
                    ticket.passenger_line_id._update_travel_summary_from_tickets()
        return result


