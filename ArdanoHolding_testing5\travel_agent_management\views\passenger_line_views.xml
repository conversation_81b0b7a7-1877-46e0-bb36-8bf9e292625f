<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Passenger Line Form View for Flight Segment Management -->
        <record id="view_travel_passenger_line_form" model="ir.ui.view">
            <field name="name">travel.passenger.line.form</field>
            <field name="model">travel.passenger.line</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <field name="status" widget="statusbar" statusbar_visible="draft,booked,ticketed"/>
                    </header>

                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="passenger_id" readonly="1"/>
                            </h1>
                            <div class="o_row">
                                <field name="request_id" readonly="1" class="oe_inline"/>
                                <span class="oe_grey"> • </span>
                                <field name="travel_purpose" readonly="1" class="oe_inline"/>
                            </div>
                        </div>

                        <!-- Passenger Information Section -->
                        <group name="passenger_section" string="PASSENGER INFORMATION">
                            <group name="passenger_details">
                                <field name="passenger_passport_number"/>
                                <field name="passenger_nationality"/>
                                <field name="travel_purpose"/>
                                <field name="status"/>
                            </group>
                            <group name="travel_summary">
                                <field name="departure_route"/>
                                <field name="departure_date"/>
                                <field name="return_route"/>
                                <field name="return_date"/>
                            </group>
                        </group>

                        <!-- Preferences Section -->
                        <group name="preferences_section" string="PREFERENCES">
                            <group name="seat_meal_prefs">
                                <field name="seat_preference"/>
                                <field name="meal_preference"/>
                            </group>
                            <group name="special_needs">
                                <field name="special_requirements" placeholder="Wheelchair assistance, medical needs, dietary restrictions, etc."/>
                            </group>
                        </group>

                        <notebook>
                            <page string="Travel Tickets" name="travel_tickets">
                                <!-- Ticket Summary Section -->
                                <group name="ticket_summary_section" string="Ticket Summary">
                                    <group name="ticket_stats">
                                        <field name="ticket_count" string="Number of Tickets"/>
                                        <field name="has_layovers" string="Multiple Tickets"/>
                                        <field name="days_until_departure"/>
                                    </group>
                                    <group name="pricing_summary">
                                        <field name="vendor_cost"/>
                                        <field name="customer_price"/>
                                        <field name="margin"/>
                                    </group>
                                </group>

                                <!-- Tickets List -->
                                <div class="mt-3">
                                    <h4>Travel Tickets</h4>
                                    <p class="text-muted">
                                        Add travel tickets for this passenger. Each ticket represents a separate booking with a vendor.
                                        Example: Ticket 1 (Cairo→Dubai) and Ticket 2 (Dubai→Washington) for a journey from Cairo to Washington.
                                    </p>

                                    <field name="ticket_ids">
                                        <tree editable="bottom" create="true" delete="true">
                                            <field name="sequence" widget="handle"/>
                                            <field name="route" placeholder="Cairo → Dubai"/>
                                            <field name="travel_date"/>
                                            <field name="vendor_id"/>
                                            <field name="vendor_cost"/>
                                            <field name="customer_price"/>
                                            <field name="margin" readonly="1"/>
                                            <field name="ticket_reference" placeholder="Ticket Number"/>
                                            <field name="status" widget="badge" decoration-info="status=='draft'" decoration-warning="status=='quoted'" decoration-primary="status=='booked'" decoration-success="status=='ticketed'" decoration-danger="status=='cancelled'"/>
                                            <!-- Smart single button that shows next action -->
                                            <button name="action_set_quoted" type="object" string="→ Quote" title="Set as Quoted" class="btn-sm btn-warning" icon="fa-quote-left" attrs="{'invisible': [('status', '!=', 'draft')]}"/>
                                            <button name="action_set_booked" type="object" string="→ Book" title="Set as Booked" class="btn-sm btn-primary" icon="fa-calendar-check-o" attrs="{'invisible': [('status', '!=', 'quoted')]}"/>
                                            <button name="action_set_ticketed" type="object" string="→ Ticket" title="Set as Ticketed" class="btn-sm btn-success" icon="fa-plane" attrs="{'invisible': [('status', '!=', 'booked')]}"/>
                                            <!-- Cancel button only for non-cancelled tickets -->
                                            <button name="action_cancel" type="object" string="✕" title="Cancel Ticket" class="btn-sm btn-outline-danger" icon="fa-times" attrs="{'invisible': [('status', '=', 'cancelled')]}"/>
                                        </tree>
                                    </field>

                                    <div class="alert alert-info mt-3" role="alert">
                                        <strong>Tips for entering tickets:</strong>
                                        <ul class="mb-0">
                                            <li><strong>Sequence:</strong> Use 10, 20, 30... to maintain ticket order</li>
                                            <li><strong>Route format:</strong> "City → City" - like "Cairo → Dubai"</li>
                                            <li><strong>Vendor:</strong> Choose the appropriate vendor for each ticket</li>
                                            <li><strong>Pricing:</strong> Enter vendor cost and customer price for each separate ticket</li>
                                        </ul>
                                    </div>
                                </div>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
    </data>
</odoo> 