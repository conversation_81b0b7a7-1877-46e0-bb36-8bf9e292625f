/** @odoo-module **/

import { BinaryField } from "@web/views/fields/binary/binary_field";
import { patch } from "@web/core/utils/patch";

// Patch the BinaryField to automatically set filename
patch(BinaryField.prototype, "travel_agent_management.BinaryField", {
    
    async _onFileUploaded(info) {
        // Call the original method
        await this._super(...arguments);
        
        // If this is a visa document and we have a filename field, update it
        if (this.props.record.resModel === 'visa.request.document' && 
            this.props.name === 'datas' && 
            info.name && 
            this.props.record.data.filename !== info.name) {
            
            // Update the filename field
            await this.props.record.update({
                filename: info.name
            });
        }
    }
});
