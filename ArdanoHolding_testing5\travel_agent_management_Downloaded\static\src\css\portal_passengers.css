/* Passengers Portal Enhanced Styles */

/* ====== CARD IMPROVEMENTS ====== */
.passenger-info .info-row {
    font-size: 0.9rem;
    line-height: 1.4;
}

.passenger-info .info-row i {
    width: 16px;
    text-align: center;
}

.card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    border-color: #007bff;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

/* ====== STATUS BADGES ====== */
.badge {
    font-size: 0.75rem;
    padding: 0.4em 0.8em;
    border-radius: 1rem;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

/* ====== CONTROLS BAR ====== */
.controls-bar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.controls-bar .card-body {
    padding: 1.5rem;
}

/* Search Input */
.controls-bar .form-control {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 0.6rem 1rem;
    font-size: 0.95rem;
}

.controls-bar .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Dropdown Buttons */
.controls-bar .dropdown-toggle {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    font-size: 0.95rem;
    border: 1px solid #dee2e6;
    background: white;
    transition: all 0.2s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.controls-bar .dropdown-toggle:hover {
    border-color: #007bff;
    box-shadow: 0 0 0 0.1rem rgba(0,123,255,.25);
}

.controls-bar .dropdown-toggle:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    outline: none;
}

.controls-bar .dropdown-toggle::after {
    margin-left: auto;
}

/* View Toggle Buttons */
.controls-bar .btn-group .btn {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    transition: all 0.2s ease;
}

.controls-bar .btn-group .btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.controls-bar .btn-group .btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* Dropdown Container */
.dropdown {
    position: relative;
    display: inline-block;
}

/* Dropdown Menus */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1050 !important;
    display: none;
    float: left;
    min-width: 200px;
    max-height: 300px;
    overflow-y: auto;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 0.9rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff !important;
    background-clip: padding-box;
    border: 2px solid #007bff !important;
    border-radius: 8px;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.3) !important;
}

.dropdown-menu.show,
.dropdown-menu:visible {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.dropdown.show .dropdown-menu {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Right-aligned dropdown menus */
.dropdown-menu-right {
    right: 0;
    left: auto;
}

/* Dropdown Toggle Button */
.dropdown-toggle {
    white-space: nowrap;
    cursor: pointer;
}

.dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}

.dropdown-toggle:empty::after {
    margin-left: 0;
}

/* When dropdown is open */
.dropdown.show .dropdown-toggle::after {
    border-top: 0;
    border-bottom: 0.3em solid;
}

/* Dropdown Header */
.dropdown-header {
    padding: 0.5rem 1rem;
    margin-bottom: 0.25rem;
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background-color: #f8f9fa;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    display: block;
    width: 100%;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    transition: all 0.15s ease-in-out;
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: #16181b;
    background-color: #f8f9fa;
    text-decoration: none;
}

.dropdown-item.active {
    color: #fff;
    background-color: #007bff;
    font-weight: 500;
}

.dropdown-item.active i {
    color: #fff;
}

.dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #e9ecef;
}

/* Grid View - Cards */
.card {
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 2rem rgba(0,0,0,0.15);
    border-color: #007bff;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
}

.info-row {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    line-height: 1.5;
}

.info-row i {
    width: 16px;
    text-align: center;
}

/* Status Badges */
.badge {
    font-size: 0.8rem;
    font-weight: 500;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

/* List View - Table */
.table {
    margin-bottom: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    background-color: white;
}

.table thead th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1rem 0.75rem;
    border: none;
}

.table tbody tr.passenger-row {
    transition: all 0.2s ease;
    border-bottom: 1px solid #f1f3f4;
    background-color: white;
}

.table tbody tr.passenger-row:nth-child(even) {
    background-color: #fafbfc;
}

.table tbody tr.passenger-row:hover {
    background-color: #f0f8ff !important;
    transform: scale(1.005);
    box-shadow: 0 2px 8px rgba(0,123,255,0.15);
    border-left: 3px solid #007bff;
}

.table tbody tr.passenger-row:last-child {
    border-bottom: none;
}

.table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-top: none;
    font-size: 0.9rem;
}

/* List View Specific Styling */
.passenger-name-cell strong {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1rem;
}

.passport-number {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 0.3rem 0.6rem;
    border-radius: 4px;
    font-weight: 500;
}

.nationality-cell {
    display: flex;
    align-items: center;
    font-weight: 500;
}

.expiry-date-cell {
    display: flex;
    align-items: center;
    font-weight: 500;
}

.travel-info {
    text-align: center;
}

.travel-info .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

/* Group Headers */
.group-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

.group-header h5 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.group-header i {
    color: #6c757d;
}

/* Empty State */
.alert-info {
    border-radius: 12px;
    border: 1px solid #b8daff;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.alert-info i.fa-users {
    color: #17a2b8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .controls-bar .row > div {
        margin-bottom: 0.5rem;
    }
    
    .controls-bar .card-body {
        padding: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: 8px;
    }
    
    .table thead th {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }
    
    .table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.85rem;
    }
}

/* Print Styles */
@media print {
    .controls-bar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .table {
        font-size: 0.8rem;
    }
}

/* Accessibility */
.btn:focus,
.form-control:focus,
.dropdown-toggle:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    z-index: 999;
}

/* ====== ANIMATION ENHANCEMENTS ====== */
.card-body {
    position: relative;
    overflow: hidden;
}

.card-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.card:hover .card-body::before {
    left: 100%;
}

/* ====== LOADING STATES ====== */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ====== ACCESSIBILITY IMPROVEMENTS ====== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ====== CUSTOM SCROLLBAR ====== */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ====== PRINT STYLES ====== */
@media print {
    .controls-bar,
    .btn,
    .dropdown {
        display: none !important;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #000;
    }
    
    .badge {
        color: #000 !important;
        background-color: transparent !important;
        border: 1px solid #000;
    }
} 