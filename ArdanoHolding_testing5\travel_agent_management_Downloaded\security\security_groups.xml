<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        
        <!-- Travel Agent Management Category -->
        <record id="module_category_travel_agent" model="ir.module.category">
            <field name="name">Travel Agent Management</field>
            <field name="description">Access rights for Travel Agent Management system</field>
            <field name="sequence">10</field>
        </record>

        <!-- Travel Agent User Group -->
        <record id="group_travel_agent_user" model="res.groups">
            <field name="name">Travel Agent User</field>
            <field name="category_id" ref="module_category_travel_agent"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">
                Travel Agent User can:
                - Create and edit own travel requests
                - View own assigned requests
                - Manage passenger information
                - Add documents and attachments
            </field>
        </record>

        <!-- Travel Agent Manager Group -->
        <record id="group_travel_agent_manager" model="res.groups">
            <field name="name">Travel Agent Manager</field>
            <field name="category_id" ref="module_category_travel_agent"/>
            <field name="implied_ids" eval="[(4, ref('group_travel_agent_user'))]"/>
            <field name="comment">
                Travel Agent Manager can:
                - All Travel Agent User permissions
                - View and manage all travel requests
                - Assign requests to team members
                - Access reporting and analytics
                - Manage vendor relationships
            </field>
        </record>

        <!-- Travel Agent Admin Group -->
        <record id="group_travel_agent_admin" model="res.groups">
            <field name="name">Travel Agent Admin</field>
            <field name="category_id" ref="module_category_travel_agent"/>
            <field name="implied_ids" eval="[(4, ref('group_travel_agent_manager'))]"/>
            <field name="comment">
                Travel Agent Admin can:
                - All Travel Agent Manager permissions
                - System configuration and settings
                - User management and permissions
                - Data export and import
                - System maintenance and troubleshooting
            </field>
        </record>

        <!-- Accounting Team Read-Only Access -->
        <record id="group_travel_agent_accounting" model="res.groups">
            <field name="name">Travel Agent Accounting</field>
            <field name="category_id" ref="module_category_travel_agent"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">
                Travel Agent Accounting can:
                - Read-only access to travel requests
                - View financial information
                - Access to invoicing and billing data
                - Generate financial reports
            </field>
        </record>

    </data>
</odoo> 