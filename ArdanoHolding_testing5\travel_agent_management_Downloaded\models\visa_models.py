# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class VisaCountry(models.Model):
    """Countries that offer visa services"""
    _name = 'visa.country'
    _description = 'Visa Country'
    _order = 'name'

    name = fields.Char('Country Name', required=True)
    code = fields.Char('Country Code', size=3, help="ISO country code")
    flag_emoji = fields.Char('Flag Emoji', help="Country flag emoji")
    active = fields.Boolean('Active', default=True)
    visa_type_ids = fields.One2many('visa.type', 'country_id', string='Visa Types')
    visa_type_count = fields.Integer('Visa Types Count', compute='_compute_visa_type_count', store=True)

    @api.depends('visa_type_ids')
    def _compute_visa_type_count(self):
        for record in self:
            record.visa_type_count = len(record.visa_type_ids)

    def action_view_visa_types(self):
        return {
            'name': _('Visa Types'),
            'type': 'ir.actions.act_window',
            'res_model': 'visa.type',
            'view_mode': 'tree,form',
            'domain': [('country_id', '=', self.id)],
            'context': {'default_country_id': self.id}
        }


class VisaDocumentType(models.Model):
    """Types of documents that can be required for visas"""
    _name = 'visa.document.type'
    _description = 'Visa Document Type'
    _order = 'sequence, name'

    name = fields.Char('Document Name', required=True)
    description = fields.Text('Description')
    sequence = fields.Integer('Sequence', default=10)
    is_required = fields.Boolean('Required by Default', default=True)
    file_type = fields.Selection([
        ('image', 'Image'),
        ('pdf', 'PDF'),
        ('any', 'Any File Type')
    ], string='Allowed File Type', default='any')
    max_file_size = fields.Float('Max File Size (MB)', default=5.0)
    active = fields.Boolean('Active', default=True)

    _sql_constraints = [
        ('name_unique', 'unique(name)', 'Document type name must be unique!')
    ]


class VisaFieldType(models.Model):
    """Types of additional fields that can be required for visas"""
    _name = 'visa.field.type'
    _description = 'Visa Field Type'
    _order = 'sequence, name'

    name = fields.Char('Field Name', required=True)
    field_type = fields.Selection([
        ('char', 'Text'),
        ('text', 'Long Text'),
        ('date', 'Date'),
        ('datetime', 'Date & Time'),
        ('selection', 'Selection'),
        ('boolean', 'Yes/No'),
        ('integer', 'Number'),
        ('float', 'Decimal'),
    ], string='Field Type', required=True, default='char')
    
    selection_options = fields.Text('Selection Options', 
                                   help="For selection fields, enter options separated by new lines")
    sequence = fields.Integer('Sequence', default=10)
    is_required = fields.Boolean('Required by Default', default=True)
    placeholder = fields.Char('Placeholder Text')
    help_text = fields.Text('Help Text')
    active = fields.Boolean('Active', default=True)

    _sql_constraints = [
        ('name_unique', 'unique(name)', 'Field type name must be unique!')
    ]

    @api.constrains('field_type', 'selection_options')
    def _check_selection_options(self):
        for record in self:
            if record.field_type == 'selection' and not record.selection_options:
                raise ValidationError(_("Selection options are required for selection fields."))


class VisaType(models.Model):
    """Types of visas offered by countries"""
    _name = 'visa.type'
    _description = 'Visa Type'
    _order = 'country_id, sequence, name'

    name = fields.Char('Visa Type Name', required=True)
    country_id = fields.Many2one('visa.country', string='Country', required=True, ondelete='cascade')
    description = fields.Text('Description')
    sequence = fields.Integer('Sequence', default=10)

    # Category
    visa_category = fields.Selection([
        ('tourist', 'Tourist'),
        ('business', 'Business'),
        ('medical', 'Medical'),
        ('study', 'Study'),
        ('work', 'Work'),
        ('transit', 'Transit'),
        ('family', 'Family Visit'),
        ('other', 'Other')
    ], string='Visa Category', default='tourist')

    # Pricing
    price = fields.Float('Price', digits='Product Price')
    currency_id = fields.Many2one('res.currency', string='Currency',
                                 default=lambda self: self.env.company.currency_id)

    # Processing time
    processing_days = fields.Integer('Processing Days', help="Estimated processing time in days")
    
    # Requirements
    document_type_ids = fields.Many2many('visa.document.type', 
                                        'visa_type_document_rel', 
                                        'visa_type_id', 'document_type_id',
                                        string='Required Documents')
    
    field_type_ids = fields.Many2many('visa.field.type',
                                     'visa_type_field_rel',
                                     'visa_type_id', 'field_type_id', 
                                     string='Additional Fields Required')
    
    # Status
    active = fields.Boolean('Active', default=True)
    
    # Statistics
    request_count = fields.Integer('Request Count', compute='_compute_request_count', store=True)

    @api.depends('name', 'country_id')
    def name_get(self):
        result = []
        for record in self:
            name = f"{record.country_id.name} - {record.name}"
            result.append((record.id, name))
        return result

    @api.depends()  # This will be computed manually when needed
    def _compute_request_count(self):
        for record in self:
            record.request_count = self.env['visa.request'].search_count([
                ('visa_type_id', '=', record.id)
            ])

    def action_view_requests(self):
        return {
            'name': _('Visa Requests'),
            'type': 'ir.actions.act_window',
            'res_model': 'visa.request',
            'view_mode': 'tree,form',
            'domain': [('visa_type_id', '=', self.id)],
            'context': {'default_visa_type_id': self.id}
        }


class VisaRequest(models.Model):
    """Visa requests from customers"""
    _name = 'visa.request'
    _description = 'Visa Request'
    _order = 'create_date desc'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'portal.mixin']

    name = fields.Char('Request Number', required=True, copy=False, readonly=True,
                      default=lambda self: _('New'))
    
    # Customer info
    partner_id = fields.Many2one('res.partner', string='Customer', required=True, tracking=True)
    
    # Visa info
    visa_type_id = fields.Many2one('visa.type', string='Visa Type', required=True, tracking=True)
    country_id = fields.Many2one('visa.country', string='Country', 
                                related='visa_type_id.country_id', store=True)
    
    # Pricing
    price = fields.Float('Price', related='visa_type_id.price', store=True)
    currency_id = fields.Many2one('res.currency', related='visa_type_id.currency_id', store=True)
    
    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('in_progress', 'In Progress'),
        ('done', 'Done'),
    ], string='Status', default='draft', tracking=True)
    
    # Documents and fields
    document_ids = fields.One2many('visa.request.document', 'request_id', string='Documents')
    field_ids = fields.One2many('visa.request.field', 'request_id', string='Additional Information')
    
    # Dates
    request_date = fields.Date('Request Date', default=fields.Date.today, tracking=True)
    completion_date = fields.Date('Completion Date', tracking=True)
    
    # Notes
    notes = fields.Text('Notes')
    internal_notes = fields.Text('Internal Notes')

    # Document statistics
    total_documents = fields.Integer('Total Documents', compute='_compute_document_stats', store=True)
    uploaded_documents = fields.Integer('Uploaded Documents', compute='_compute_document_stats', store=True)
    approved_documents = fields.Integer('Approved Documents', compute='_compute_document_stats', store=True)
    document_completion_rate = fields.Float('Completion Rate (%)', compute='_compute_document_stats', store=True)

    @api.depends('document_ids', 'document_ids.attachment_id', 'document_ids.is_approved')
    def _compute_document_stats(self):
        """Compute document statistics"""
        for record in self:
            total = len(record.document_ids)
            uploaded = len(record.document_ids.filtered(lambda d: d.attachment_id))
            approved = len(record.document_ids.filtered(lambda d: d.attachment_id and d.is_approved))

            record.total_documents = total
            record.uploaded_documents = uploaded
            record.approved_documents = approved
            record.document_completion_rate = (approved / total * 100) if total > 0 else 0

    def refresh_document_stats(self):
        """Manual method to refresh document statistics"""
        self._compute_document_stats()

    @api.model
    def create(self, vals):
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('visa.request') or _('New')

        # Create the record first
        record = super().create(vals)

        # Then create required documents if visa_type is set
        if record.visa_type_id and not record.document_ids:
            try:
                for doc_type in record.visa_type_id.document_type_ids:
                    self.env['visa.request.document'].create({
                        'request_id': record.id,
                        'document_type_id': doc_type.id,
                    })
            except Exception as e:
                # Log error but don't prevent creation
                import logging
                _logger = logging.getLogger(__name__)
                _logger.warning(f"Failed to auto-create documents: {e}")

        return record

    def action_start_processing(self):
        self.write({'state': 'in_progress'})

    def action_complete(self):
        self.write({
            'state': 'done',
            'completion_date': fields.Date.today()
        })

    def action_reset_to_draft(self):
        self.write({'state': 'draft'})

    def action_create_documents(self):
        """Manual action to create required documents"""
        if not self.visa_type_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Warning',
                    'message': 'Please select a visa type first.',
                    'type': 'warning'
                }
            }

        created_count = 0
        # Create document lines for required documents only if they don't exist
        for doc_type in self.visa_type_id.document_type_ids:
            existing = self.document_ids.filtered(lambda d: d.document_type_id.id == doc_type.id)
            if not existing:
                self.env['visa.request.document'].create({
                    'request_id': self.id,
                    'document_type_id': doc_type.id,
                })
                created_count += 1

        if created_count > 0:
            message = f'Created {created_count} required document(s) successfully.'
            msg_type = 'success'
        else:
            message = 'All required documents already exist.'
            msg_type = 'info'

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Documents Creation',
                'message': message,
                'type': msg_type
            }
        }

    # Temporarily disable onchange to fix the error
    # @api.onchange('visa_type_id')
    # def _onchange_visa_type_id(self):
    #     """Auto-create required documents when visa type changes"""
    #     if self.visa_type_id:
    #         # Clear existing documents first
    #         self.document_ids = [(5, 0, 0)]
    #
    #         # Auto-create documents for new requests
    #         doc_vals = []
    #         for doc_type in self.visa_type_id.document_type_ids:
    #             doc_vals.append((0, 0, {
    #                 'document_type_id': doc_type.id,
    #             }))
    #         self.document_ids = doc_vals

    def action_check_document_completeness(self):
        """Check if all required documents are uploaded and approved"""
        if not self.visa_type_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Warning',
                    'message': 'Please select a visa type first.',
                    'type': 'warning'
                }
            }

        required_docs = self.visa_type_id.document_type_ids
        uploaded_docs = self.document_ids.filtered(lambda d: d.attachment_id)
        approved_docs = self.document_ids.filtered(lambda d: d.attachment_id and d.is_approved)

        missing_docs = []
        unapproved_docs = []

        for req_doc in required_docs:
            doc_record = self.document_ids.filtered(lambda d: d.document_type_id.id == req_doc.id)
            if not doc_record or not doc_record.attachment_id:
                missing_docs.append(req_doc.name)
            elif not doc_record.is_approved:
                unapproved_docs.append(req_doc.name)

        if missing_docs:
            message = f"Missing documents: {', '.join(missing_docs)}"
            msg_type = 'warning'
        elif unapproved_docs:
            message = f"Unapproved documents: {', '.join(unapproved_docs)}"
            msg_type = 'warning'
        else:
            message = "All required documents are uploaded and approved!"
            msg_type = 'success'

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Document Status',
                'message': message,
                'type': msg_type
            }
        }

    def _get_portal_return_action(self):
        """Return action for portal access"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_url',
            'url': '/my/visa_request/%s' % self.id,
            'target': 'self',
        }

    # Simplified - no automatic creation


class VisaRequestDocument(models.Model):
    """Documents uploaded for visa requests"""
    _name = 'visa.request.document'
    _description = 'Visa Request Document'
    _order = 'sequence, document_type_id'

    request_id = fields.Many2one('visa.request', string='Visa Request', required=True, ondelete='cascade')
    document_type_id = fields.Many2one('visa.document.type', string='Document Type', required=True)
    sequence = fields.Integer('Sequence', related='document_type_id.sequence', store=True)

    # File - Direct binary field approach (simpler and more reliable)
    datas = fields.Binary('File Content', attachment=True)
    filename = fields.Char('Filename')

    # File info
    file_size_bytes = fields.Integer('File Size (bytes)')
    file_size = fields.Float('File Size (MB)', compute='_compute_file_size_mb', store=True)
    mimetype = fields.Char('MIME Type')
    file_type = fields.Char('File Type', compute='_compute_file_type', store=True)

    # Preview fields
    is_image = fields.Boolean('Is Image', compute='_compute_file_type', store=True)
    is_pdf = fields.Boolean('Is PDF', compute='_compute_file_type', store=True)

    # Keep attachment_id for compatibility but make it computed
    attachment_id = fields.Many2one('ir.attachment', string='Attachment', compute='_compute_attachment_id')

    # Status
    is_approved = fields.Boolean('Approved', default=False)
    notes = fields.Text('Notes')

    @api.depends('datas')
    def _compute_attachment_id(self):
        """Find the attachment record for this binary data"""
        for record in self:
            if record.datas:
                # Find attachment by searching for this record's binary data
                attachment = self.env['ir.attachment'].search([
                    ('res_model', '=', self._name),
                    ('res_id', '=', record.id),
                    ('res_field', '=', 'datas')
                ], limit=1)
                record.attachment_id = attachment.id if attachment else False
            else:
                record.attachment_id = False

    @api.depends('file_size_bytes')
    def _compute_file_size_mb(self):
        """Compute file size in MB"""
        for record in self:
            record.file_size = record.file_size_bytes / (1024 * 1024) if record.file_size_bytes else 0.0

    @api.depends('mimetype')
    def _compute_file_type(self):
        """Compute file type and flags from mimetype"""
        for record in self:
            mimetype = record.mimetype or ''
            if mimetype.startswith('image/'):
                record.file_type = 'Image'
                record.is_image = True
                record.is_pdf = False
            elif mimetype == 'application/pdf':
                record.file_type = 'PDF'
                record.is_image = False
                record.is_pdf = True
            else:
                record.file_type = 'Document'
                record.is_image = False
                record.is_pdf = False

    @api.model
    def create(self, vals):
        """Override create to handle file metadata"""
        record = super().create(vals)
        if record.datas and not record.filename:
            # Try to get filename from context or generate one
            filename = self.env.context.get('filename') or f"document_{record.id}"
            record.filename = filename
        record._update_file_metadata()
        return record

    def write(self, vals):
        """Override write to handle file metadata"""
        result = super().write(vals)
        if 'datas' in vals:
            for record in self:
                record._update_file_metadata()
        return result

    def _update_file_metadata(self):
        """Update file metadata from attachment"""
        for record in self:
            if record.datas:
                # Find the attachment created by the binary field
                attachment = self.env['ir.attachment'].search([
                    ('res_model', '=', self._name),
                    ('res_id', '=', record.id),
                    ('res_field', '=', 'datas')
                ], limit=1)

                if attachment:
                    # Update metadata from attachment
                    record.write({
                        'file_size_bytes': attachment.file_size,
                        'mimetype': attachment.mimetype,
                        'filename': record.filename or attachment.name,
                    })
            else:
                # Clear metadata if no file
                record.write({
                    'file_size_bytes': 0,
                    'mimetype': False,
                    'filename': False,
                })

    def name_get(self):
        """Custom name_get to show document type and filename"""
        result = []
        for record in self:
            if record.filename:
                name = f"{record.document_type_id.name}: {record.filename}"
            else:
                name = record.document_type_id.name or 'Document'
            result.append((record.id, name))
        return result

    def action_download_file(self):
        """Action to download the attached file"""
        if not self.datas:
            raise UserError(_('No file attached to download.'))

        # Create a temporary attachment for download
        attachment = self.env['ir.attachment'].create({
            'name': self.filename or 'document',
            'datas': self.datas,
            'mimetype': self.mimetype,
            'res_model': self._name,
            'res_id': self.id,
        })

        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'new',
        }

    def action_preview_file(self):
        """Action to preview the file in a popup"""
        if not self.datas:
            raise UserError(_('No file attached to preview.'))

        return {
            'type': 'ir.actions.act_window',
            'name': f'Preview: {self.filename}',
            'res_model': 'visa.request.document',
            'res_id': self.id,
            'view_mode': 'form',
            'view_id': self.env.ref('travel_agent_management.view_visa_request_document_preview_form').id,
            'target': 'new',
            'context': {'preview_mode': True}
        }

    @api.onchange('datas')
    def _onchange_datas(self):
        """Update file metadata when file is uploaded"""
        if self.datas:
            # Get filename from context if available
            filename = self.env.context.get('filename')
            if filename and not self.filename:
                self.filename = filename

            # Trigger metadata update
            self._update_file_metadata()
        else:
            # Clear metadata when file is removed
            self.filename = False
            self.file_size_bytes = 0
            self.mimetype = False



    def _validate_file(self):
        """Validate uploaded file against document type requirements"""
        if not self.datas or not self.document_type_id:
            return

        doc_type = self.document_type_id

        # Check file size
        if doc_type.max_file_size > 0:
            file_size_mb = self.file_size or 0
            if file_size_mb > doc_type.max_file_size:
                raise UserError(_(
                    'File size (%.2f MB) exceeds the maximum allowed size (%.2f MB) for %s.'
                ) % (file_size_mb, doc_type.max_file_size, doc_type.name))

        # Check file type
        if doc_type.file_type != 'any':
            mimetype = self.mimetype or ''
            if doc_type.file_type == 'image' and not mimetype.startswith('image/'):
                raise UserError(_(
                    'Only image files are allowed for %s. Please upload a valid image file.'
                ) % doc_type.name)
            elif doc_type.file_type == 'pdf' and mimetype != 'application/pdf':
                raise UserError(_(
                    'Only PDF files are allowed for %s. Please upload a valid PDF file.'
                ) % doc_type.name)

    def refresh_all_computed_fields(self):
        """Refresh all computed fields for this document"""
        self._compute_file_size_mb()
        self._compute_file_type()
        # Also refresh parent request stats
        if self.request_id:
            self.request_id.refresh_document_stats()


class VisaRequestField(models.Model):
    """Additional field values for visa requests"""
    _name = 'visa.request.field'
    _description = 'Visa Request Field'
    _order = 'sequence, field_type_id'

    request_id = fields.Many2one('visa.request', string='Visa Request', required=True, ondelete='cascade')
    field_type_id = fields.Many2one('visa.field.type', string='Field Type', required=True)
    sequence = fields.Integer('Sequence', related='field_type_id.sequence', store=True)

    # Helper field for easier access
    field_type = fields.Selection(related='field_type_id.field_type', store=True, readonly=True)

    # Values for different field types
    value_char = fields.Char('Text Value')
    value_text = fields.Text('Long Text Value')
    value_date = fields.Date('Date Value')
    value_datetime = fields.Datetime('DateTime Value')
    value_selection = fields.Char('Selection Value')
    value_boolean = fields.Boolean('Boolean Value')
    value_integer = fields.Integer('Integer Value')
    value_float = fields.Float('Float Value')

    # Display value
    display_value = fields.Char('Value', compute='_compute_display_value', store=True)

    @api.depends('field_type_id', 'value_char', 'value_text', 'value_date', 'value_datetime',
                 'value_selection', 'value_boolean', 'value_integer', 'value_float')
    def _compute_display_value(self):
        for record in self:
            field_type = record.field_type_id.field_type
            if field_type == 'char':
                record.display_value = record.value_char or ''
            elif field_type == 'text':
                record.display_value = record.value_text or ''
            elif field_type == 'date':
                record.display_value = str(record.value_date) if record.value_date else ''
            elif field_type == 'datetime':
                record.display_value = str(record.value_datetime) if record.value_datetime else ''
            elif field_type == 'selection':
                record.display_value = record.value_selection or ''
            elif field_type == 'boolean':
                record.display_value = _('Yes') if record.value_boolean else _('No')
            elif field_type == 'integer':
                record.display_value = str(record.value_integer) if record.value_integer else '0'
            elif field_type == 'float':
                record.display_value = str(record.value_float) if record.value_float else '0.0'
            else:
                record.display_value = ''
