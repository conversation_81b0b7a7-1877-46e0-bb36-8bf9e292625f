<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Visa Field Type Tree View -->
        <record id="view_visa_field_type_tree" model="ir.ui.view">
            <field name="name">visa.field.type.tree</field>
            <field name="model">visa.field.type</field>
            <field name="arch" type="xml">
                <tree string="Field Types" editable="bottom">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="field_type"/>
                    <field name="is_required"/>
                    <field name="placeholder"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Visa Field Type Form View -->
        <record id="view_visa_field_type_form" model="ir.ui.view">
            <field name="name">visa.field.type.form</field>
            <field name="model">visa.field.type</field>
            <field name="arch" type="xml">
                <form string="Field Type">
                    <sheet>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                                attrs="{'invisible': [('active', '=', True)]}"/>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Field Name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="field_type"/>
                                <field name="sequence"/>
                                <field name="is_required"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="placeholder"/>
                            </group>
                        </group>
                        
                        <group string="Selection Options" 
                               attrs="{'invisible': [('field_type', '!=', 'selection')]}">
                            <field name="selection_options" nolabel="1" 
                                   placeholder="Enter each option on a new line:&#10;Option 1&#10;Option 2&#10;Option 3"/>
                        </group>
                        
                        <group string="Help Text">
                            <field name="help_text" nolabel="1"
                                   placeholder="Enter help text to guide users..."/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Visa Field Type Search View -->
        <record id="view_visa_field_type_search" model="ir.ui.view">
            <field name="name">visa.field.type.search</field>
            <field name="model">visa.field.type</field>
            <field name="arch" type="xml">
                <search string="Search Field Types">
                    <field name="name"/>
                    <field name="field_type"/>
                    <separator/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="Required" name="required" domain="[('is_required', '=', True)]"/>
                    <filter string="Optional" name="optional" domain="[('is_required', '=', False)]"/>
                    <separator/>
                    <filter string="Text Fields" name="text_fields" 
                            domain="[('field_type', 'in', ['char', 'text'])]"/>
                    <filter string="Date Fields" name="date_fields" 
                            domain="[('field_type', 'in', ['date', 'datetime'])]"/>
                    <filter string="Selection Fields" name="selection_fields" 
                            domain="[('field_type', '=', 'selection')]"/>
                    <group expand="0" string="Group By">
                        <filter string="Field Type" name="group_field_type" context="{'group_by': 'field_type'}"/>
                        <filter string="Required" name="group_required" context="{'group_by': 'is_required'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Visa Field Type Action -->
        <record id="action_visa_field_type" model="ir.actions.act_window">
            <field name="name">Additional Field Types</field>
            <field name="res_model">visa.field.type</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_visa_field_type_search"/>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first field type!
                </p>
                <p>
                    Define additional fields that can be required for visa applications,
                    such as profession, mother's name, phone number, travel dates, etc.
                </p>
            </field>
        </record>

    </data>
</odoo>
