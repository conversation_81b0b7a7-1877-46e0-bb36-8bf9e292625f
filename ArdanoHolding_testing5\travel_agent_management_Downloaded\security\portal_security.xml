<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        
        <!-- Portal User Security Rules -->
        <record id="travel_ticket_request_portal_rule" model="ir.rule">
            <field name="name">Travel Request: Portal Users</field>
            <field name="model_id" ref="model_travel_ticket_request"/>
            <field name="domain_force">[('customer_id', '=', user.partner_id.commercial_partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- Portal Users can only see passengers related to their travel requests -->
        <record id="travel_passenger_line_portal_rule" model="ir.rule">
            <field name="name">Travel Passenger Line: Portal Users</field>
            <field name="model_id" ref="model_travel_passenger_line"/>
            <field name="domain_force">[('request_id.customer_id', '=', user.partner_id.commercial_partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- Portal Users can see passenger records they are associated with -->
        <record id="travel_passenger_portal_rule" model="ir.rule">
            <field name="name">Travel Passenger: Portal Users</field>
            <field name="model_id" ref="model_travel_passenger"/>
            <field name="domain_force">[
                '|',
                ('contact_id', '=', user.partner_id.id),
                ('contact_id', 'child_of', user.partner_id.commercial_partner_id.id)
            ]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- Portal users can access visa requests -->
        <record id="visa_request_portal_rule" model="ir.rule">
            <field name="name">Visa Request: Portal Users</field>
            <field name="model_id" ref="model_visa_request"/>
            <field name="domain_force">[('partner_id', '=', user.partner_id.commercial_partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- Portal users can access their visa request documents -->
        <record id="visa_request_document_portal_rule" model="ir.rule">
            <field name="name">Visa Request Document: Portal Users</field>
            <field name="model_id" ref="model_visa_request_document"/>
            <field name="domain_force">[('request_id.partner_id', '=', user.partner_id.commercial_partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- Portal users can access their visa request fields -->
        <record id="visa_request_field_portal_rule" model="ir.rule">
            <field name="name">Visa Request Field: Portal Users</field>
            <field name="model_id" ref="model_visa_request_field"/>
            <field name="domain_force">[('request_id.partner_id', '=', user.partner_id.commercial_partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- Portal users can access transfer requests -->
        <record id="transfer_request_portal_rule" model="ir.rule">
            <field name="name">Transfer Request: Portal Users</field>
            <field name="model_id" ref="model_transfer_request"/>
            <field name="domain_force">[('partner_id', '=', user.partner_id.commercial_partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- Portal users can read supplier information for their transfer requests -->
        <record id="res_partner_supplier_portal_rule" model="ir.rule">
            <field name="name">Supplier Partners: Portal Users</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="domain_force">[
                '|',
                ('id', '=', user.partner_id.commercial_partner_id.id),
                '&amp;',
                ('is_company', '=', True),
                ('supplier_rank', '>', 0)
            ]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- Portal users can access hotel booking requests -->
        <record id="hotel_booking_request_portal_rule" model="ir.rule">
            <field name="name">Hotel Booking Request: Portal Users</field>
            <field name="model_id" ref="model_hotel_booking_request"/>
            <field name="domain_force">[('partner_id', '=', user.partner_id.commercial_partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- Portal users can access medical insurance requests -->
        <record id="medical_insurance_request_portal_rule" model="ir.rule">
            <field name="name">Medical Insurance Request: Portal Users</field>
            <field name="model_id" ref="model_medical_insurance_request"/>
            <field name="domain_force">[('partner_id', '=', user.partner_id.commercial_partner_id.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
        </record>

        <!-- Portal users can access their passenger attachments -->
        <record id="ir_attachment_rule_portal_user" model="ir.rule">
            <field name="name">Portal Travel: Access Own Passenger Attachments</field>
            <field name="model_id" ref="base.model_ir_attachment"/>
            <field name="domain_force">[
                '|',
                ('public', '=', True),
                ('res_model', '!=', 'travel.passenger')
            ]</field>
            <field name="groups" eval="[(4, ref('base.group_portal'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

    </data>
</odoo> 