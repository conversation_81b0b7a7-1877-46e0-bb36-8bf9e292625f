<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Travel Passenger Tree View -->
        <record id="view_travel_passenger_tree" model="ir.ui.view">
            <field name="name">travel.passenger.tree</field>
            <field name="model">travel.passenger</field>
            <field name="arch" type="xml">
                <tree decoration-danger="passport_status=='expired'" 
                      decoration-warning="passport_status=='expiring'">
                    <field name="name"/>
                    <field name="customer_id"/>
                    <field name="contact_id"/>
                    <field name="employee_id"/>
                    <field name="nationality_id"/>
                    <field name="passport_number"/>
                    <field name="passport_expiration"/>
                    <field name="passport_status"/>
                    <field name="days_until_passport_expiry"/>
                    <field name="email"/>
                    <field name="phone"/>
                    <field name="travel_count"/>
                    <field name="last_travel_date"/>
                </tree>
            </field>
        </record>
        
        <!-- Travel Passenger Form View -->
        <record id="view_travel_passenger_form" model="ir.ui.view">
            <field name="name">travel.passenger.form</field>
            <field name="model">travel.passenger</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_check_passport_validity" string="Check Passport" 
                                type="object" class="btn-primary"/>
                    </header>
                    
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_travel_history" type="object" 
                                    class="oe_stat_button" icon="fa-plane">
                                <field name="travel_count" widget="statinfo" string="Travels"/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Full Name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group name="basic_info" string="Basic Information">
                                <field name="customer_id" options="{'no_create': True}"/>
                                <field name="contact_id" domain="['|', ('parent_id', '=', customer_id), ('id', '=', customer_id)]"/>
                                <field name="employee_id"/>
                                <field name="date_of_birth"/>
                                <field name="gender"/>
                                <field name="nationality_id"/>
                            </group>
                            <group name="contact_info" string="Contact Information">
                                <field name="email" widget="email"/>
                                <field name="phone" widget="phone"/>
                                <field name="travel_count"/>
                                <field name="last_travel_date"/>
                            </group>
                        </group>
                        
                        <group name="passport_group" string="Passport Information">
                            <group>
                                <field name="passport_number"/>
                                <field name="passport_issue_date"/>
                                <field name="passport_issue_place"/>
                            </group>
                            <group>
                                <field name="passport_expiration"/>
                                <field name="passport_status" readonly="1"/>
                                <field name="days_until_passport_expiry" readonly="1"/>
                            </group>
                        </group>
                        
                        <group name="preferences" string="Travel Preferences">
                            <group>
                                <field name="seat_preference"/>
                                <field name="meal_preference"/>
                            </group>
                            <group>
                                <field name="special_requirements" placeholder="Medical needs, accessibility requirements, etc."/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Travel History" name="travel_history">
                                <field name="passenger_line_ids" readonly="1">
                                    <tree decoration-info="status=='draft'" 
                                          decoration-warning="status=='booked'" 
                                          decoration-success="status=='ticketed'"
                                          decoration-muted="status=='cancelled'">
                                        <field name="request_id"/>
                                        <field name="travel_purpose"/>
                                        <field name="departure_route"/>
                                        <field name="departure_date"/>
                                        <field name="return_route"/>
                                        <field name="return_date"/>
                                        <field name="ticket_class"/>
                                        <field name="status"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Passport Documents" name="passport_docs">
                                <field name="passport_attachment_ids" widget="many2many_binary" string="Passport Scans"/>
                            </page>
                            <page string="Visa Documents" name="visa_docs">
                                <field name="visa_attachment_ids" widget="many2many_binary" string="Visa Scans"/>
                            </page>
                            <page string="Other Documents" name="other_docs">
                                <field name="other_attachment_ids" widget="many2many_binary" string="Other Documents"/>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Travel Passenger Search View -->
        <record id="view_travel_passenger_search" model="ir.ui.view">
            <field name="name">travel.passenger.search</field>
            <field name="model">travel.passenger</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="Name"/>
                    <field name="passport_number" string="Passport Number"/>
                    <field name="employee_id" string="Employee ID"/>
                    <field name="customer_id" string="Customer"/>
                    <field name="email" string="Email"/>
                    <field name="nationality_id" string="Nationality"/>
                    
                    <filter string="Passport Expired" name="passport_expired" domain="[('passport_status', '=', 'expired')]"/>
                    <filter string="Passport Expiring Soon" name="passport_expiring" domain="[('passport_status', '=', 'expiring')]"/>
                    <filter string="Passport Valid" name="passport_valid" domain="[('passport_status', '=', 'valid')]"/>
                    <separator/>
                    <filter string="Frequent Travelers" name="frequent_travelers" domain="[('travel_count', '&gt;=', 5)]"/>
                    <filter string="New Travelers" name="new_travelers" domain="[('travel_count', '=', 0)]"/>
                    <separator/>
                    <filter string="Recent Travelers" name="recent_travelers" 
                            domain="[('last_travel_date', '&gt;=', (datetime.datetime.now() - datetime.timedelta(days=90)).strftime('%Y-%m-%d'))]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Customer" name="group_customer" context="{'group_by': 'customer_id'}"/>
                        <filter string="Nationality" name="group_nationality" context="{'group_by': 'nationality_id'}"/>
                        <filter string="Passport Status" name="group_passport_status" context="{'group_by': 'passport_status'}"/>
                        <filter string="Gender" name="group_gender" context="{'group_by': 'gender'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Travel Passenger Action -->
        <record id="action_travel_passenger" model="ir.actions.act_window">
            <field name="name">Passengers</field>
            <field name="res_model">travel.passenger</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_travel_passenger_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Add your first passenger!
                </p>
                <p>
                    Manage passenger information including passport details, travel preferences, and travel history.
                    Keep track of passport expiration dates and ensure all documents are up to date.
                </p>
            </field>
        </record>
        
    </data>
</odoo> 