<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Transfer Request Tree View -->
        <record id="view_transfer_request_tree" model="ir.ui.view">
            <field name="name">transfer.request.tree</field>
            <field name="model">transfer.request</field>
            <field name="arch" type="xml">
                <tree decoration-info="state=='draft'" decoration-success="state=='confirmed'" decoration-muted="state=='cancelled'">
                    <field name="name"/>
                    <field name="customer_id"/>
                    <field name="transfer_route"/>
                    <field name="departure_date"/>
                    <field name="departure_time" widget="float_time"/>
                    <field name="passenger_count"/>
                    <field name="vehicle_type"/>
                    <field name="supplier_id"/>
                    <field name="customer_price" widget="monetary"/>
                    <field name="profit_amount" widget="monetary"/>
                    <field name="sale_order_id" optional="hide"/>
                    <field name="purchase_order_id" optional="hide"/>
                    <field name="state" widget="badge" decoration-info="state=='draft'" decoration-success="state=='confirmed'" decoration-muted="state=='cancelled'"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <!-- Transfer Request Form View -->
        <record id="view_transfer_request_form" model="ir.ui.view">
            <field name="name">transfer.request.form</field>
            <field name="model">transfer.request</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_confirm" type="object" string="Confirm"
                                class="btn-primary" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                        <button name="action_create_invoice" type="object" string="Create Invoice"
                                class="btn-primary"
                                attrs="{'invisible': ['|', '|', ('customer_price', '=', 0), ('id', '=', False), ('sale_order_id', '!=', False)]}"/>
                        <button name="action_create_vendor_bill" type="object" string="Create Bill"
                                class="btn-primary"
                                attrs="{'invisible': ['|', '|', ('supplier_cost', '=', 0), ('id', '=', False), ('purchase_order_id', '!=', False)]}"/>
                        <button name="action_cancel" type="object" string="Cancel"
                                class="btn-secondary" attrs="{'invisible': [('state', '=', 'cancelled')]}"/>
                        <button name="action_set_to_draft" type="object" string="Set to Draft"
                                class="btn-secondary" attrs="{'invisible': [('state', '=', 'draft')]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,confirmed,cancelled"/>
                    </header>
                    
                    <sheet>
                        <!-- Stat Buttons -->
                        <div class="oe_button_box" name="button_box">
                            <!-- Sales Order Button -->
                            <button name="action_view_sale_order" type="object"
                                    class="oe_stat_button" icon="fa-file-text-o"
                                    attrs="{'invisible': [('sale_order_id', '=', False)]}">
                                <field name="sale_order_id" widget="statinfo" string="Sales Order"/>
                            </button>

                            <!-- Purchase Order Button -->
                            <button name="action_view_purchase_order" type="object"
                                    class="oe_stat_button" icon="fa-file-o"
                                    attrs="{'invisible': [('purchase_order_id', '=', False)]}">
                                <field name="purchase_order_id" widget="statinfo" string="Purchase Order"/>
                            </button>
                        </div>

                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>

                        <group>
                            <group string="Transfer Details">
                                <field name="customer_id" options="{'no_create': True}"/>
                                <field name="transfer_from"/>
                                <field name="transfer_to"/>
                                <field name="transfer_route" readonly="1"/>
                                <field name="passenger_count"/>
                                <field name="vehicle_type"/>
                                <field name="contact_phone"/>
                            </group>
                        </group>
                        
                        <group>
                            <group string="Departure">
                                <field name="departure_date"/>
                                <field name="departure_time" widget="float_time"/>
                            </group>
                            
                            <group string="Arrival">
                                <field name="arrive_date"/>
                                <field name="arrive_time" widget="float_time"/>
                            </group>
                        </group>
                        
                        <group>
                            <group string="Flight Information (Optional)">
                                <field name="flight_number"/>
                            </group>

                            <group>
                            </group>
                        </group>
                        
                        <group>
                            <group string="Pricing">
                                <field name="currency_id" invisible="1"/>
                                <field name="supplier_id" options="{'no_create': True}"/>
                                <field name="supplier_cost" widget="monetary"/>
                                <field name="customer_price" widget="monetary"/>
                                <field name="profit_amount" widget="monetary" readonly="1"
                                       decoration-success="profit_amount &gt; 0"
                                       decoration-danger="profit_amount &lt; 0"/>
                                <field name="profit_percentage" readonly="1" widget="percentage"
                                       decoration-success="profit_percentage &gt; 15"
                                       decoration-warning="profit_percentage &gt;= 5 and profit_percentage &lt;= 15"
                                       decoration-danger="profit_percentage &lt; 5"/>
                            </group>

                            <group string="Related Orders" attrs="{'invisible': [('sale_order_id', '=', False), ('purchase_order_id', '=', False)]}">
                                <field name="sale_order_id" readonly="1" attrs="{'invisible': [('sale_order_id', '=', False)]}"/>
                                <field name="purchase_order_id" readonly="1" attrs="{'invisible': [('purchase_order_id', '=', False)]}"/>
                            </group>
                        </group>


                        
                        <notebook>
                            <page string="Special Requirements">
                                <field name="special_requirements" placeholder="Any special requirements like child seats, wheelchair access, etc."/>
                            </page>
                            
                            <page string="Notes">
                                <group>
                                    <group string="Customer Notes">
                                        <field name="customer_notes" nolabel="1" placeholder="Notes visible to customer"/>
                                    </group>
                                    <group string="Internal Notes">
                                        <field name="notes" nolabel="1" placeholder="Internal notes for staff"/>
                                    </group>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Transfer Request Search View -->
        <record id="view_transfer_request_search" model="ir.ui.view">
            <field name="name">transfer.request.search</field>
            <field name="model">transfer.request</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="Reference"/>
                    <field name="customer_id" string="Customer"/>
                    <field name="transfer_from" string="From"/>
                    <field name="transfer_to" string="To"/>
                    <field name="supplier_id" string="Supplier"/>
                    <field name="flight_number" string="Flight"/>
                    <field name="vehicle_type" string="Vehicle"/>
                    
                    <filter name="draft" string="Draft" domain="[('state', '=', 'draft')]"/>
                    <filter name="confirmed" string="Confirmed" domain="[('state', '=', 'confirmed')]"/>
                    <filter name="cancelled" string="Cancelled" domain="[('state', '=', 'cancelled')]"/>
                    
                    <separator/>
                    <filter name="today" string="Today" domain="[('departure_date', '=', context_today())]"/>
                    <filter name="this_week" string="This Week" domain="[('departure_date', '&gt;=', (context_today() - datetime.timedelta(days=7)))]"/>
                    <filter name="this_month" string="This Month" domain="[('departure_date', '&gt;=', (context_today() - datetime.timedelta(days=30)))]"/>

                    <separator/>
                    <filter name="profitable" string="Profitable" domain="[('profit_amount', '&gt;', 0)]"/>
                    <filter name="loss" string="Loss" domain="[('profit_amount', '&lt;', 0)]"/>
                    
                    <group expand="0" string="Group By">
                        <filter name="group_customer" string="Customer" context="{'group_by': 'customer_id'}"/>
                        <filter name="group_supplier" string="Supplier" context="{'group_by': 'supplier_id'}"/>
                        <filter name="group_state" string="Status" context="{'group_by': 'state'}"/>
                        <filter name="group_departure_date" string="Departure Date" context="{'group_by': 'departure_date'}"/>
                        <filter name="group_vehicle_type" string="Vehicle Type" context="{'group_by': 'vehicle_type'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Transfer Request Kanban View -->
        <record id="view_transfer_request_kanban" model="ir.ui.view">
            <field name="name">transfer.request.kanban</field>
            <field name="model">transfer.request</field>
            <field name="arch" type="xml">
                <kanban default_group_by="state" class="o_kanban_small_column">
                    <field name="name"/>
                    <field name="customer_id"/>
                    <field name="transfer_route"/>
                    <field name="departure_date"/>
                    <field name="departure_time"/>
                    <field name="passenger_count"/>
                    <field name="customer_price"/>
                    <field name="profit_amount"/>
                    <field name="state"/>
                    <field name="currency_id"/>
                    
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <br/>
                                        <span class="o_kanban_record_subtitle">
                                            <field name="customer_id"/>
                                        </span>
                                    </div>
                                    <div class="o_kanban_record_top_right">
                                        <span class="badge badge-pill" t-attf-class="badge-#{record.state.raw_value == 'confirmed' ? 'success' : record.state.raw_value == 'cancelled' ? 'secondary' : 'info'}">
                                            <field name="state"/>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="o_kanban_record_body">
                                    <div>
                                        <i class="fa fa-route"/> <field name="transfer_route"/>
                                    </div>
                                    <div>
                                        <i class="fa fa-calendar"/> <field name="departure_date"/>
                                        <i class="fa fa-clock-o"/> <field name="departure_time" widget="float_time"/>
                                    </div>
                                    <div>
                                        <i class="fa fa-users"/> <field name="passenger_count"/> passengers
                                    </div>
                                </div>
                                
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span>Price: <field name="customer_price" widget="monetary"/></span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span t-attf-class="#{record.profit_amount.raw_value >= 0 ? 'text-success' : 'text-danger'}">
                                            Profit: <field name="profit_amount" widget="monetary"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Transfer Request Action -->
        <record id="action_transfer_request" model="ir.actions.act_window">
            <field name="name">Transfer Requests</field>
            <field name="res_model">transfer.request</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="search_view_id" ref="view_transfer_request_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No transfer requests yet!
                </p>
                <p>
                    Create your first transfer request to manage transportation services.
                </p>
            </field>
        </record>

        <!-- Menu Items -->
        <menuitem id="menu_transfer_management"
                  name="Transfer Management"
                  parent="menu_travel_agent_main"
                  sequence="30"/>

        <menuitem id="menu_transfer_requests"
                  name="Transfer Requests"
                  parent="menu_transfer_management"
                  action="action_transfer_request"
                  sequence="10"/>

    </data>
</odoo>
