# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class TravelFlightSegment(models.Model):
    _name = 'travel.flight.segment'
    _description = 'Flight Ticket/Segment'
    _order = 'passenger_line_id, sequence, departure_date'

    sequence = fields.Integer(
        string='Order',
        default=10,
        help="Order of this ticket in the journey"
    )
    
    passenger_line_id = fields.Many2one(
        'travel.passenger.line',
        string='Passenger Line',
        required=True,
        ondelete='cascade'
    )
    
    # Essential ticket information (same fields as passenger line for simplicity)
    departure_route = fields.Char(
        string='Route',
        required=True,
        placeholder="e.g. New York (JFK) → London (LHR)",
        help="Flight route description"
    )
    
    departure_date = fields.Datetime(
        string='Departure Date',
        required=True
    )
    
    return_route = fields.Char(
        string='Return Route',
        placeholder="e.g. London (LHR) → New York (JFK) (if applicable)"
    )
    
    return_date = fields.Datetime(
        string='Return Date'
    )
    
    # Basic flight info
    flight_number = fields.Char(
        string='Flight Number',
        placeholder="e.g. EK001, BA123"
    )
    
    airline = fields.Char(
        string='Airline',
        placeholder="e.g. Emirates, British Airways"
    )
    
    # Ticket information
    ticket_class = fields.Selection([
        ('economy', 'Economy'),
        ('premium_economy', 'Premium Economy'),
        ('business', 'Business'),
        ('first', 'First Class')
    ], string='Class', required=True, default='economy')
    
    ticket_number = fields.Char(
        string='Ticket Number',
        placeholder="Ticket confirmation number"
    )
    
    # Pricing (same as main ticket)
    vendor_cost = fields.Monetary(
        string='Vendor Cost',
        currency_field='cost_currency_id'
    )
    
    customer_price = fields.Monetary(
        string='Customer Price',
        currency_field='price_currency_id'
    )
    
    margin = fields.Monetary(
        string='Margin',
        compute='_compute_margin',
        store=True,
        currency_field='price_currency_id'
    )
    
    cost_currency_id = fields.Many2one(
        'res.currency',
        string='Cost Currency',
        related='passenger_line_id.cost_currency_id',
        store=True
    )
    
    price_currency_id = fields.Many2one(
        'res.currency',
        string='Price Currency',
        related='passenger_line_id.price_currency_id',
        store=True
    )
    
    # Status
    status = fields.Selection([
        ('draft', 'Draft'),
        ('booked', 'Booked'),
        ('ticketed', 'Ticketed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft')
    
    # Relations to parent
    passenger_id = fields.Many2one(
        'travel.passenger',
        related='passenger_line_id.passenger_id',
        string='Passenger',
        store=True
    )
    
    request_id = fields.Many2one(
        'travel.ticket.request',
        related='passenger_line_id.request_id',
        string='Travel Request',
        store=True
    )
    
    # Simple compute methods
    @api.depends('vendor_cost', 'customer_price')
    def _compute_margin(self):
        for segment in self:
            segment.margin = segment.customer_price - segment.vendor_cost
    
    @api.constrains('departure_date', 'return_date')
    def _check_flight_dates(self):
        for segment in self:
            if segment.return_date and segment.departure_date:
                if segment.return_date <= segment.departure_date:
                    raise ValidationError(_("Return date must be after departure date."))
    
    def name_get(self):
        result = []
        for segment in self:
            name = f"Ticket {segment.sequence}"
            if segment.departure_route:
                name = f"{name}: {segment.departure_route}"
            if segment.departure_date:
                name = f"{name} ({segment.departure_date.strftime('%Y-%m-%d')})"
            result.append((segment.id, name))
        return result 