/* Visa Request Document Styles */

.visa_document_preview {
    max-width: 100%;
    text-align: center;
    padding: 20px;
}

.visa_document_preview img {
    max-width: 100%;
    max-height: 600px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.document_progress_bar {
    margin-top: 15px;
    height: 25px;
}

.document_progress_bar .progress-bar {
    background-color: #28a745;
    transition: width 0.3s ease;
}

.document_progress_bar .progress-bar.warning {
    background-color: #ffc107;
}

.document_progress_bar .progress-bar.danger {
    background-color: #dc3545;
}

.document_stats {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 1rem 0;
}

.document_stats h4 {
    color: #495057;
    margin-bottom: 0.75rem;
}

.document_type_badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.document_type_badge.image {
    color: #155724;
    background-color: #d4edda;
}

.document_type_badge.pdf {
    color: #721c24;
    background-color: #f8d7da;
}

.document_type_badge.document {
    color: #0c5460;
    background-color: #d1ecf1;
}

.file_upload_area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: border-color 0.15s ease-in-out;
}

.file_upload_area:hover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.file_upload_area.dragover {
    border-color: #28a745;
    background-color: #d4edda;
}

.document_actions {
    margin-top: 10px;
}

.document_actions .btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

.document_preview_modal .modal-dialog {
    max-width: 90%;
    width: 90%;
}

.document_preview_modal .modal-body {
    padding: 0;
}

.document_preview_modal iframe {
    width: 100%;
    height: 70vh;
    border: none;
}

/* Tree view improvements */
.o_list_view .o_data_row.decoration-success {
    background-color: #d4edda !important;
}

.o_list_view .o_data_row.decoration-warning {
    background-color: #fff3cd !important;
}

.o_list_view .o_data_row.decoration-info {
    background-color: #d1ecf1 !important;
}

/* File size formatting */
.file_size_small {
    color: #28a745;
    font-weight: bold;
}

.file_size_medium {
    color: #ffc107;
    font-weight: bold;
}

.file_size_large {
    color: #dc3545;
    font-weight: bold;
}

/* Status indicators */
.status_indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.status_indicator.uploaded {
    background-color: #28a745;
}

.status_indicator.pending {
    background-color: #ffc107;
}

.status_indicator.missing {
    background-color: #dc3545;
}

.status_indicator.approved {
    background-color: #007bff;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .document_stats .row {
        flex-direction: column;
    }
    
    .document_stats .col-md-3 {
        margin-bottom: 0.5rem;
    }
    
    .document_actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Animation for progress bar */
@keyframes progressAnimation {
    0% { width: 0%; }
    100% { width: var(--progress-width); }
}

.progress-bar.animated {
    animation: progressAnimation 1s ease-in-out;
}

/* Tooltip styles */
.document_tooltip {
    position: relative;
    cursor: help;
}

.document_tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

/* File type icons */
.file_type_icon {
    margin-right: 5px;
    font-size: 1.2em;
}

.file_type_icon.image {
    color: #28a745;
}

.file_type_icon.pdf {
    color: #dc3545;
}

.file_type_icon.document {
    color: #6c757d;
}
