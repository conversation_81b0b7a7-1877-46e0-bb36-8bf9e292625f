<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Travel Ticket Tree View -->
        <record id="view_travel_ticket_tree" model="ir.ui.view">
            <field name="name">travel.ticket.tree</field>
            <field name="model">travel.ticket</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="sequence" widget="handle"/>
                    <field name="passenger_id"/>
                    <field name="ticket_type" widget="badge"/>
                    <field name="trip_summary"/>
                    <field name="travel_date"/>
                    <field name="vendor_id"/>
                    <field name="vendor_cost"/>
                    <field name="customer_price"/>
                    <field name="margin"/>
                    <field name="ticket_reference"/>
                    <field name="status" widget="badge"
                           decoration-success="status=='ticketed'"
                           decoration-warning="status in ['quoted','booked']"
                           decoration-danger="status=='cancelled'"/>
                </tree>
            </field>
        </record>
        
        <!-- Travel Ticket Form View -->
        <record id="view_travel_ticket_form" model="ir.ui.view">
            <field name="name">travel.ticket.form</field>
            <field name="model">travel.ticket</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_set_quoted" string="Set Quoted" type="object" 
                                class="btn-primary" attrs="{'invisible': [('status', '!=', 'draft')]}"/>
                        <button name="action_set_booked" string="Set Booked" type="object" 
                                class="btn-primary" attrs="{'invisible': [('status', '!=', 'quoted')]}"/>
                        <button name="action_set_ticketed" string="Set Ticketed" type="object" 
                                class="btn-success" attrs="{'invisible': [('status', '!=', 'booked')]}"/>
                        <button name="action_cancel" string="Cancel" type="object" 
                                class="btn-secondary"/>
                        <field name="status" widget="statusbar" statusbar_visible="draft,quoted,booked,ticketed"/>
                    </header>
                    
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="route" placeholder="القاهرة → دبي"/>
                            </h1>
                            <div class="o_row">
                                <field name="passenger_id" readonly="1" class="oe_inline"/>
                                <span class="oe_grey"> • </span>
                                <field name="travel_date" class="oe_inline"/>
                            </div>
                        </div>
                        
                        <group>
                            <group name="ticket_info" string="Ticket Information">
                                <field name="sequence"/>
                                <field name="passenger_line_id" readonly="1" 
                                       attrs="{'invisible': [('passenger_line_id', '=', False)]}"/>
                                <field name="vendor_id"/>
                                <field name="ticket_reference"/>
                                <field name="days_until_travel"/>
                            </group>
                            <group name="pricing" string="Pricing">
                                <field name="vendor_cost"/>
                                <field name="customer_price"/>
                                <field name="margin" readonly="1"/>
                            </group>
                        </group>
                        
                        <group name="notes" string="Notes">
                            <field name="notes" nolabel="1"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Travel Ticket Search View -->
        <record id="view_travel_ticket_search" model="ir.ui.view">
            <field name="name">travel.ticket.search</field>
            <field name="model">travel.ticket</field>
            <field name="arch" type="xml">
                <search>
                    <field name="route"/>
                    <field name="passenger_id"/>
                    <field name="vendor_id"/>
                    <field name="ticket_reference"/>
                    
                    <filter string="Draft" name="draft" domain="[('status', '=', 'draft')]"/>
                    <filter string="Quoted" name="quoted" domain="[('status', '=', 'quoted')]"/>
                    <filter string="Booked" name="booked" domain="[('status', '=', 'booked')]"/>
                    <filter string="Ticketed" name="ticketed" domain="[('status', '=', 'ticketed')]"/>
                    <filter string="Cancelled" name="cancelled" domain="[('status', '=', 'cancelled')]"/>
                    
                    <separator/>
                    <filter string="Today's Travel" name="today" 
                            domain="[('travel_date', '=', context_today())]"/>
                    <filter string="This Week" name="week" 
                            domain="[('travel_date', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')), 
                                     ('travel_date', '&lt;', (context_today() + datetime.timedelta(days=7-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                    <filter string="This Month" name="month" 
                            domain="[('travel_date', '&gt;=', context_today().strftime('%Y-%m-01')), 
                                     ('travel_date', '&lt;', (context_today().replace(day=28) + datetime.timedelta(days=4)).replace(day=1).strftime('%Y-%m-%d'))]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Passenger" name="group_passenger" context="{'group_by': 'passenger_id'}"/>
                        <filter string="Vendor" name="group_vendor" context="{'group_by': 'vendor_id'}"/>
                        <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                        <filter string="Travel Date" name="group_travel_date" context="{'group_by': 'travel_date:day'}"/>
                        <filter string="Request" name="group_request" context="{'group_by': 'request_id'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Travel Ticket Action -->
        <record id="action_travel_ticket" model="ir.actions.act_window">
            <field name="name">Travel Tickets</field>
            <field name="res_model">travel.ticket</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_travel_ticket_search"/>
            <field name="context">{'search_default_draft': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a travel ticket
                </p>
                <p>
                    Travel tickets represent individual bookings for passengers.
                    Each ticket contains route information, vendor, pricing, and booking details.
                    This simplified model focuses on the essential information needed by the booking department.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
