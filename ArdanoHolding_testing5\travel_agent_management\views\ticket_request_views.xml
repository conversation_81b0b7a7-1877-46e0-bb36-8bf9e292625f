<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Travel Ticket Request Tree View -->
        <record id="view_travel_ticket_request_tree" model="ir.ui.view">
            <field name="name">travel.ticket.request.tree</field>
            <field name="model">travel.ticket.request</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="request_date"/>
                    <field name="customer_id"/>
                    <field name="passenger_count"/>
                    <field name="vendor_count" string="Vendors"/>
                    <field name="state" widget="badge" decoration-info="state=='draft'" decoration-warning="state in ['research','quotation']" decoration-primary="state in ['booking','delivered']" decoration-success="state in ['invoiced','done']" decoration-danger="state=='cancelled'"/>
                    <field name="assigned_to"/>
                    <field name="customer_price_total" sum="Total Customer Price"/>
                    <field name="vendor_cost_total" sum="Total Vendor Cost"/>
                    <field name="margin_total" sum="Total Margin"/>
                </tree>
            </field>
        </record>
        
        <!-- Travel Ticket Request Form View -->
        <record id="view_travel_ticket_request_form" model="ir.ui.view">
            <field name="name">travel.ticket.request.form</field>
            <field name="model">travel.ticket.request</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="action_start_research" string="Start Research" 
                                type="object" class="btn-primary" 
                                attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                        <button name="action_quotation_ready" string="Quotation Ready" 
                                type="object" class="btn-primary" 
                                attrs="{'invisible': [('state', '!=', 'research')]}"/>
                        <button name="action_confirm_booking" string="Confirm Booking"
                                type="object" class="btn-primary"
                                attrs="{'invisible': [('state', '!=', 'quotation')]}"
                                help="This will create purchase orders for all vendors automatically"/>
                        <button name="action_deliver_ticket" string="Deliver Ticket" 
                                type="object" class="btn-primary" 
                                attrs="{'invisible': [('state', '!=', 'booking')]}"/>
                        <button name="action_invoice_customer" string="Invoice Customer" 
                                type="object" class="btn-primary" 
                                attrs="{'invisible': [('state', '!=', 'delivered')]}"/>
                        <button name="action_complete" string="Complete" 
                                type="object" class="btn-success" 
                                attrs="{'invisible': [('state', '!=', 'invoiced')]}"/>
                        <button name="action_cancel" string="Cancel" 
                                type="object" class="btn-secondary" 
                                attrs="{'invisible': [('state', 'in', ['done', 'cancelled'])]}"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,research,quotation,booking,delivered,invoiced,done"/>
                    </header>
                    
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button type="object" name="action_view_sale_order" 
                                    class="oe_stat_button" icon="fa-usd"
                                    attrs="{'invisible': [('sale_order_id', '=', False)]}">
                                <field name="sale_order_id" widget="statinfo" string="Sales Order"/>
                            </button>
                            <button type="object" name="action_view_purchase_orders" 
                                    class="oe_stat_button" icon="fa-shopping-cart"
                                    attrs="{'invisible': [('purchase_order_count', '=', 0)]}">
                                <field name="purchase_order_count" widget="statinfo" string="Purchase Orders"/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                            <div class="o_row">
                                <field name="customer_id" options="{'no_create': True}" placeholder="Select Customer..." class="oe_inline" style="width: 50%"/>
                                <span class="oe_grey"> • </span>
                                <field name="passenger_count" readonly="1" class="oe_inline"/>
                                <span class="oe_grey"> passenger(s)</span>
                            </div>
                        </div>
                        
                        <group>
                            <group>
                                <field name="request_date" widget="date"/>
                                <field name="assigned_to" widget="many2one_avatar_user"/>
                            </group>
                            <group>
                            </group>
                        </group>
                        
                        <group>
                            <group string="Vendor Information">
                                <field name="selected_vendor_id" options="{'no_create': True}" placeholder="Primary Vendor (Legacy)..." readonly="1"/>
                                <field name="vendor_count" string="Vendors Used"/>
                                <field name="vendor_ids" widget="many2many_tags" readonly="1"/>
                                <field name="vendor_cost_total"/>
                                <field name="cost_currency_id"/>
                            </group>
                            <group string="Customer Pricing">
                                <field name="customer_price_total"/>
                                <field name="price_currency_id"/>
                                <field name="margin_total"/>
                                <field name="margin_percentage" string="Margin %" widget="percentage"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Passengers" name="passengers">
                                <field name="passenger_line_ids">
                                    <tree create="true" delete="true">
                                        <field name="sequence" widget="handle"/>
                                        <field name="passenger_id" options="{'no_quick_create': True, 'create': True, 'create_edit': True}" 
                                               domain="[]" 
                                               context="{'default_customer_id': parent.customer_id, 'search_default_customer_id': parent.customer_id}"
                                               required="1"/>
                                        <field name="passenger_passport_number" optional="show"/>
                                        <field name="passenger_nationality" optional="show"/>
                                        <field name="travel_purpose" required="1"/>
                                        <field name="departure_route" placeholder="e.g. New York (JFK) → London (LHR)" required="1"/>
                                        <field name="departure_date" required="1" widget="datetime"/>
                                        <field name="return_route" placeholder="e.g. London (LHR) → New York (JFK)" optional="show"/>
                                        <field name="return_date" optional="show" widget="datetime"/>
                                        <field name="ticket_count" string="Tickets"/>
                                        <field name="ticket_references" string="Ticket Numbers" readonly="1" optional="show"/>

                                        <field name="vendor_cost" sum="Total Vendor Cost"/>
                                        <field name="customer_price" sum="Total Customer Price"/>
                                        <field name="margin" sum="Total Margin" readonly="1"/>
                                        <field name="status" widget="badge" decoration-info="status=='draft'" decoration-warning="status=='booked'" decoration-success="status=='ticketed'" decoration-danger="status=='cancelled'"/>

                                    </tree>
                                    <form>
                                        <sheet>
                                            <group col="1">
                                                <group string="Passenger Information" col="2">
                                                    <field name="passenger_id" options="{'no_quick_create': True, 'create': True, 'create_edit': True}"
                                                           context="{'default_customer_id': parent.customer_id, 'search_default_customer_id': parent.customer_id}"/>
                                                    <field name="status"/>
                                                    <field name="passenger_passport_number"/>
                                                    <field name="passenger_nationality"/>
                                                    <field name="travel_purpose"/>
                                                </group>

                                                <group string="Preferences" col="2">
                                                    <field name="seat_preference"/>
                                                    <field name="meal_preference"/>
                                                    <field name="special_requirements" colspan="2"/>
                                                </group>

                                                <group string="Travel Tickets" col="1">
                                                    <p class="text-muted">
                                                        إضافة تذاكر السفر لهذا المسافر. كل تذكرة تمثل حجز منفصل مع مورد.
                                                        مثال: تذكرة 1 (القاهرة→دبي) وتذكرة 2 (دبي→واشنطن) لرحلة من القاهرة إلى واشنطن.
                                                    </p>
                                                    <field name="ticket_ids" context="{'default_passenger_line_id': active_id}" nolabel="1">
                                                    <tree editable="bottom" create="true" delete="true">
                                                        <field name="sequence" widget="handle"/>
                                                        <field name="ticket_type" decoration-info="ticket_type=='oneway'" decoration-success="ticket_type=='roundtrip'" decoration-warning="ticket_type=='multicity'"/>

                                                        <field name="route" invisible="1"/>
                                                        <field name="return_route" invisible="1"/>
                                                        <field name="trip_summary" readonly="1"/>
                                                        <field name="departure_city" placeholder="بنغازي"/>
                                                        <field name="arrival_city" placeholder="طرابلس"/>
                                                        <field name="travel_date"/>
                                                        <field name="return_departure_city" placeholder="طرابلس" attrs="{'invisible': [('ticket_type', '!=', 'roundtrip')], 'required': [('ticket_type', '=', 'roundtrip')]}"/>
                                                        <field name="return_arrival_city" placeholder="بنغازي" attrs="{'invisible': [('ticket_type', '!=', 'roundtrip')], 'required': [('ticket_type', '=', 'roundtrip')]}"/>
                                                        <field name="return_travel_date" attrs="{'invisible': [('ticket_type', '!=', 'roundtrip')], 'required': [('ticket_type', '=', 'roundtrip')]}"/>
                                                        <field name="vendor_id"/>
                                                        <field name="vendor_cost"/>
                                                        <field name="customer_price"/>
                                                        <field name="margin" readonly="1"/>
                                                        <field name="ticket_reference" placeholder="رقم التذكرة"/>
                                                        <field name="status" widget="badge" decoration-info="status=='draft'" decoration-warning="status=='quoted'" decoration-primary="status=='booked'" decoration-success="status=='ticketed'" decoration-danger="status=='cancelled'"/>
                                                        <!-- Smart single button that shows next action -->
                                                        <button name="action_set_quoted" type="object" string="→ Quote" title="Set as Quoted" class="btn-sm btn-warning" icon="fa-quote-left" attrs="{'invisible': [('status', '!=', 'draft')]}"/>
                                                        <button name="action_set_booked" type="object" string="→ Book" title="Set as Booked" class="btn-sm btn-primary" icon="fa-calendar-check-o" attrs="{'invisible': [('status', '!=', 'quoted')]}"/>
                                                        <button name="action_set_ticketed" type="object" string="→ Ticket" title="Set as Ticketed" class="btn-sm btn-success" icon="fa-plane" attrs="{'invisible': [('status', '!=', 'booked')]}"/>

                                                        <!-- Cancel button only for non-cancelled tickets -->
                                                        <button name="action_cancel" type="object" string="✕" title="Cancel Ticket" class="btn-sm btn-outline-danger" icon="fa-times" attrs="{'invisible': [('status', '=', 'cancelled')]}"/>
                                                    </tree>

                                                    <!-- Detailed Form View for Individual Tickets -->
                                                    <form>
                                                        <header>
                                                            <field name="status" widget="statusbar" statusbar_visible="draft,quoted,booked,ticketed"/>
                                                            <button name="action_set_quoted" type="object" string="Set as Quoted" class="btn-warning" attrs="{'invisible': [('status', '!=', 'draft')]}"/>
                                                            <button name="action_set_booked" type="object" string="Set as Booked" class="btn-primary" attrs="{'invisible': [('status', 'not in', ['draft', 'quoted'])]}"/>
                                                            <button name="action_set_ticketed" type="object" string="Set as Ticketed" class="btn-success" attrs="{'invisible': [('status', '!=', 'booked')]}"/>
                                                            <button name="action_cancel" type="object" string="Cancel Ticket" class="btn-danger" attrs="{'invisible': [('status', '=', 'cancelled')]}"/>
                                                        </header>

                                                        <sheet>
                                                            <div class="oe_title">
                                                                <h1>
                                                                    <field name="trip_summary" readonly="1"/>
                                                                </h1>
                                                                <div class="o_row">
                                                                    <field name="ticket_type"/>
                                                                </div>
                                                            </div>

                                                            <group>
                                                                <group name="outbound_info" string="Outbound Journey">
                                                                    <field name="departure_city" placeholder="e.g., Benghazi"/>
                                                                    <field name="arrival_city" placeholder="e.g., Tripoli"/>
                                                                    <field name="route" readonly="1"/>
                                                                    <field name="travel_date"/>
                                                                </group>
                                                                <group name="return_info" string="Return Journey" attrs="{'invisible': [('ticket_type', '!=', 'roundtrip')]}">
                                                                    <field name="return_departure_city" placeholder="e.g., Tripoli"/>
                                                                    <field name="return_arrival_city" placeholder="e.g., Benghazi"/>
                                                                    <field name="return_route" readonly="1"/>
                                                                    <field name="return_travel_date"/>
                                                                </group>
                                                            </group>

                                                            <group>
                                                                <group name="flight_details" string="Flight Details">
                                                                    <field name="departure_date"/>
                                                                    <field name="flight_number"/>
                                                                    <field name="airline"/>
                                                                    <field name="class_type"/>
                                                                </group>
                                                                <group name="return_flight_details" string="Return Flight Details" attrs="{'invisible': [('ticket_type', '!=', 'roundtrip')]}">
                                                                    <field name="return_departure_date"/>
                                                                    <field name="return_flight_number"/>
                                                                    <field name="return_airline"/>
                                                                </group>
                                                            </group>

                                                            <group>
                                                                <group name="booking_info" string="Booking Information">
                                                                    <field name="vendor_id"/>
                                                                    <field name="ticket_reference"/>
                                                                    <field name="sequence"/>
                                                                    <field name="days_until_travel"/>
                                                                </group>
                                                                <group name="pricing" string="Pricing">
                                                                    <field name="vendor_cost"/>
                                                                    <field name="customer_price"/>
                                                                    <field name="margin" readonly="1"/>
                                                                </group>
                                                            </group>

                                                            <group name="notes" string="Notes">
                                                                <field name="notes" nolabel="1"/>
                                                            </group>
                                                        </sheet>
                                                    </form>
                                                    </field>
                                                </group>
                                            </group>
                                        </sheet>
                                    </form>

                                </field>
                            </page>
                            <page string="Additional Services" name="services">
                                <group>
                                    <field name="additional_services" placeholder="Hotel bookings, transportation, insurance, special assistance, etc."/>
                                </group>
                            </page>
                            <page string="Internal Notes" name="internal_notes">
                                <field name="internal_notes" placeholder="Internal team notes and processing information"/>
                            </page>
                            <page string="Customer Notes" name="customer_notes">
                                <field name="customer_notes" placeholder="Notes from customer communication"/>
                            </page>
                            <page string="Process Guide" name="process_guide">
                                <div class="alert alert-info" role="alert">
                                    <h4><i class="fa fa-info-circle"/> دليل العملية</h4>

                                    <h5 class="mt-3">📋 تغيير حالة التذاكر:</h5>
                                    <ul>
                                        <li><strong>Draft → Quoted:</strong> بعد الحصول على عروض أسعار من الموردين</li>
                                        <li><strong>Quoted → Booked:</strong> بعد تأكيد الحجز مع المورد</li>
                                        <li><strong>Booked → Ticketed:</strong> بعد استلام التذكرة من المورد</li>
                                        <li><strong>Cancel:</strong> في أي مرحلة عند الحاجة للإلغاء</li>
                                    </ul>

                                    <h5 class="mt-3">💰 إنشاء فواتير الموردين:</h5>
                                    <ul>
                                        <li><strong>متى:</strong> عند الضغط على "Confirm Booking" (حالة Quotation → Booking)</li>
                                        <li><strong>كيف:</strong> يتم إنشاء فاتورة منفصلة لكل مورد تلقائياً</li>
                                        <li><strong>المحتوى:</strong> كل تذكرة تصبح سطر منفصل في الفاتورة</li>
                                        <li><strong>العرض:</strong> استخدم زر "Purchase Orders" في الأعلى لعرض الفواتير</li>
                                    </ul>

                                    <h5 class="mt-3">🔄 سير العمل:</h5>
                                    <ol>
                                        <li><strong>Draft:</strong> طلب جديد</li>
                                        <li><strong>Research:</strong> البحث عن موردين وأسعار</li>
                                        <li><strong>Quotation:</strong> الأسعار جاهزة للعميل</li>
                                        <li><strong>Booking:</strong> تأكيد الحجز + إنشاء فواتير الموردين</li>
                                        <li><strong>Delivered:</strong> تسليم التذاكر للعميل</li>
                                        <li><strong>Invoiced:</strong> إرسال فاتورة للعميل</li>
                                        <li><strong>Done:</strong> اكتمال العملية</li>
                                    </ol>
                                </div>
                            </page>
                        </notebook>
                        
                        <div class="oe_chatter">
                            <field name="message_follower_ids"/>
                            <field name="activity_ids"/>
                            <field name="message_ids"/>
                        </div>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Travel Ticket Request Search View -->
        <record id="view_travel_ticket_request_search" model="ir.ui.view">
            <field name="name">travel.ticket.request.search</field>
            <field name="model">travel.ticket.request</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="Request Number"/>
                    <field name="customer_id" string="Customer"/>
                    <field name="assigned_to" string="Assigned To"/>
                    
                    <filter string="My Requests" name="my_requests" domain="[('assigned_to', '=', uid)]"/>
                    <filter string="Unassigned" name="unassigned" domain="[('assigned_to', '=', False)]"/>
                    <separator/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="In Research" name="research" domain="[('state', '=', 'research')]"/>
                    <filter string="Quotation Ready" name="quotation" domain="[('state', '=', 'quotation')]"/>
                    <filter string="Booking Confirmed" name="booking" domain="[('state', '=', 'booking')]"/>
                    <filter string="Delivered" name="delivered" domain="[('state', '=', 'delivered')]"/>
                    <filter string="Invoiced" name="invoiced" domain="[('state', '=', 'invoiced')]"/>
                    <filter string="Completed" name="done" domain="[('state', '=', 'done')]"/>
                    <separator/>



                    
                    <group expand="0" string="Group By">
                        <filter string="Customer" name="group_customer" context="{'group_by': 'customer_id'}"/>
                        <filter string="Assigned To" name="group_assigned" context="{'group_by': 'assigned_to'}"/>
                        <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Request Date" name="group_request_date" context="{'group_by': 'request_date:month'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Travel Ticket Request Action -->
        <record id="action_travel_ticket_request" model="ir.actions.act_window">
            <field name="name">Travel Requests</field>
            <field name="res_model">travel.ticket.request</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_travel_ticket_request_search"/>
            <field name="context">{'search_default_my_requests': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first travel request!
                </p>
                <p>
                    Manage travel ticket requests from customers. Track the entire process from initial request 
                    to ticket delivery and invoicing.
                </p>
            </field>
        </record>
        
    </data>
</odoo> 