<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Visa Country Tree View -->
        <record id="view_visa_country_tree" model="ir.ui.view">
            <field name="name">visa.country.tree</field>
            <field name="model">visa.country</field>
            <field name="arch" type="xml">
                <tree string="Visa Countries">
                    <field name="flag_emoji"/>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="visa_type_count"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Visa Country Form View -->
        <record id="view_visa_country_form" model="ir.ui.view">
            <field name="name">visa.country.form</field>
            <field name="model">visa.country</field>
            <field name="arch" type="xml">
                <form string="Visa Country">
                    <header>
                        <button name="action_view_visa_types" type="object" 
                                string="View Visa Types" class="btn-primary"
                                attrs="{'invisible': [('visa_type_count', '=', 0)]}"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_visa_types" type="object" 
                                    class="oe_stat_button" icon="fa-list">
                                <field name="visa_type_count" widget="statinfo" 
                                       string="Visa Types"/>
                            </button>
                        </div>
                        
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                                attrs="{'invisible': [('active', '=', True)]}"/>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="flag_emoji" class="me-2"/>
                                <field name="name" placeholder="Country Name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="code"/>
                                <field name="active"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Visa Types" name="visa_types">
                                <field name="visa_type_ids" context="{'default_country_id': active_id}">
                                    <tree>
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="price" widget="monetary"/>
                                        <field name="currency_id" invisible="1"/>
                                        <field name="processing_days"/>
                                        <field name="request_count"/>
                                        <field name="active"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Visa Country Kanban View -->
        <record id="view_visa_country_kanban" model="ir.ui.view">
            <field name="name">visa.country.kanban</field>
            <field name="model">visa.country</field>
            <field name="arch" type="xml">
                <kanban>
                    <field name="id"/>
                    <field name="name"/>
                    <field name="flag_emoji"/>
                    <field name="visa_type_count"/>
                    <field name="active"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_image">
                                    <span class="fa fa-globe fa-2x" style="font-size: 3em;" 
                                          t-if="!record.flag_emoji.raw_value"/>
                                    <span style="font-size: 3em;" t-if="record.flag_emoji.raw_value" 
                                          t-esc="record.flag_emoji.value"/>
                                </div>
                                <div class="oe_kanban_details">
                                    <strong class="o_kanban_record_title">
                                        <field name="name"/>
                                    </strong>
                                    <div class="o_kanban_record_body">
                                        <field name="visa_type_count"/> Visa Types
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span class="badge badge-pill badge-info" 
                                              t-if="record.active.raw_value">Active</span>
                                        <span class="badge badge-pill badge-secondary" 
                                              t-if="!record.active.raw_value">Archived</span>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Visa Country Search View -->
        <record id="view_visa_country_search" model="ir.ui.view">
            <field name="name">visa.country.search</field>
            <field name="model">visa.country</field>
            <field name="arch" type="xml">
                <search string="Search Visa Countries">
                    <field name="name"/>
                    <field name="code"/>
                    <separator/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="Has Visa Types" name="has_visa_types"
                            domain="[('visa_type_count', '>', 0)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Status" name="group_active" context="{'group_by': 'active'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Visa Country Action -->
        <record id="action_visa_country" model="ir.actions.act_window">
            <field name="name">Visa Countries</field>
            <field name="res_model">visa.country</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="search_view_id" ref="view_visa_country_search"/>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first visa country!
                </p>
                <p>
                    Add countries that offer visa services to start managing visa types and requests.
                </p>
            </field>
        </record>

    </data>
</odoo>
