<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Fix for language selector when languages is None -->
        <template id="language_selector_fix" name="Language Selector Fix" inherit_id="portal.language_selector" priority="1">
            <xpath expr="//t[@t-set='active_lang']" position="before">
                <t t-if="not languages">
                    <t t-set="languages" t-value="request.env['res.lang'].get_available()"/>
                </t>
            </xpath>
            <xpath expr="//t[@t-set='language_selector_visible']" position="replace">
                <t t-set="language_selector_visible" t-value="languages and len(languages) > 1"/>
            </xpath>
        </template>

        <!-- Add Travel Management to Portal Home -->
        <template id="portal_my_home_travel_management" name="Portal My Home: Travel Management" customize_show="True" inherit_id="portal.portal_my_home" priority="40">
            <xpath expr="//div[hasclass('o_portal_docs')]" position="inside">
                <t t-call="portal.portal_docs_entry">
                    <t t-set="title">Travel Requests</t>
                    <t t-set="url" t-value="'/my/travel_requests'"/>
                    <t t-set="placeholder_count" t-value="'travel_request_count'"/>
                </t>
                <t t-call="portal.portal_docs_entry">
                    <t t-set="title">My Passengers</t>
                    <t t-set="url" t-value="'/my/passengers'"/>
                    <t t-set="placeholder_count" t-value="'passenger_count'"/>
                </t>
                <t t-call="portal.portal_docs_entry">
                    <t t-set="title">Visa Requests</t>
                    <t t-set="url" t-value="'/my/visa_requests'"/>
                    <t t-set="placeholder_count" t-value="'visa_request_count'"/>
                </t>
                <t t-call="portal.portal_docs_entry">
                    <t t-set="title">Transfer Requests</t>
                    <t t-set="url" t-value="'/my/transfer_request'"/>
                    <t t-set="placeholder_count" t-value="'transfer_request_count'"/>
                </t>
                <t t-call="portal.portal_docs_entry">
                    <t t-set="title">Hotel Bookings</t>
                    <t t-set="url" t-value="'/my/hotel_booking'"/>
                    <t t-set="placeholder_count" t-value="'hotel_booking_count'"/>
                </t>
                <t t-call="portal.portal_docs_entry">
                    <t t-set="title">Medical Insurance</t>
                    <t t-set="url" t-value="'/my/medical_insurance'"/>
                    <t t-set="placeholder_count" t-value="'medical_insurance_count'"/>
                </t>
            </xpath>
        </template>

        <!-- Travel Portal Enhancement -->
        <template id="portal_my_home_travel_enhancement" name="Portal Travel Enhancement" inherit_id="portal.portal_my_home" priority="100">
            <xpath expr="//div[hasclass('o_portal_docs')]" position="after">
                <!-- CSS to force show travel entries -->
                <style>
                    /* Force show travel entries even if hidden by default */
                    a[href*="/my/travel_requests"],
                    a[href*="/my/passengers"],
                    a[href*="/my/visa_requests"],
                    a[href*="/my/transfer_request"],
                    a[href*="/my/hotel_booking"],
                    a[href*="/my/medical_insurance"] {
                        display: flex !important;
                    }


                </style>

                <!-- Simple JavaScript to show travel entries -->
                <script type="text/javascript">
                    //<![CDATA[
                    window.addEventListener('load', function() {
                        setTimeout(function() {
                            const travelEntries = document.querySelectorAll('a[href*="/my/travel_requests"], a[href*="/my/passengers"], a[href*="/my/visa_requests"], a[href*="/my/transfer_request"], a[href*="/my/hotel_booking"], a[href*="/my/medical_insurance"]');
                            travelEntries.forEach(function(entry) {
                                entry.classList.remove('d-none');
                            });
                        }, 100);
                    });
                    //]]>
                </script>
            </xpath>
        </template>



        <!-- Travel Requests List View -->
        <template id="portal_my_travel_requests" name="My Travel Requests">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                
                <t t-call="portal.portal_searchbar">
                    <t t-set="title">Travel Requests</t>
                </t>

                <div class="o_portal_docs row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Travel Requests</h3>
                            <a href="/my/travel_request/new" class="btn btn-primary">
                                <i class="fa fa-plus"/> New Request
                            </a>
                        </div>
                        
                        <t t-if="not travel_requests">
                            <div class="alert alert-info text-center">
                                <strong>You don't have any travel requests yet.</strong><br/>
                                <a href="/my/travel_request/new" class="btn btn-primary mt-2">
                                    Create your first travel request
                                </a>
                            </div>
                        </t>
                        
                        <t t-if="travel_requests" t-call="portal.portal_table">
                            <thead>
                                <tr class="active">
                                    <th>Reference</th>
                                    <th>Request Date</th>
                                    <th>Passengers</th>
                                    <th>Status</th>
                                    <th class="text-right">Total Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-foreach="travel_requests" t-as="travel_request">
                                    <tr>
                                        <td>
                                            <a t-attf-href="/my/travel_request/#{travel_request.id}">
                                                <t t-esc="travel_request.name"/>
                                            </a>
                                        </td>
                                        <td>
                                            <span t-field="travel_request.request_date"/>
                                        </td>
                                        <td>
                                            <span t-esc="travel_request.passenger_count"/>
                                        </td>
                                        <td>
                                            <span class="badge badge-pill badge-info" t-esc="travel_request.state"/>
                                        </td>
                                        <td class="text-right">
                                            <span t-field="travel_request.customer_price_total" 
                                                  t-options="{'widget': 'monetary', 'display_currency': travel_request.price_currency_id}"/>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </t>
                    </div>
                </div>
            </t>
        </template>

        <!-- Travel Request Detail View -->
        <template id="portal_my_travel_request" name="My Travel Request">
            <t t-call="portal.portal_layout">
                <t t-set="title" t-value="travel_request.name"/>
                
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-10">
                            
                            <div class="status_card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h4 class="mb-0">
                                        <span t-field="travel_request.name"/>
                                    </h4>
                                    <span class="badge badge-pill badge-info" t-esc="travel_request.state"/>
                                </div>
                                
                                <div class="card-body">
                                    <!-- Request Information -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <strong>Request Date:</strong>
                                            <span t-field="travel_request.request_date"/>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Assigned To:</strong>
                                            <span t-field="travel_request.assigned_to.name" t-if="travel_request.assigned_to"/>
                                            <span t-else="">Not assigned yet</span>
                                        </div>
                                    </div>

                                    <!-- Passengers Information -->
                                    <h5>Passengers</h5>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Passenger</th>
                                                    <th>Purpose</th>
                                                    <th>Route</th>
                                                    <th>Departure</th>
                                                    <th>Return</th>
                                                    <th>Class</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <t t-foreach="travel_request.passenger_line_ids" t-as="line">
                                                    <tr>
                                                        <td><span t-field="line.passenger_id.name"/></td>
                                                        <td><span t-field="line.travel_purpose"/></td>
                                                        <td><span t-field="line.departure_route"/></td>
                                                        <td><span t-field="line.departure_date"/></td>
                                                        <td><span t-field="line.return_date"/></td>
                                                        <td><span t-field="line.ticket_class"/></td>
                                                    </tr>
                                                </t>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Additional Services -->
                                    <t t-if="travel_request.additional_services">
                                        <h5>Additional Services</h5>
                                        <p t-field="travel_request.additional_services"/>
                                    </t>

                                    <!-- Customer Notes -->
                                    <t t-if="travel_request.customer_notes">
                                        <h5>Customer Notes</h5>
                                        <p t-field="travel_request.customer_notes"/>
                                    </t>

                                    <!-- Pricing Information -->
                                    <t t-if="travel_request.customer_price_total">
                                        <h5>Pricing</h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>Total Price:</strong>
                                                <span t-field="travel_request.customer_price_total" 
                                                      t-options="{'widget': 'monetary', 'display_currency': travel_request.price_currency_id}"/>
                                            </div>
                                        </div>
                                    </t>
                                </div>
                            </div>

                            <!-- Message Area -->
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5>Communication</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Add message form -->
                                    <form t-attf-action="/my/travel_request/#{travel_request.id}/message" method="post" class="mb-3">
                                        <input name="csrf_token" t-att-value="request.csrf_token()" type="hidden"/>
                                        <div class="form-group">
                                            <label for="message">Send a message:</label>
                                            <textarea name="message" class="form-control" rows="3" placeholder="Type your message here..."></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Send Message</button>
                                    </form>
                                    
                                    <!-- Messages thread -->
                                    <t t-call="portal.message_thread">
                                        <t t-set="object" t-value="travel_request"/>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- New Travel Request Form -->
        <template id="portal_travel_request_new" name="New Travel Request">
            <t t-call="portal.portal_layout">
                <t t-set="title">New Travel Request</t>
                
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-10">
                            <h3>Create New Travel Request</h3>
                            
                            <form action="/my/travel_request/create" method="post" class="card">
                                <input name="csrf_token" t-att-value="request.csrf_token()" type="hidden"/>
                                
                                <div class="card-header">
                                    <h5>Travel Request Details</h5>
                                </div>
                                
                                <div class="card-body">
                                    <!-- Basic Information -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="customer_notes">Request Description</label>
                                                <textarea name="customer_notes" class="form-control" rows="3" 
                                                         placeholder="Describe your travel requirements..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="additional_services">Additional Services</label>
                                                <textarea name="additional_services" class="form-control" rows="3" 
                                                         placeholder="Hotel, transportation, insurance, etc."></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Passengers Section -->
                                    <h5>Passengers</h5>
                                    <div class="alert alert-info">
                                        <strong>ملاحظة:</strong> يمكنك ترك حقول الرحلة فارغة وسيتم ملؤها لاحقاً من قبل فريق الحجز.
                                    </div>
                                    <div id="passengers_section">
                                        <div class="passenger_form border p-3 mb-3" data-passenger="0">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>Passenger *</label>
                                                        <select name="passenger_id_0" class="form-control" required="required">
                                                            <option value="">Select Passenger</option>
                                                            <t t-foreach="passengers" t-as="passenger">
                                                                <option t-att-value="passenger.id">
                                                                    <t t-esc="passenger.name"/>
                                                                </option>
                                                            </t>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>Travel Purpose *</label>
                                                        <select name="travel_purpose_0" class="form-control" required="required">
                                                            <option value="business">Business</option>
                                                            <option value="personal">Personal</option>
                                                            <option value="emergency">Emergency</option>
                                                            <option value="medical">Medical</option>
                                                            <option value="training">Training</option>
                                                            <option value="conference">Conference</option>
                                                            <option value="other">Other</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>Departure Route</label>
                                                        <input type="text" name="departure_route_0" class="form-control"
                                                               placeholder="From - To (optional)"/>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>Departure Date</label>
                                                        <input type="date" name="departure_date_0" class="form-control"/>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>Return Route</label>
                                                        <input type="text" name="return_route_0" class="form-control" 
                                                               placeholder="From - To (optional)"/>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>Return Date</label>
                                                        <input type="date" name="return_date_0" class="form-control"/>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>Ticket Class</label>
                                                        <select name="ticket_class_0" class="form-control">
                                                            <option value="economy">Economy</option>
                                                            <option value="premium_economy">Premium Economy</option>
                                                            <option value="business">Business</option>
                                                            <option value="first">First Class</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>Seat Preference</label>
                                                        <select name="seat_preference_0" class="form-control">
                                                            <option value="no_preference">No Preference</option>
                                                            <option value="aisle">Aisle</option>
                                                            <option value="window">Window</option>
                                                            <option value="middle">Middle</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Meal Preference</label>
                                                        <select name="meal_preference_0" class="form-control">
                                                            <option value="regular">Regular</option>
                                                            <option value="vegetarian">Vegetarian</option>
                                                            <option value="vegan">Vegan</option>
                                                            <option value="halal">Halal</option>
                                                            <option value="kosher">Kosher</option>
                                                            <option value="gluten_free">Gluten Free</option>
                                                            <option value="diabetic">Diabetic</option>
                                                            <option value="no_meal">No Meal</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Special Requirements</label>
                                                        <textarea name="special_requirements_0" class="form-control" rows="2" 
                                                                 placeholder="Wheelchair assistance, medical needs, etc."></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <input type="hidden" name="passenger_count" id="passenger_count" value="1"/>
                                    
                                    <div class="text-center mb-3">
                                        <button type="button" class="btn btn-secondary" onclick="addPassenger()">
                                            <i class="fa fa-plus"/> Add Another Passenger
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="card-footer text-right">
                                    <a href="/my/travel_requests" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Submit Request</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <script>
                    let passengerCounter = 1;
                    
                    function addPassenger() {
                        const passengersSection = document.getElementById('passengers_section');
                        const firstPassenger = document.querySelector('.passenger_form[data-passenger="0"]');
                        const newPassenger = firstPassenger.cloneNode(true);
                        
                        // Update passenger index
                        newPassenger.setAttribute('data-passenger', passengerCounter);
                        
                        // Update all input names and clear values
                        const inputs = newPassenger.querySelectorAll('input, select, textarea');
                        inputs.forEach(input => {
                            const name = input.getAttribute('name');
                            if (name) {
                                input.setAttribute('name', name.replace('_0', '_' + passengerCounter));
                                if (input.type !== 'hidden') {
                                    input.value = '';
                                }
                            }
                        });
                        
                        // Add remove button
                        const removeBtn = document.createElement('button');
                        removeBtn.type = 'button';
                        removeBtn.className = 'btn btn-danger btn-sm float-right';
                        removeBtn.innerHTML = '&lt;i class="fa fa-trash"/&gt; Remove';
                        removeBtn.onclick = function() { 
                            newPassenger.remove(); 
                            updatePassengerCount();
                        };
                        
                        newPassenger.querySelector('.row').prepend(removeBtn);
                        passengersSection.appendChild(newPassenger);
                        
                        passengerCounter++;
                        updatePassengerCount();
                    }
                    
                    function updatePassengerCount() {
                        const count = document.querySelectorAll('.passenger_form').length;
                        document.getElementById('passenger_count').value = count;
                    }
                </script>
            </t>
        </template>

        <!-- Portal Passenger Management -->
        <template id="portal_my_passengers" name="My Passengers">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>
                
                <!-- Include CSS -->
                <link rel="stylesheet" type="text/css" href="/travel_agent_management/static/src/css/portal_passengers.css"/>
                
                <!-- Debug CSS for dropdowns -->
                <style>
                    .dropdown {
                        position: relative !important;
                    }
                    .dropdown-menu {
                        position: absolute !important;
                        top: 100% !important;
                        left: 0 !important;
                        z-index: 9999 !important;
                        background: white !important;
                        border: 2px solid red !important;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.5) !important;
                        min-width: 200px !important;
                        display: none !important;
                    }
                    .dropdown-menu.show,
                    .dropdown.show .dropdown-menu {
                        display: block !important;
                    }
                    .dropdown-toggle {
                        cursor: pointer !important;
                        border: 1px solid blue !important;
                    }
                </style>
                
                <div class="o_portal_docs row">
                    <div class="col-12">
                        <!-- Header with title, stats and add button -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h3 class="mb-1">My Passengers</h3>
                                <small class="text-muted">
                                    <span t-esc="passenger_count"/> passenger<t t-if="passenger_count != 1">s</t>
                                    <t t-if="filterby != 'all'"> • <span t-esc="searchbar_filters[filterby]['label']"/></t>
                                </small>
                            </div>
                            <a href="/my/passenger/new" class="btn btn-primary">
                                <i class="fa fa-plus"/> Add Passenger
                            </a>
                        </div>
                        
                        <!-- Controls Bar -->
                        <div class="card border-0 shadow-sm mb-4 controls-bar">
                            <div class="card-body py-3">
                                <div class="row align-items-center">
                                    <!-- Search -->
                                    <div class="col-md-4 mb-2 mb-md-0">
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="search" 
                                                   t-att-value="search or ''" placeholder="Search passengers..." 
                                                   id="passenger_search"/>
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" 
                                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <span t-esc="searchbar_inputs[search_in or 'content']['label']"/>
                                                </button>
                                                <div class="dropdown-menu dropdown-menu-right">
                                                    <h6 class="dropdown-header">Search In:</h6>
                                                    <t t-foreach="searchbar_inputs" t-as="option">
                                                        <a t-attf-href="/my/passengers?{{ keep_query('search', 'filterby', 'sortby', 'group_by', 'view_type', search_in=option) }}" 
                                                           t-attf-class="dropdown-item #{'active' if option == (search_in or 'content') else ''}">
                                                           <t t-if="option == (search_in or 'content')"><i class="fa fa-check text-success mr-2"/></t>
                                                           <span t-esc="searchbar_inputs[option]['label']"/>
                                                        </a>
                                                    </t>
                                                    <div class="dropdown-divider"></div>
                                                    <a href="#" class="dropdown-item clear-search text-muted">
                                                        <i class="fa fa-times mr-2"></i>Clear Search
                                                    </a>
                                                </div>
                                            </div>
                                            <t t-if="search">
                                                <div class="input-group-append">
                                                    <a t-attf-href="/my/passengers?{{ keep_query('filterby', 'sortby', 'group_by', 'view_type', 'search_in') }}" 
                                                       class="btn btn-outline-secondary" title="Clear search">
                                                        <i class="fa fa-times"></i>
                                                    </a>
                                                </div>
                                            </t>
                                        </div>
                                    </div>
                                    
                                    <!-- Filter -->
                                    <div class="col-md-2 mb-2 mb-md-0">
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" 
                                                    data-toggle="dropdown">
                                                <i class="fa fa-filter"/> <span t-esc="searchbar_filters[filterby]['label']"/>
                                            </button>
                                            <div class="dropdown-menu">
                                                <t t-foreach="searchbar_filters" t-as="option">
                                                    <a t-attf-href="/my/passengers?{{ keep_query('search', 'search_in', 'sortby', 'group_by', 'view_type', filterby=option) }}" 
                                                       class="dropdown-item">
                                                       <t t-if="option == filterby"><i class="fa fa-check text-success mr-2"/></t>
                                                       <span t-esc="searchbar_filters[option]['label']"/>
                                                    </a>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Sort -->
                                    <div class="col-md-2 mb-2 mb-md-0">
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" 
                                                    data-toggle="dropdown">
                                                <i class="fa fa-sort"/> <span t-esc="searchbar_sortings[sortby]['label']"/>
                                            </button>
                                            <div class="dropdown-menu">
                                                <t t-foreach="searchbar_sortings" t-as="option">
                                                    <a t-attf-href="/my/passengers?{{ keep_query('search', 'search_in', 'filterby', 'group_by', 'view_type', sortby=option) }}" 
                                                       class="dropdown-item">
                                                       <t t-if="option == sortby"><i class="fa fa-check text-success mr-2"/></t>
                                                       <span t-esc="searchbar_sortings[option]['label']"/>
                                                    </a>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Group By -->
                                    <div class="col-md-2 mb-2 mb-md-0">
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" 
                                                    data-toggle="dropdown">
                                                <i class="fa fa-object-group"/> <span t-esc="searchbar_groupby[group_by]['label']"/>
                                            </button>
                                            <div class="dropdown-menu">
                                                <t t-foreach="searchbar_groupby" t-as="option">
                                                    <a t-attf-href="/my/passengers?{{ keep_query('search', 'search_in', 'filterby', 'sortby', 'view_type', group_by=option) }}" 
                                                       class="dropdown-item">
                                                       <t t-if="option == group_by"><i class="fa fa-check text-success mr-2"/></t>
                                                       <span t-esc="searchbar_groupby[option]['label']"/>
                                                    </a>
                                                </t>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- View Toggle -->
                                    <div class="col-md-2">
                                        <div class="btn-group w-100" role="group">
                                            <a t-attf-href="/my/passengers?{{ keep_query('search', 'search_in', 'filterby', 'sortby', 'group_by', view_type='grid') }}" 
                                               t-attf-class="btn btn-sm #{'btn-primary' if view_type == 'grid' else 'btn-outline-secondary'}"
                                               title="Grid View">
                                                <i class="fa fa-th"/>
                                            </a>
                                            <a t-attf-href="/my/passengers?{{ keep_query('search', 'search_in', 'filterby', 'sortby', 'group_by', view_type='list') }}" 
                                               t-attf-class="btn btn-sm #{'btn-primary' if view_type == 'list' else 'btn-outline-secondary'}"
                                               title="List View">
                                                <i class="fa fa-list"/>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Empty State -->
                        <t t-if="not passengers">
                            <div class="alert alert-info text-center">
                                <i class="fa fa-users fa-3x text-muted mb-3"></i>
                                <h5>No passengers found</h5>
                                <p class="mb-3">
                                    <t t-if="search">
                                        No passengers match your search criteria.
                                        <br/>
                                        <a t-attf-href="/my/passengers" class="btn btn-sm btn-outline-primary mt-2">
                                            Clear search
                                        </a>
                                    </t>
                                    <t t-else="">
                                        You don't have any passengers registered yet.
                                    </t>
                                </p>
                                <a href="/my/passenger/new" class="btn btn-primary">
                                    <i class="fa fa-plus"/> Add Passenger
                                </a>
                            </div>
                        </t>
                        
                        <!-- Content -->
                        <t t-if="passengers">
                            
                            <!-- Grouped View -->
                            <t t-if="group_by != 'none' and grouped_passengers">
                                <t t-foreach="grouped_passengers" t-as="group">
                                    <div class="mb-4">
                                        <div class="group-header">
                                            <h5>
                                                <i class="fa fa-folder-open mr-2"></i>
                                                <span t-esc="group['name']"/>
                                                <small>(<span t-esc="group['count']"/> passenger<t t-if="group['count'] != 1">s</t>)</small>
                                            </h5>
                                        </div>
                                        
                                        <!-- Grid View -->
                                        <t t-if="view_type == 'grid'">
                                            <div class="row">
                                                <t t-foreach="group['passengers']" t-as="passenger">
                                                    <t t-call="travel_agent_management.passenger_card_template"/>
                                                </t>
                                            </div>
                                        </t>
                                        
                                        <!-- List View -->
                                        <t t-if="view_type == 'list'">
                                            <t t-call="travel_agent_management.passenger_list_template">
                                                <t t-set="passenger_list" t-value="group['passengers']"/>
                                            </t>
                                        </t>
                                    </div>
                                </t>
                            </t>
                            
                            <!-- Non-grouped View -->
                            <t t-if="group_by == 'none'">
                                <!-- Grid View -->
                                <t t-if="view_type == 'grid'">
                                    <div class="row">
                                        <t t-foreach="passengers" t-as="passenger">
                                            <t t-call="travel_agent_management.passenger_card_template"/>
                                        </t>
                                    </div>
                                </t>
                                
                                <!-- List View -->
                                <t t-if="view_type == 'list'">
                                    <t t-call="travel_agent_management.passenger_list_template">
                                        <t t-set="passenger_list" t-value="passengers"/>
                                    </t>
                                </t>
                            </t>
                            
                            <!-- Pagination -->
                            <t t-call="portal.pager"/>
                        </t>
                    </div>
                </div>
                
                <!-- JavaScript for search functionality -->
                <script type="text/javascript">
                    <![CDATA[
                    $(document).ready(function() {
                        console.log('Starting dropdown setup...');
                        
                        // Very simple dropdown implementation
                        $('.dropdown-toggle').off('click').on('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            
                            var $this = $(this);
                            var $dropdown = $this.closest('.dropdown');
                            var $menu = $dropdown.find('.dropdown-menu');
                            
                            console.log('Dropdown clicked:', $this.text().trim());
                            console.log('Menu found:', $menu.length);
                            
                            // Close all other dropdowns first
                            $('.dropdown').not($dropdown).removeClass('show');
                            $('.dropdown-menu').not($menu).hide();
                            
                            // Toggle this dropdown
                            if ($menu.is(':visible')) {
                                $menu.hide();
                                $dropdown.removeClass('show');
                                console.log('Closing dropdown');
                            } else {
                                $menu.show();
                                $dropdown.addClass('show');
                                console.log('Opening dropdown');
                            }
                        });
                        
                        // Close dropdowns when clicking outside
                        $(document).on('click', function(e) {
                            if (!$(e.target).closest('.dropdown').length) {
                                $('.dropdown-menu').hide();
                                $('.dropdown').removeClass('show');
                            }
                        });
                        
                        // Prevent dropdown from closing when clicking inside the menu
                        $('.dropdown-menu').on('click', function(e) {
                            e.stopPropagation();
                        });
                        
                        // Handle dropdown item clicks
                        $('.dropdown-item').on('click', function(e) {
                            var href = $(this).attr('href');
                            if (href &amp;&amp; href !== '#') {
                                console.log('Navigating to:', href);
                                window.location.href = href;
                            }
                        });
                        
                        // Search functionality
                        var searchTimeout;
                        $('#passenger_search').on('input', function() {
                            clearTimeout(searchTimeout);
                            var searchValue = $(this).val();
                            
                            if (searchValue.length >= 2 || searchValue.length === 0) {
                                searchTimeout = setTimeout(function() {
                                    performSearch();
                                }, 1000);
                            }
                        });
                        
                        $('#passenger_search').on('keypress', function(e) {
                            if (e.which === 13) {
                                e.preventDefault();
                                performSearch();
                            }
                        });
                        
                        function performSearch() {
                            var search = $('#passenger_search').val().trim();
                            var url = new URL(window.location.href);
                            
                            if (search) {
                                url.searchParams.set('search', search);
                            } else {
                                url.searchParams.delete('search');
                            }
                            url.searchParams.delete('page');
                            window.location.href = url.toString();
                        }
                        
                        console.log('Setup complete. Dropdowns found:', $('.dropdown-toggle').length);
                        
                        // Debug: Log each dropdown
                        $('.dropdown-toggle').each(function(i) {
                            var $this = $(this);
                            var $menu = $this.closest('.dropdown').find('.dropdown-menu');
                            console.log('Dropdown ' + i + ':', $this.text().trim(), 'Menu items:', $menu.find('.dropdown-item').length);
                        });
                    });
                    ]]>
                </script>
            </t>
        </template>
        
        <!-- Passenger Card Template for Grid View -->
        <template id="passenger_card_template" name="Passenger Card">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 shadow-sm border-0">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="card-title mb-0" t-esc="passenger.name"/>
                            <span t-if="passenger.passport_status == 'expired'" class="badge badge-danger">Expired</span>
                            <span t-if="passenger.passport_status == 'expiring'" class="badge badge-warning">Expiring</span>
                            <span t-if="passenger.passport_status == 'valid'" class="badge badge-success">Valid</span>
                        </div>
                        
                        <div class="passenger-info">
                            <div class="info-row mb-2">
                                <i class="fa fa-id-card text-muted mr-2"></i>
                                <strong>Passport:</strong> <span t-esc="passenger.passport_number"/>
                            </div>
                            <div class="info-row mb-2">
                                <i class="fa fa-flag text-muted mr-2"></i>
                                <strong>Nationality:</strong> <span t-esc="passenger.nationality_id.name"/>
                            </div>
                            <div class="info-row mb-2">
                                <i class="fa fa-calendar text-muted mr-2"></i>
                                <strong>Expires:</strong> <span t-esc="passenger.passport_expiration"/>
                                <t t-if="passenger.passport_status == 'expiring'">
                                    <br/><small class="text-warning">
                                        <i class="fa fa-exclamation-triangle"></i> 
                                        <span t-esc="passenger.days_until_passport_expiry"/> days remaining
                                    </small>
                                </t>
                            </div>
                            <t t-if="passenger.travel_count > 0">
                                <div class="info-row mb-2">
                                    <i class="fa fa-plane text-muted mr-2"></i>
                                    <strong>Travels:</strong> <span t-esc="passenger.travel_count"/> trip<t t-if="passenger.travel_count != 1">s</t>
                                </div>
                            </t>
                        </div>
                    </div>
                    <div class="card-footer bg-white border-top-0">
                        <div class="d-flex justify-content-between">
                            <a t-attf-href="/my/passenger/#{passenger.id}" class="btn btn-sm btn-outline-primary">
                                <i class="fa fa-eye"/> View Details
                            </a>
                            <small class="text-muted align-self-center">
                                <t t-if="passenger.last_travel_date">
                                    Last: <span t-esc="passenger.last_travel_date"/>
                                </t>
                                <t t-else="">New traveler</t>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        
        <!-- Passenger List Template for List View -->
        <template id="passenger_list_template" name="Passenger List">
            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead class="thead-dark">
                        <tr>
                            <th class="border-0">Name</th>
                            <th class="border-0">Passport</th>
                            <th class="border-0">Nationality</th>
                            <th class="border-0 text-center">Status</th>
                            <th class="border-0">Expiry Date</th>
                            <th class="border-0 text-center">Travels</th>
                            <th class="border-0 text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="passenger_list" t-as="passenger">
                            <tr class="passenger-row">
                                <td class="align-middle">
                                    <div class="passenger-name-cell">
                                        <strong t-esc="passenger.name"/>
                                        <t t-if="passenger.employee_id">
                                            <br/><small class="text-muted">
                                                <i class="fa fa-id-badge mr-1"></i>ID: <span t-esc="passenger.employee_id"/>
                                            </small>
                                        </t>
                                    </div>
                                </td>
                                <td class="align-middle">
                                    <span class="passport-number" t-esc="passenger.passport_number"/>
                                </td>
                                <td class="align-middle">
                                    <div class="nationality-cell">
                                        <i class="fa fa-flag mr-2"></i>
                                        <span t-esc="passenger.nationality_id.name"/>
                                    </div>
                                </td>
                                <td class="align-middle text-center">
                                    <span t-if="passenger.passport_status == 'expired'" class="badge badge-danger px-3 py-2">
                                        <i class="fa fa-exclamation-triangle mr-1"></i>Expired
                                    </span>
                                    <span t-if="passenger.passport_status == 'expiring'" class="badge badge-warning px-3 py-2">
                                        <i class="fa fa-clock mr-1"></i>Expiring (<span t-esc="passenger.days_until_passport_expiry"/> days)
                                    </span>
                                    <span t-if="passenger.passport_status == 'valid'" class="badge badge-success px-3 py-2">
                                        <i class="fa fa-check mr-1"></i>Valid
                                    </span>
                                </td>
                                <td class="align-middle">
                                    <div class="expiry-date-cell">
                                        <i class="fa fa-calendar mr-2 text-muted"></i>
                                        <span t-esc="passenger.passport_expiration"/>
                                    </div>
                                </td>
                                <td class="align-middle text-center">
                                    <div class="travel-info">
                                        <span class="badge badge-light border px-2 py-1">
                                            <i class="fa fa-plane mr-1"></i>
                                            <span t-esc="passenger.travel_count"/> trip<t t-if="passenger.travel_count != 1">s</t>
                                        </span>
                                        <t t-if="passenger.last_travel_date">
                                            <br/><small class="text-muted mt-1 d-block">
                                                Last: <span t-esc="passenger.last_travel_date"/>
                                            </small>
                                        </t>
                                        <t t-else="">
                                            <br/><small class="text-muted mt-1 d-block">New traveler</small>
                                        </t>
                                    </div>
                                </td>
                                <td class="align-middle text-center">
                                    <a t-attf-href="/my/passenger/#{passenger.id}" class="btn btn-sm btn-outline-primary">
                                        <i class="fa fa-eye"/> View
                                    </a>
                                </td>
                            </tr>
                        </t>
                    </tbody>
                </table>
            </div>
        </template>

        <!-- New Passenger Form -->
        <template id="portal_passenger_new" name="New Passenger">
            <t t-call="portal.portal_layout">
                <t t-set="title">Add Passenger</t>
                
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <h3>Add New Passenger</h3>
                            
                            <form action="/my/passenger/create" method="post" enctype="multipart/form-data" class="card">
                                <input name="csrf_token" t-att-value="request.csrf_token()" type="hidden"/>
                                
                                <div class="card-header">
                                    <h5>Passenger Information</h5>
                                </div>
                                
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="name">Full Name *</label>
                                                <input type="text" name="name" class="form-control" required="required" 
                                                       placeholder="As shown on passport"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="employee_id">Employee ID</label>
                                                <input type="text" name="employee_id" class="form-control" 
                                                       placeholder="Optional"/>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="email">Email</label>
                                                <input type="email" name="email" class="form-control" 
                                                       placeholder="<EMAIL>"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="phone">Phone</label>
                                                <input type="tel" name="phone" class="form-control" 
                                                       placeholder="+1234567890"/>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="date_of_birth">Date of Birth</label>
                                                <input type="date" name="date_of_birth" class="form-control"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="gender">Gender</label>
                                                <select name="gender" class="form-control">
                                                    <option value="">Select Gender</option>
                                                    <option value="male">Male</option>
                                                    <option value="female">Female</option>
                                                    <option value="other">Other</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <h5>Passport Information</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="passport_number">Passport Number *</label>
                                                <input type="text" name="passport_number" class="form-control" required="required"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="nationality_id">Nationality *</label>
                                                <select name="nationality_id" class="form-control" required="required">
                                                    <option value="">Select Country</option>
                                                    <t t-foreach="countries" t-as="country">
                                                        <option t-att-value="country.id">
                                                            <t t-esc="country.name"/>
                                                        </option>
                                                    </t>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="passport_expiration">Passport Expiration *</label>
                                                <input type="date" name="passport_expiration" class="form-control" required="required"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="passport_issue_date">Passport Issue Date</label>
                                                <input type="date" name="passport_issue_date" class="form-control"/>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="passport_issue_place">Passport Issue Place</label>
                                        <input type="text" name="passport_issue_place" class="form-control" 
                                               placeholder="City, Country"/>
                                    </div>
                                    
                                    <h5>Travel Preferences</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="seat_preference">Seat Preference</label>
                                                <select name="seat_preference" class="form-control">
                                                    <option value="no_preference">No Preference</option>
                                                    <option value="window">Window</option>
                                                    <option value="aisle">Aisle</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="meal_preference">Meal Preference</label>
                                                <select name="meal_preference" class="form-control">
                                                    <option value="regular">Regular</option>
                                                    <option value="vegetarian">Vegetarian</option>
                                                    <option value="vegan">Vegan</option>
                                                    <option value="halal">Halal</option>
                                                    <option value="kosher">Kosher</option>
                                                    <option value="special">Special Request</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="special_requirements">Special Requirements</label>
                                        <textarea name="special_requirements" class="form-control" rows="3" 
                                                 placeholder="Medical needs, accessibility requirements, etc."></textarea>
                                    </div>
                                    
                                    <h5>Document Uploads</h5>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="passport_files">Passport Documents</label>
                                                <input type="file" name="passport_files" class="form-control-file" 
                                                       multiple="multiple" accept="image/*,.pdf"/>
                                                <small class="form-text text-muted">Upload passport scans (PNG, JPG, PDF)</small>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="visa_files">Visa Documents</label>
                                                <input type="file" name="visa_files" class="form-control-file" 
                                                       multiple="multiple" accept="image/*,.pdf"/>
                                                <small class="form-text text-muted">Upload visa scans (PNG, JPG, PDF)</small>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="other_files">Other Documents</label>
                                                <input type="file" name="other_files" class="form-control-file" 
                                                       multiple="multiple" accept="image/*,.pdf"/>
                                                <small class="form-text text-muted">ID cards, certificates, etc.</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card-footer text-right">
                                    <a href="/my/passengers" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Save Passenger</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Passenger Detail View -->
        <template id="portal_passenger_detail" name="My Passenger Detail">
            <t t-call="portal.portal_layout">
                <t t-set="title" t-value="passenger.name"/>
                
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-10">
                            
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h4 class="mb-0">
                                        <span t-field="passenger.name"/>
                                    </h4>
                                    <div>
                                        <span t-if="passenger.passport_status == 'expired'" class="badge badge-danger">Passport Expired</span>
                                        <span t-if="passenger.passport_status == 'expiring'" class="badge badge-warning">Passport Expiring</span>
                                        <span t-if="passenger.passport_status == 'valid'" class="badge badge-success">Passport Valid</span>
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <!-- Basic Information -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <h5>Personal Information</h5>
                                            <p><strong>Full Name:</strong> <span t-field="passenger.name"/></p>
                                            <p t-if="passenger.employee_id"><strong>Employee ID:</strong> <span t-field="passenger.employee_id"/></p>
                                            <p t-if="passenger.email"><strong>Email:</strong> <span t-field="passenger.email"/></p>
                                            <p t-if="passenger.phone"><strong>Phone:</strong> <span t-field="passenger.phone"/></p>
                                            <p t-if="passenger.date_of_birth"><strong>Date of Birth:</strong> <span t-field="passenger.date_of_birth"/></p>
                                            <p t-if="passenger.gender"><strong>Gender:</strong> <span t-field="passenger.gender"/></p>
                                        </div>
                                        <div class="col-md-6">
                                            <h5>Passport Information</h5>
                                            <p><strong>Passport Number:</strong> <span t-field="passenger.passport_number"/></p>
                                            <p><strong>Nationality:</strong> <span t-field="passenger.nationality_id.name"/></p>
                                            <p><strong>Expiration Date:</strong> <span t-field="passenger.passport_expiration"/></p>
                                            <p t-if="passenger.passport_issue_date"><strong>Issue Date:</strong> <span t-field="passenger.passport_issue_date"/></p>
                                            <p t-if="passenger.passport_issue_place"><strong>Issue Place:</strong> <span t-field="passenger.passport_issue_place"/></p>
                                            <t t-if="passenger.passport_status == 'expiring'">
                                                <div class="alert alert-warning">
                                                    <strong>Warning:</strong> Passport expires in <span t-esc="passenger.days_until_passport_expiry"/> days!
                                                </div>
                                            </t>
                                            <t t-if="passenger.passport_status == 'expired'">
                                                <div class="alert alert-danger">
                                                    <strong>Alert:</strong> Passport has expired!
                                                </div>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Travel Preferences -->
                                    <div class="row mb-4">
                                        <div class="col-md-12">
                                            <h5>Travel Preferences</h5>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <p><strong>Seat Preference:</strong> <span t-field="passenger.seat_preference"/></p>
                                                </div>
                                                <div class="col-md-4">
                                                    <p><strong>Meal Preference:</strong> <span t-field="passenger.meal_preference"/></p>
                                                </div>
                                                <div class="col-md-4">
                                                    <p><strong>Travel Count:</strong> <span t-field="passenger.travel_count"/> completed trips</p>
                                                </div>
                                            </div>
                                            <t t-if="passenger.special_requirements">
                                                <p><strong>Special Requirements:</strong></p>
                                                <p t-field="passenger.special_requirements"/>
                                            </t>
                                            <t t-if="passenger.last_travel_date">
                                                <p><strong>Last Travel:</strong> <span t-field="passenger.last_travel_date"/></p>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Travel History -->
                                    <t t-if="passenger.passenger_line_ids">
                                        <h5>Recent Travel History</h5>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Request</th>
                                                        <th>Route</th>
                                                        <th>Departure</th>
                                                        <th>Return</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <t t-foreach="passenger.passenger_line_ids[:10]" t-as="line">
                                                        <tr>
                                                            <td>
                                                                <a t-attf-href="/my/travel_request/#{line.request_id.id}">
                                                                    <span t-field="line.request_id.name"/>
                                                                </a>
                                                            </td>
                                                            <td><span t-field="line.departure_route"/></td>
                                                            <td><span t-field="line.departure_date"/></td>
                                                            <td><span t-field="line.return_date"/></td>
                                                            <td><span t-field="line.request_id.state"/></td>
                                                        </tr>
                                                    </t>
                                                </tbody>
                                            </table>
                                        </div>
                                    </t>

                                    <!-- Documents Section -->
                                    <div class="row mt-4">
                                        <div class="col-md-4">
                                            <h5>Passport Documents</h5>
                                            <t t-if="passport_attachments">
                                                <t t-foreach="passport_attachments" t-as="attachment">
                                                    <div class="mb-2">
                                                        <a t-attf-href="/web/content/#{attachment['id']}?download=true" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fa fa-download"/> <span t-esc="attachment['name']"/>
                                                        </a>
                                                    </div>
                                                </t>
                                            </t>
                                            <t t-else="">
                                                <p class="text-muted">No passport documents uploaded</p>
                                            </t>
                                        </div>
                                        <div class="col-md-4">
                                            <h5>Visa Documents</h5>
                                            <t t-if="visa_attachments">
                                                <t t-foreach="visa_attachments" t-as="attachment">
                                                    <div class="mb-2">
                                                        <a t-attf-href="/web/content/#{attachment['id']}?download=true" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fa fa-download"/> <span t-esc="attachment['name']"/>
                                                        </a>
                                                    </div>
                                                </t>
                                            </t>
                                            <t t-else="">
                                                <p class="text-muted">No visa documents uploaded</p>
                                            </t>
                                        </div>
                                        <div class="col-md-4">
                                            <h5>Other Documents</h5>
                                            <t t-if="other_attachments">
                                                <t t-foreach="other_attachments" t-as="attachment">
                                                    <div class="mb-2">
                                                        <a t-attf-href="/web/content/#{attachment['id']}?download=true" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fa fa-download"/> <span t-esc="attachment['name']"/>
                                                        </a>
                                                    </div>
                                                </t>
                                            </t>
                                            <t t-else="">
                                                <p class="text-muted">No other documents uploaded</p>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Upload more documents section -->
                                    <div class="mt-4">
                                        <h5>Upload Additional Documents</h5>
                                        <form t-attf-action="/my/passenger/#{passenger.id}/upload" method="post" enctype="multipart/form-data">
                                            <input name="csrf_token" t-att-value="request.csrf_token()" type="hidden"/>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Document Type</label>
                                                        <select name="document_type" class="form-control" required="required">
                                                            <option value="">Select Type</option>
                                                            <option value="passport">Passport</option>
                                                            <option value="visa">Visa</option>
                                                            <option value="other">Other</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Files</label>
                                                        <input type="file" name="files" class="form-control-file" 
                                                               multiple="multiple" accept="image/*,.pdf" required="required"/>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="form-group">
                                                        <label> </label><br/>
                                                        <button type="submit" class="btn btn-primary">Upload</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                
                                <div class="card-footer">
                                    <a href="/my/passengers" class="btn btn-secondary">
                                        <i class="fa fa-arrow-left"/> Back to Passengers
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Simple Error Template -->
        <template id="portal_error_simple" name="Portal Error">
            <t t-call="portal.portal_layout">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="alert alert-danger text-center">
                                <h4>خطأ في تحميل الصفحة</h4>
                                <p t-esc="error_message or 'حدث خطأ غير متوقع'"/>
                                <a href="/my" class="btn btn-primary">العودة للصفحة الرئيسية</a>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- ========================================
             VISA REQUEST TEMPLATES
             ======================================== -->

        <!-- Visa Requests List View -->
        <template id="portal_my_visa_requests" name="My Visa Requests">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>

                <t t-call="portal.portal_searchbar">
                    <t t-set="title">Visa Requests</t>
                </t>

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h3>My Visa Requests</h3>
                                <a href="/my/visa_request/new" class="btn btn-primary">
                                    <i class="fa fa-plus"/> New Visa Request
                                </a>
                            </div>

                            <t t-if="not visa_requests">
                                <div class="alert alert-info text-center">
                                    <h4>No Visa Requests Found</h4>
                                    <p>You haven't submitted any visa requests yet.</p>
                                    <a href="/my/visa_request/new" class="btn btn-primary">
                                        <i class="fa fa-plus"/> Create Your First Visa Request
                                    </a>
                                </div>
                            </t>

                            <t t-if="visa_requests">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="thead-light">
                                            <tr>
                                                <th>Reference</th>
                                                <th>Country</th>
                                                <th>Visa Type</th>
                                                <th>Request Date</th>
                                                <th>Status</th>
                                                <th>Progress</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-foreach="visa_requests" t-as="visa_request">
                                                <tr>
                                                    <td>
                                                        <a t-attf-href="/my/visa_request/#{visa_request.id}">
                                                            <strong t-field="visa_request.name"/>
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <span t-field="visa_request.country_id.name"/>
                                                        <span t-field="visa_request.country_id.flag_emoji"/>
                                                    </td>
                                                    <td><span t-field="visa_request.visa_type_id.name"/></td>
                                                    <td><span t-field="visa_request.request_date"/></td>
                                                    <td>
                                                        <span t-if="visa_request.state == 'draft'" class="badge badge-secondary">Draft</span>
                                                        <span t-if="visa_request.state == 'in_progress'" class="badge badge-warning">In Progress</span>
                                                        <span t-if="visa_request.state == 'done'" class="badge badge-success">Completed</span>
                                                    </td>
                                                    <td>
                                                        <div class="progress" style="height: 20px;">
                                                            <div class="progress-bar" role="progressbar"
                                                                 t-attf-style="width: #{visa_request.document_completion_rate}%"
                                                                 t-attf-aria-valuenow="#{visa_request.document_completion_rate}"
                                                                 aria-valuemin="0" aria-valuemax="100">
                                                                <span t-esc="int(visa_request.document_completion_rate)"/>%
                                                            </div>
                                                        </div>
                                                        <small class="text-muted">
                                                            <span t-esc="visa_request.approved_documents"/>/<span t-esc="visa_request.total_documents"/> documents approved
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <a t-attf-href="/my/visa_request/#{visa_request.id}" class="btn btn-sm btn-outline-primary">
                                                            <i class="fa fa-eye"/> View
                                                        </a>
                                                    </td>
                                                </tr>
                                            </t>
                                        </tbody>
                                    </table>
                                </div>

                                <t t-call="portal.pager"/>
                            </t>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Visa Request Detail View -->
        <template id="portal_my_visa_request" name="My Visa Request">
            <t t-call="portal.portal_layout">
                <t t-set="title" t-value="visa_request.name"/>

                <div class="container">
                    <!-- Success/Error Messages -->
                    <t t-if="request.params.get('message') == 'created'">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <strong>Success!</strong> Your visa request has been created successfully.
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                    </t>
                    <t t-if="request.params.get('message') == 'created_with_files'">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <strong>Success!</strong> Your visa request has been created successfully with <span t-esc="request.params.get('files', '0')"/> document(s) uploaded.
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                    </t>
                    <t t-if="request.params.get('message') == 'uploaded'">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <strong>Success!</strong> <span t-esc="request.params.get('count', '1')"/> document(s) uploaded successfully.
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                    </t>
                    <t t-if="request.params.get('error')">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <strong>Error!</strong>
                            <t t-if="request.params.get('error') == 'upload_failed'">Failed to upload documents. Please try again.</t>
                            <t t-if="request.params.get('error') == 'no_files'">No files were selected for upload.</t>
                            <t t-if="request.params.get('error') == 'access_denied'">You don't have permission to access this visa request.</t>
                            <t t-if="request.params.get('error') == 'not_found'">Visa request not found.</t>
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                    </t>

                    <div class="row justify-content-center">
                        <div class="col-lg-10">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h4 class="mb-0">
                                        Visa Request: <span t-field="visa_request.name"/>
                                    </h4>
                                    <div>
                                        <span t-if="visa_request.state == 'draft'" class="badge badge-secondary">Draft</span>
                                        <span t-if="visa_request.state == 'in_progress'" class="badge badge-warning">In Progress</span>
                                        <span t-if="visa_request.state == 'done'" class="badge badge-success">Completed</span>
                                    </div>
                                </div>

                                <div class="card-body">
                                    <!-- Basic Information -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <h5>Visa Information</h5>
                                            <p><strong>Country:</strong>
                                                <span t-field="visa_request.country_id.name"/>
                                                <span t-field="visa_request.country_id.flag_emoji"/>
                                            </p>
                                            <p><strong>Visa Type:</strong> <span t-field="visa_request.visa_type_id.name"/></p>
                                            <p><strong>Category:</strong> <span t-field="visa_request.visa_type_id.visa_category"/></p>
                                            <p><strong>Processing Time:</strong> <span t-field="visa_request.visa_type_id.processing_days"/> days</p>
                                        </div>
                                        <div class="col-md-6">
                                            <h5>Request Details</h5>
                                            <p><strong>Request Date:</strong> <span t-field="visa_request.request_date"/></p>
                                            <p><strong>Price:</strong>
                                                <span t-field="visa_request.price" t-options="{'widget': 'monetary', 'display_currency': visa_request.currency_id}"/>
                                            </p>
                                            <t t-if="visa_request.completion_date">
                                                <p><strong>Completion Date:</strong> <span t-field="visa_request.completion_date"/></p>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Document Progress -->
                                    <div class="row mb-4">
                                        <div class="col-md-12">
                                            <h5>Document Progress</h5>
                                            <div class="progress mb-2" style="height: 25px;">
                                                <div class="progress-bar" role="progressbar"
                                                     t-attf-style="width: #{visa_request.document_completion_rate}%"
                                                     t-attf-aria-valuenow="#{visa_request.document_completion_rate}"
                                                     aria-valuemin="0" aria-valuemax="100">
                                                    <span t-esc="int(visa_request.document_completion_rate)"/>% Complete
                                                </div>
                                            </div>
                                            <p class="text-muted">
                                                <span t-esc="visa_request.approved_documents"/>/<span t-esc="visa_request.total_documents"/> documents approved
                                                (<span t-esc="visa_request.uploaded_documents"/>/<span t-esc="visa_request.total_documents"/> uploaded)
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Required Documents -->
                                    <t t-if="visa_request.document_ids">
                                        <h5>Required Documents</h5>
                                        <div class="table-responsive mb-4">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Document Type</th>
                                                        <th>Status</th>
                                                        <th>File</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <t t-foreach="visa_request.document_ids" t-as="document">
                                                        <tr>
                                                            <td>
                                                                <strong t-field="document.document_type_id.name"/>
                                                                <t t-if="document.document_type_id.description">
                                                                    <br/><small class="text-muted" t-field="document.document_type_id.description"/>
                                                                </t>
                                                            </td>
                                                            <td>
                                                                <t t-if="not document.datas">
                                                                    <span class="badge badge-secondary">Not Uploaded</span>
                                                                </t>
                                                                <t t-if="document.datas and not document.is_approved">
                                                                    <span class="badge badge-warning">Pending Review</span>
                                                                </t>
                                                                <t t-if="document.datas and document.is_approved">
                                                                    <span class="badge badge-success">Approved</span>
                                                                </t>
                                                            </td>
                                                            <td>
                                                                <t t-if="document.filename">
                                                                    <span t-field="document.filename"/>
                                                                    <br/><small class="text-muted">
                                                                        <span t-esc="'%.1f' % document.file_size"/> MB
                                                                    </small>
                                                                </t>
                                                                <t t-else="">
                                                                    <span class="text-muted">No file uploaded</span>
                                                                </t>
                                                            </td>
                                                            <td>
                                                                <t t-if="document.datas">
                                                                    <a t-attf-href="/web/content/visa.request.document/#{document.id}/datas/#{document.filename}?download=true"
                                                                       class="btn btn-sm btn-outline-primary" target="_blank">
                                                                        <i class="fa fa-download"/> Download
                                                                    </a>
                                                                    <br/><br/>
                                                                    <form t-attf-action="/my/visa_request/#{visa_request.id}/upload" method="post" enctype="multipart/form-data" style="display: inline-block;">
                                                                        <input name="csrf_token" t-att-value="request.csrf_token()" type="hidden"/>
                                                                        <div class="input-group input-group-sm">
                                                                            <input t-attf-name="document_#{document.id}" type="file" class="form-control-file"
                                                                                   t-attf-accept="#{document.document_type_id.file_type == 'image' and 'image/*' or document.document_type_id.file_type == 'pdf' and 'application/pdf' or '*/*'}"/>
                                                                            <div class="input-group-append">
                                                                                <button type="submit" class="btn btn-sm btn-warning">
                                                                                    <i class="fa fa-refresh"/> Replace
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                        <small class="text-muted">Replace with new file</small>
                                                                    </form>
                                                                </t>
                                                                <t t-else="">
                                                                    <form t-attf-action="/my/visa_request/#{visa_request.id}/upload" method="post" enctype="multipart/form-data" style="display: inline-block;">
                                                                        <input name="csrf_token" t-att-value="request.csrf_token()" type="hidden"/>
                                                                        <div class="input-group input-group-sm">
                                                                            <input t-attf-name="document_#{document.id}" type="file" class="form-control-file"
                                                                                   t-attf-accept="#{document.document_type_id.file_type == 'image' and 'image/*' or document.document_type_id.file_type == 'pdf' and 'application/pdf' or '*/*'}"
                                                                                   required="required"/>
                                                                            <div class="input-group-append">
                                                                                <button type="submit" class="btn btn-sm btn-primary">
                                                                                    <i class="fa fa-upload"/> Upload
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                        <small class="text-muted">
                                                                            Max: <span t-esc="document.document_type_id.max_file_size"/> MB
                                                                            <t t-if="document.document_type_id.file_type != 'any'">
                                                                                | Type: <span t-esc="document.document_type_id.file_type"/>
                                                                            </t>
                                                                        </small>
                                                                    </form>
                                                                </t>
                                                            </td>
                                                        </tr>
                                                    </t>
                                                </tbody>
                                            </table>
                                        </div>
                                    </t>

                                    <!-- Notes -->
                                    <t t-if="visa_request.notes">
                                        <div class="row mb-4">
                                            <div class="col-md-12">
                                                <h5>Notes</h5>
                                                <p t-field="visa_request.notes"/>
                                            </div>
                                        </div>
                                    </t>
                                </div>

                                <div class="card-footer">
                                    <a href="/my/visa_requests" class="btn btn-secondary">
                                        <i class="fa fa-arrow-left"/> Back to Visa Requests
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- New Visa Request Form -->
        <template id="portal_visa_request_new" name="New Visa Request">
            <t t-call="portal.portal_layout">
                <t t-set="title">New Visa Request</t>

                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="mb-0">Create New Visa Request</h4>
                                </div>

                                <form action="/my/visa_request/create" method="post" enctype="multipart/form-data">
                                    <input name="csrf_token" t-att-value="request.csrf_token()" type="hidden"/>

                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="visa_type_id">Visa Type *</label>
                                            <select name="visa_type_id" id="visa_type_id" class="form-control" required="required">
                                                <option value="">Select Visa Type</option>
                                                <t t-foreach="visa_types_grouped" t-as="country_group">
                                                    <optgroup t-att-label="country_group['country'].flag_emoji + ' ' + country_group['country'].name">
                                                        <t t-foreach="country_group['visa_types']" t-as="visa_type">
                                                            <option t-att-value="visa_type.id">
                                                                <span t-esc="visa_type.name"/> (<span t-esc="visa_type.visa_category"/>) - $<span t-esc="visa_type.price"/>
                                                            </option>
                                                        </t>
                                                    </optgroup>
                                                </t>
                                            </select>
                                        </div>

                                        <!-- Required Documents Preview -->
                                        <div id="required_documents" class="form-group" style="display: none;">
                                            <h6>Required Documents for this Visa Type:</h6>
                                            <div id="documents_list" class="alert alert-info">
                                                <!-- Will be populated by JavaScript -->
                                            </div>
                                        </div>

                                        <!-- Document Upload Section -->
                                        <div id="document_upload_section" class="form-group" style="display: none;">
                                            <h6>Upload Required Documents:</h6>
                                            <div id="upload_forms_container">
                                                <!-- Will be populated by JavaScript -->
                                            </div>
                                            <div class="alert alert-warning">
                                                <i class="fa fa-exclamation-triangle"/>
                                                <strong>Note:</strong> You can upload documents now or after creating the visa request.
                                                All uploaded documents will be attached to your visa request.
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="notes">Additional Notes</label>
                                            <textarea name="notes" class="form-control" rows="4"
                                                     placeholder="Any special requirements or additional information..."></textarea>
                                        </div>

                                        <div class="alert alert-info">
                                            <h6><i class="fa fa-info-circle"/> What happens next?</h6>
                                            <ul class="mb-0">
                                                <li>Your visa request will be created with a unique reference number</li>
                                                <li>Required documents will be automatically generated based on the visa type</li>
                                                <li>You can upload documents and track progress from your visa request page</li>
                                                <li>Our team will review your documents and update the status</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="card-footer text-right">
                                        <a href="/my/visa_requests" class="btn btn-secondary">Cancel</a>
                                        <button type="submit" class="btn btn-primary">Create Visa Request</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- JavaScript for dynamic visa type loading -->
                <script type="text/javascript">
                    // Wait for DOM and ensure jQuery is loaded
                    function initVisaForm() {
                        try {
                            var visaTypesData = <t t-raw="visa_types_json"/>;
                            console.log('Visa Types Data:', visaTypesData);
                            console.log('Data type:', typeof visaTypesData);
                            console.log('Data length:', visaTypesData ? visaTypesData.length : 'undefined');

                            // Check if jQuery is loaded
                            if (typeof $ === 'undefined') {
                                console.error('jQuery is not loaded! Using vanilla JS fallback.');
                                initWithVanillaJS(visaTypesData);
                                return;
                            }

                            console.log('jQuery is available, using jQuery version');

                            $('#country_id').change(function() {
                                var countryId = parseInt($(this).val());
                                var visaTypeSelect = $('#visa_type_id');
                                var documentsDiv = $('#required_documents');

                                console.log('Selected country ID:', countryId);

                                // Clear visa types
                                visaTypeSelect.html('<option value="">Loading...</option>');
                                documentsDiv.hide();

                                if (countryId) {
                                    // Enable visa type select
                                    visaTypeSelect.prop('disabled', false);

                                    // Try to use local data first
                                    if (visaTypesData &amp;&amp; Array.isArray(visaTypesData)) {
                                        var filteredTypes = visaTypesData.filter(function(vt) {
                                            return parseInt(vt.country_id) === countryId;
                                        });

                                        console.log('Filtered visa types (local):', filteredTypes);
                                        populateVisaTypes(filteredTypes);
                                    } else {
                                        // Fallback to AJAX
                                        console.log('Using AJAX fallback for visa types');
                                        $.ajax({
                                            url: '/my/visa_request/get_visa_types',
                                            type: 'POST',
                                            contentType: 'application/json',
                                            data: JSON.stringify({
                                                'jsonrpc': '2.0',
                                                'method': 'call',
                                                'params': {'country_id': countryId}
                                            }),
                                            success: function(response) {
                                                console.log('AJAX response:', response);
                                                if (response.result &amp;&amp; response.result.visa_types) {
                                                    populateVisaTypes(response.result.visa_types);
                                                } else {
                                                    visaTypeSelect.html('<option value="">No visa types found</option>');
                                                }
                                            },
                                            error: function(xhr, status, error) {
                                                console.error('AJAX error:', error);
                                                visaTypeSelect.html('<option value="">Error loading visa types</option>');
                                            }
                                        });
                                    }
                                } else {
                                    visaTypeSelect.html('<option value="">Select Country First</option>');
                                    visaTypeSelect.prop('disabled', true);
                                }
                            });

                            function populateVisaTypes(visaTypes) {
                                var visaTypeSelect = $('#visa_type_id');
                                visaTypeSelect.html('<option value="">Select Visa Type</option>');

                                if (visaTypes &amp;&amp; visaTypes.length &gt; 0) {
                                    visaTypes.forEach(function(visaType) {
                                        visaTypeSelect.append(
                                            '<option value="' + visaType.id + '">' +
                                            visaType.name + ' (' + visaType.visa_category + ')' +
                                            '</option>'
                                        );
                                    });
                                    // Store for document preview
                                    window.currentVisaTypes = visaTypes;
                                } else {
                                    visaTypeSelect.append('<option value="">No visa types available for this country</option>');
                                }
                            }

                            $('#visa_type_id').change(function() {
                                var visaTypeId = parseInt($(this).val());
                                var documentsDiv = $('#required_documents');
                                var documentsList = $('#documents_list');

                                if (visaTypeId) {
                                    // Find selected visa type from current data or fallback to original
                                    var dataSource = window.currentVisaTypes || visaTypesData || [];
                                    var selectedType = dataSource.find(function(vt) {
                                        return parseInt(vt.id) === visaTypeId;
                                    });

                                    console.log('Selected visa type:', selectedType);
                                    console.log('Data source:', dataSource);

                                    if (selectedType &amp;&amp; selectedType.document_types &amp;&amp; selectedType.document_types.length &gt; 0) {
                                        var html = '<ul class="mb-0">';
                                        selectedType.document_types.forEach(function(doc) {
                                            html += '<li><strong>' + doc.name + '</strong>';
                                            if (doc.description) {
                                                html += ' - ' + doc.description;
                                            }
                                            html += ' <small class="text-muted">(' + doc.file_type + ', max ' + doc.max_file_size + 'MB)</small></li>';
                                        });
                                        html += '</ul>';
                                        documentsList.html(html);
                                        documentsDiv.show();
                                    } else {
                                        documentsDiv.hide();
                                    }
                                } else {
                                    documentsDiv.hide();
                                }
                            });

                        } catch (error) {
                            console.error('JavaScript error in visa request form:', error);
                        }
                    }

                    // Vanilla JavaScript fallback
                    function initWithVanillaJS(visaTypesData) {
                        console.log('Initializing with vanilla JavaScript');

                        var visaTypeSelect = document.getElementById('visa_type_id');
                        var documentsDiv = document.getElementById('required_documents');
                        var documentsList = document.getElementById('documents_list');
                        var uploadSection = document.getElementById('document_upload_section');
                        var uploadContainer = document.getElementById('upload_forms_container');

                        if (!visaTypeSelect) {
                            console.error('Visa type select not found');
                            return;
                        }

                        // Direct visa type selection (no country filter needed)
                        console.log('Setting up visa type change listener');

                        visaTypeSelect.addEventListener('change', function() {
                            var visaTypeId = parseInt(this.value);
                            console.log('Selected visa type ID:', visaTypeId);

                            if (visaTypeId &amp;&amp; visaTypesData) {
                                var selectedType = visaTypesData.find(function(vt) {
                                    return parseInt(vt.id) === visaTypeId;
                                });

                                console.log('Selected visa type (vanilla):', selectedType);

                                if (selectedType &amp;&amp; selectedType.document_types &amp;&amp; selectedType.document_types.length &gt; 0) {
                                    // Show document preview
                                    if (documentsList) {
                                        var html = '&lt;ul class="mb-0"&gt;';
                                        selectedType.document_types.forEach(function(doc) {
                                            html += '&lt;li&gt;&lt;strong&gt;' + doc.name + '&lt;/strong&gt;';
                                            if (doc.description) {
                                                html += ' - ' + doc.description;
                                            }
                                            html += ' &lt;small class="text-muted"&gt;(' + doc.file_type + ', max ' + doc.max_file_size + 'MB)&lt;/small&gt;&lt;/li&gt;';
                                        });
                                        html += '&lt;/ul&gt;';
                                        documentsList.innerHTML = html;
                                        if (documentsDiv) documentsDiv.style.display = 'block';
                                    }

                                    // Create upload forms
                                    if (uploadContainer) {
                                        createUploadForms(selectedType.document_types, uploadContainer);
                                        if (uploadSection) uploadSection.style.display = 'block';
                                    }
                                } else {
                                    if (documentsDiv) documentsDiv.style.display = 'none';
                                    if (uploadSection) uploadSection.style.display = 'none';
                                }
                            } else {
                                if (documentsDiv) documentsDiv.style.display = 'none';
                                if (uploadSection) uploadSection.style.display = 'none';
                            }
                        });
                    }

                    // Visa types are now populated directly in the template, no need for dynamic population

                    function createUploadForms(documentTypes, container) {
                        if (!container) return;

                        var html = '';
                        documentTypes.forEach(function(doc, index) {
                            var acceptTypes = '';
                            if (doc.file_type === 'image') {
                                acceptTypes = 'image/*';
                            } else if (doc.file_type === 'pdf') {
                                acceptTypes = 'application/pdf';
                            } else {
                                acceptTypes = '*/*';
                            }

                            var requiredText = doc.is_required ? ' *' : ' (Optional)';
                            var requiredAttr = doc.is_required ? 'required' : '';

                            html += '&lt;div class="card mb-3"&gt;';
                            html += '  &lt;div class="card-body"&gt;';
                            html += '    &lt;h6 class="card-title"&gt;' + doc.name + requiredText + '&lt;/h6&gt;';
                            if (doc.description) {
                                html += '    &lt;p class="text-muted small"&gt;' + doc.description + '&lt;/p&gt;';
                            }
                            html += '    &lt;div class="input-group"&gt;';
                            html += '      &lt;input type="file" name="document_' + doc.id + '" class="form-control-file" ';
                            html += '             accept="' + acceptTypes + '" ' + requiredAttr + '&gt;';
                            html += '    &lt;/div&gt;';
                            html += '    &lt;small class="text-muted"&gt;';
                            html += '      Max size: ' + doc.max_file_size + 'MB';
                            if (doc.file_type !== 'any') {
                                html += ' | Type: ' + doc.file_type.toUpperCase();
                            }
                            html += '    &lt;/small&gt;';
                            html += '  &lt;/div&gt;';
                            html += '&lt;/div&gt;';
                        });

                        container.innerHTML = html;
                    }

                    // Initialize when DOM is ready
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', initVisaForm);
                    } else {
                        initVisaForm();
                    }

                    // Also try with jQuery when it becomes available
                    if (typeof $ !== 'undefined') {
                        $(document).ready(initVisaForm);
                    } else {
                        // Wait a bit for jQuery to load
                        setTimeout(function() {
                            if (typeof $ !== 'undefined') {
                                $(document).ready(initVisaForm);
                            }
                        }, 1000);
                    }
                </script>
            </t>
        </template>

        <!-- ===== TRANSFER REQUEST PORTAL TEMPLATES ===== -->

        <!-- Transfer Requests List Template -->
        <template id="portal_my_transfer_requests" name="My Transfer Requests">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>

                <t t-call="portal.portal_searchbar">
                    <t t-set="title">Transfer Requests</t>
                </t>

                <t t-if="not transfers">
                    <div class="alert alert-warning mt-3" role="alert">
                        <strong>No transfer requests found.</strong>
                        <p class="mb-0">
                            <a href="/my/transfer_request/new" class="btn btn-primary">
                                <i class="fa fa-plus"/> Create New Transfer Request
                            </a>
                        </p>
                    </div>
                </t>
                <t t-if="transfers" t-call="portal.portal_table">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>Transfer Requests</h3>
                        <a href="/my/transfer_request/new" class="btn btn-primary">
                            <i class="fa fa-plus"/> New Transfer Request
                        </a>
                    </div>

                    <thead>
                        <tr class="active">
                            <th>Reference</th>
                            <th>Route</th>
                            <th>Departure Date</th>
                            <th>Passengers</th>
                            <th>Status</th>
                            <th>Supplier</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="transfers" t-as="transfer">
                            <tr>
                                <td>
                                    <a t-att-href="'/my/transfer_request/%s' % transfer.id">
                                        <t t-esc="transfer.name"/>
                                    </a>
                                </td>
                                <td><t t-esc="transfer.transfer_route"/></td>
                                <td><span t-field="transfer.departure_date"/></td>
                                <td><t t-esc="transfer.passenger_count"/></td>
                                <td>
                                    <span t-att-class="'badge badge-%s' % ('success' if transfer.state == 'confirmed' else 'info' if transfer.state == 'draft' else 'secondary')">
                                        <t t-esc="transfer.state.title()"/>
                                    </span>
                                </td>
                                <td><t t-esc="transfer.supplier_name"/></td>
                            </tr>
                        </t>
                    </tbody>
                </t>
            </t>
        </template>

        <!-- Transfer Request Detail Template -->
        <template id="portal_transfer_request_detail" name="Transfer Request Detail">
            <t t-call="portal.portal_layout">
                <t t-set="title" t-value="transfer.name"/>

                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-10">

                            <!-- Success Message -->
                            <t t-if="success_message">
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fa fa-check-circle mr-2"></i>
                                    <t t-esc="success_message"/>
                                    <button type="button" class="close" data-dismiss="alert">
                                        <span>×</span>
                                    </button>
                                </div>
                            </t>

                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h4 class="mb-0">
                                        <span t-field="transfer.name"/>
                                    </h4>
                                    <span t-att-class="'badge badge-%s' % ('success' if transfer.state == 'confirmed' else 'info' if transfer.state == 'draft' else 'secondary')">
                                        <t t-esc="transfer.state.title()"/>
                                    </span>
                                </div>

                                <div class="card-body">
                                    <!-- Transfer Information -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <h5>Transfer Details</h5>
                                            <p><strong>From:</strong> <span t-field="transfer.transfer_from"/></p>
                                            <p><strong>To:</strong> <span t-field="transfer.transfer_to"/></p>
                                            <p><strong>Route:</strong> <span t-field="transfer.transfer_route"/></p>
                                            <p><strong>Passengers:</strong> <span t-field="transfer.passenger_count"/></p>
                                            <p><strong>Vehicle Type:</strong> <span t-field="transfer.vehicle_type"/></p>
                                        </div>
                                        <div class="col-md-6">
                                            <h5>Schedule</h5>
                                            <p><strong>Departure Date:</strong> <span t-field="transfer.departure_date"/></p>
                                            <p><strong>Departure Time:</strong> <span t-field="transfer.departure_time" t-options="{'widget': 'float_time'}"/></p>
                                            <t t-if="transfer.arrive_date">
                                                <p><strong>Arrival Date:</strong> <span t-field="transfer.arrive_date"/></p>
                                            </t>
                                            <t t-if="transfer.arrive_time">
                                                <p><strong>Arrival Time:</strong> <span t-field="transfer.arrive_time" t-options="{'widget': 'float_time'}"/></p>
                                            </t>
                                            <t t-if="transfer.flight_number">
                                                <p><strong>Flight Number:</strong> <span t-field="transfer.flight_number"/></p>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Contact Information -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <h5>Contact Information</h5>
                                            <p><strong>Contact Phone:</strong> <span t-field="transfer.contact_phone"/></p>
                                        </div>
                                        <div class="col-md-6">
                                            <h5>Supplier</h5>
                                            <p><strong>Supplier:</strong> <span t-esc="transfer.supplier_name"/></p>
                                            <t t-if="transfer.supplier_phone">
                                                <p><strong>Supplier Phone:</strong> <span t-field="transfer.supplier_phone"/></p>
                                            </t>
                                        </div>
                                    </div>

                                    <!-- Special Requirements -->
                                    <t t-if="transfer.special_requirements">
                                        <div class="row mb-4">
                                            <div class="col-12">
                                                <h5>Special Requirements</h5>
                                                <p t-field="transfer.special_requirements"/>
                                            </div>
                                        </div>
                                    </t>

                                    <!-- Customer Notes -->
                                    <t t-if="transfer.customer_notes">
                                        <div class="row mb-4">
                                            <div class="col-12">
                                                <h5>Customer Notes</h5>
                                                <p t-field="transfer.customer_notes"/>
                                            </div>
                                        </div>
                                    </t>

                                    <!-- Pricing Information -->
                                    <t t-if="transfer.customer_price">
                                        <div class="row mb-4">
                                            <div class="col-md-6">
                                                <h5>Pricing</h5>
                                                <p><strong>Price:</strong>
                                                   <span t-field="transfer.customer_price" t-options="{'widget': 'monetary', 'display_currency': transfer.currency_id}"/>
                                                </p>
                                            </div>
                                        </div>
                                    </t>
                                </div>
                            </div>

                            <!-- Message Area -->
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5>Communication</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Messages thread -->
                                    <t t-call="portal.message_thread">
                                        <t t-set="object" t-value="transfer"/>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Transfer Request Creation Form -->
        <template id="portal_transfer_request_new" name="New Transfer Request">
            <t t-call="portal.portal_layout">
                <t t-set="title">New Transfer Request</t>

                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-10">
                            <h3>Create New Transfer Request</h3>

                            <!-- Error Message -->
                            <t t-if="error_message">
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fa fa-exclamation-triangle mr-2"></i>
                                    <t t-esc="error_message"/>
                                    <button type="button" class="close" data-dismiss="alert">
                                        <span>×</span>
                                    </button>
                                </div>
                            </t>

                            <form action="/my/transfer_request/create" method="post" class="card">
                                <input name="csrf_token" t-att-value="request.csrf_token()" type="hidden"/>

                                <div class="card-header">
                                    <h5>Transfer Request Details</h5>
                                </div>

                                <div class="card-body">
                                    <!-- Transfer Route Information -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="transfer_from">From Location *</label>
                                                <input type="text" name="transfer_from" class="form-control" required="required"
                                                       placeholder="Departure location" t-att-value="transfer_from or ''"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="transfer_to">To Location *</label>
                                                <input type="text" name="transfer_to" class="form-control" required="required"
                                                       placeholder="Destination location" t-att-value="transfer_to or ''"/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Schedule Information -->
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="departure_date">Departure Date *</label>
                                                <input type="date" name="departure_date" class="form-control" required="required" t-att-value="departure_date or ''"/>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="departure_time">Departure Time</label>
                                                <input type="time" name="departure_time" class="form-control" t-att-value="departure_time or ''"/>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="passenger_count">Number of Passengers *</label>
                                                <input type="number" name="passenger_count" class="form-control"
                                                       min="1" max="50" t-att-value="passenger_count or '1'" required="required"/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Arrival Information -->
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="arrive_date">Arrival Date</label>
                                                <input type="date" name="arrive_date" class="form-control" t-att-value="arrive_date or ''"/>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="arrive_time">Arrival Time</label>
                                                <input type="time" name="arrive_time" class="form-control" t-att-value="arrive_time or ''"/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Vehicle and Contact Information -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="vehicle_type">Vehicle Type</label>
                                                <select name="vehicle_type" class="form-control">
                                                    <option value="">Select Vehicle Type</option>
                                                    <option value="sedan" t-att-selected="'selected' if vehicle_type == 'sedan' else None">Sedan</option>
                                                    <option value="suv" t-att-selected="'selected' if vehicle_type == 'suv' else None">SUV</option>
                                                    <option value="van" t-att-selected="'selected' if vehicle_type == 'van' else None">Van</option>
                                                    <option value="bus" t-att-selected="'selected' if vehicle_type == 'bus' else None">Bus</option>
                                                    <option value="luxury" t-att-selected="'selected' if vehicle_type == 'luxury' else None">Luxury Car</option>
                                                    <option value="other" t-att-selected="'selected' if vehicle_type == 'other' else None">Other</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="contact_phone">Contact Phone *</label>
                                                <input type="tel" name="contact_phone" class="form-control" required="required"
                                                       placeholder="Your contact phone number" t-att-value="contact_phone or ''"/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Flight Information -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="flight_number">Flight Number</label>
                                                <input type="text" name="flight_number" class="form-control"
                                                       placeholder="Flight number (if applicable)" t-att-value="flight_number or ''"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="supplier_id">Preferred Supplier</label>
                                                <select name="supplier_id" class="form-control">
                                                    <option value="">Select Supplier (Optional)</option>
                                                    <t t-foreach="suppliers" t-as="supplier">
                                                        <option t-att-value="supplier.id" t-att-selected="'selected' if str(supplier.id) == str(supplier_id or '') else None">
                                                            <t t-esc="supplier.name"/>
                                                        </option>
                                                    </t>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Special Requirements -->
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <label for="special_requirements">Special Requirements</label>
                                                <textarea name="special_requirements" class="form-control" rows="3"
                                                         placeholder="Any special requirements (wheelchair access, child seats, etc.)" t-raw="special_requirements or ''"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Customer Notes -->
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <label for="customer_notes">Additional Notes</label>
                                                <textarea name="customer_notes" class="form-control" rows="3"
                                                         placeholder="Any additional information or requests" t-raw="customer_notes or ''"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card-footer text-right">
                                    <a href="/my/transfer_request" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Submit Request</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- ===== HOTEL BOOKING REQUEST PORTAL TEMPLATES ===== -->

        <!-- Hotel Booking Requests List Template -->
        <template id="portal_my_hotel_bookings" name="My Hotel Bookings">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>

                <t t-call="portal.portal_searchbar">
                    <t t-set="title">Hotel Bookings</t>
                </t>

                <t t-if="not hotel_bookings">
                    <div class="alert alert-warning mt-3" role="alert">
                        <strong>No hotel booking requests found.</strong>
                        <p class="mb-0">
                            <a href="/my/hotel_booking/new" class="btn btn-primary">
                                <i class="fa fa-plus"/> Create New Hotel Booking
                            </a>
                        </p>
                    </div>
                </t>
                <t t-if="hotel_bookings" t-call="portal.portal_table">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>Hotel Booking Requests</h3>
                        <a href="/my/hotel_booking/new" class="btn btn-primary">
                            <i class="fa fa-plus"/> New Hotel Booking
                        </a>
                    </div>

                    <thead>
                        <tr class="active">
                            <th>Reference</th>
                            <th>Hotel</th>
                            <th>Check-in</th>
                            <th>Check-out</th>
                            <th>Guests</th>
                            <th>Status</th>
                            <th>Supplier</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="hotel_bookings" t-as="booking">
                            <tr>
                                <td>
                                    <a t-att-href="'/my/hotel_booking/%s' % booking.id">
                                        <t t-esc="booking.name"/>
                                    </a>
                                </td>
                                <td><t t-esc="booking.hotel_name"/></td>
                                <td><span t-field="booking.check_in_date"/></td>
                                <td><span t-field="booking.check_out_date"/></td>
                                <td><t t-esc="booking.number_of_pax"/></td>
                                <td>
                                    <span t-att-class="'badge badge-%s' % ('success' if booking.state == 'confirmed' else 'info' if booking.state == 'draft' else 'secondary')">
                                        <t t-esc="booking.state.title()"/>
                                    </span>
                                </td>
                                <td><t t-esc="booking.supplier_name"/></td>
                            </tr>
                        </t>
                    </tbody>
                </t>
            </t>
        </template>

        <!-- Hotel Booking Detail Template -->
        <template id="portal_hotel_booking_detail" name="Hotel Booking Detail">
            <t t-call="portal.portal_layout">
                <t t-set="o_portal_fullwidth_alert" t-value="True"/>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">
                                    <i class="fa fa-hotel text-primary"/> Hotel Booking Request
                                    <small class="text-muted">- <t t-esc="booking.name"/></small>
                                </h4>
                                <span t-att-class="'badge badge-lg badge-%s' % ('success' if booking.state == 'confirmed' else 'info' if booking.state == 'draft' else 'secondary')">
                                    <t t-esc="booking.state.title()"/>
                                </span>
                            </div>
                            <div class="card-body">
                                <!-- Success Message -->
                                <t t-if="success_message">
                                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                                        <t t-esc="success_message"/>
                                        <button type="button" class="close" data-dismiss="alert">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                    </div>
                                </t>

                                <!-- Hotel Information -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <h5>Hotel Information</h5>
                                        <p><strong>Hotel Name:</strong> <span t-field="booking.hotel_name"/></p>
                                        <p><strong>City:</strong> <span t-field="booking.city_name"/></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Booking Details</h5>
                                        <p><strong>Check-in Date:</strong> <span t-field="booking.check_in_date"/></p>
                                        <p><strong>Check-out Date:</strong> <span t-field="booking.check_out_date"/></p>
                                        <p><strong>Duration:</strong> <span t-field="booking.duration_nights"/> nights</p>
                                    </div>
                                </div>

                                <!-- Room Information -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <h5>Room Information</h5>
                                        <p><strong>Number of Guests:</strong> <span t-field="booking.number_of_pax"/></p>
                                        <t t-if="booking.room_type">
                                            <p><strong>Room Type:</strong> <span t-field="booking.room_type"/></p>
                                        </t>
                                        <t t-if="booking.board">
                                            <p><strong>Board:</strong> <span t-field="booking.board"/></p>
                                        </t>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Supplier</h5>
                                        <p><strong>Supplier:</strong> <span t-esc="booking.supplier_name"/></p>
                                    </div>
                                </div>

                                <!-- Special Requests -->
                                <t t-if="booking.special_request">
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <h5>Special Requests</h5>
                                            <p t-field="booking.special_request"/>
                                        </div>
                                    </div>
                                </t>

                                <!-- Customer Notes -->
                                <t t-if="booking.customer_notes">
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <h5>Customer Notes</h5>
                                            <p t-field="booking.customer_notes"/>
                                        </div>
                                    </div>
                                </t>

                                <!-- Pricing Information -->
                                <t t-if="booking.customer_price">
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <h5>Pricing</h5>
                                            <p><strong>Total Price:</strong> <span t-field="booking.customer_price" t-options="{'widget': 'monetary'}"/></p>
                                        </div>
                                    </div>
                                </t>

                                <!-- Action Buttons -->
                                <div class="row">
                                    <div class="col-12">
                                        <a href="/my/hotel_booking" class="btn btn-secondary">
                                            <i class="fa fa-arrow-left"/> Back to Hotel Bookings
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Hotel Booking Creation Form Template -->
        <template id="portal_hotel_booking_new" name="New Hotel Booking">
            <t t-call="portal.portal_layout">
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="mb-0">
                                    <i class="fa fa-hotel text-primary"/> Create New Hotel Booking Request
                                </h4>
                            </div>
                            <div class="card-body">
                                <!-- Error Message -->
                                <t t-if="error_message">
                                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                        <t t-esc="error_message"/>
                                        <button type="button" class="close" data-dismiss="alert">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                    </div>
                                </t>

                                <form action="/my/hotel_booking/create" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                    <!-- Hotel Information -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="city_name">City Name *</label>
                                                <input type="text" name="city_name" class="form-control" required="required"
                                                       placeholder="City where hotel is located" t-att-value="city_name or ''"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="hotel_name">Hotel Name *</label>
                                                <input type="text" name="hotel_name" class="form-control" required="required"
                                                       placeholder="Name of the hotel" t-att-value="hotel_name or ''"/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Booking Dates -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="check_in_date">Check-in Date *</label>
                                                <input type="date" name="check_in_date" class="form-control" required="required"
                                                       t-att-value="check_in_date or ''"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="check_out_date">Check-out Date *</label>
                                                <input type="date" name="check_out_date" class="form-control" required="required"
                                                       t-att-value="check_out_date or ''"/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Room Details -->
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="number_of_pax">Number of Guests *</label>
                                                <input type="number" name="number_of_pax" class="form-control" required="required"
                                                       min="1" placeholder="Number of guests" t-att-value="number_of_pax or '1'"/>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="room_type">Room Type</label>
                                                <input type="text" name="room_type" class="form-control"
                                                       placeholder="Single, Double, Suite, etc." t-att-value="room_type or ''"/>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="board">Board</label>
                                                <select name="board" class="form-control">
                                                    <option value="">Select Board Type</option>
                                                    <option value="room_only" t-att-selected="'selected' if board == 'room_only' else None">Room Only</option>
                                                    <option value="breakfast" t-att-selected="'selected' if board == 'breakfast' else None">Breakfast</option>
                                                    <option value="half_board" t-att-selected="'selected' if board == 'half_board' else None">Half Board</option>
                                                    <option value="full_board" t-att-selected="'selected' if board == 'full_board' else None">Full Board</option>
                                                    <option value="all_inclusive" t-att-selected="'selected' if board == 'all_inclusive' else None">All Inclusive</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Supplier Selection -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="supplier_id">Preferred Supplier</label>
                                                <select name="supplier_id" class="form-control">
                                                    <option value="">Select Supplier (Optional)</option>
                                                    <t t-foreach="suppliers" t-as="supplier">
                                                        <option t-att-value="supplier.id" t-att-selected="'selected' if str(supplier.id) == str(supplier_id or '') else None">
                                                            <t t-esc="supplier.name"/>
                                                        </option>
                                                    </t>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Special Requests -->
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <label for="special_request">Special Requests</label>
                                                <textarea name="special_request" class="form-control" rows="3"
                                                         placeholder="Any special requests (late check-in, room preferences, etc.)" t-raw="special_request or ''"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Customer Notes -->
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <label for="customer_notes">Additional Notes</label>
                                                <textarea name="customer_notes" class="form-control" rows="3"
                                                         placeholder="Any additional information or requests" t-raw="customer_notes or ''"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Submit Button -->
                                    <div class="row">
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-check"/> Submit Hotel Booking Request
                                            </button>
                                            <a href="/my/hotel_booking" class="btn btn-secondary ml-2">
                                                <i class="fa fa-times"/> Cancel
                                            </a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- ===== MEDICAL INSURANCE REQUEST PORTAL TEMPLATES ===== -->

        <!-- Medical Insurance Requests List Template -->
        <template id="portal_my_medical_insurance" name="My Medical Insurance">
            <t t-call="portal.portal_layout">
                <t t-set="breadcrumbs_searchbar" t-value="True"/>

                <t t-call="portal.portal_searchbar">
                    <t t-set="title">Medical Insurance</t>
                </t>

                <t t-if="not insurance_requests">
                    <div class="alert alert-warning mt-3" role="alert">
                        <strong>No medical insurance requests found.</strong>
                        <p class="mb-0">
                            <a href="/my/medical_insurance/new" class="btn btn-primary">
                                <i class="fa fa-plus"/> Create New Medical Insurance Request
                            </a>
                        </p>
                    </div>
                </t>
                <t t-if="insurance_requests" t-call="portal.portal_table">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>Medical Insurance Requests</h3>
                        <a href="/my/medical_insurance/new" class="btn btn-primary">
                            <i class="fa fa-plus"/> New Medical Insurance
                        </a>
                    </div>

                    <thead>
                        <tr class="active">
                            <th>Reference</th>
                            <th>Country</th>
                            <th>Nationality</th>
                            <th>Arrival Date</th>
                            <th>Departure Date</th>
                            <th>Status</th>
                            <th>Supplier</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="insurance_requests" t-as="insurance">
                            <tr>
                                <td>
                                    <a t-att-href="'/my/medical_insurance/%s' % insurance.id">
                                        <t t-esc="insurance.name"/>
                                    </a>
                                </td>
                                <td><t t-esc="insurance.travel_country"/></td>
                                <td><t t-esc="insurance.nationality"/></td>
                                <td><span t-field="insurance.arrive_date"/></td>
                                <td><span t-field="insurance.departure_date"/></td>
                                <td>
                                    <span t-att-class="'badge badge-%s' % ('success' if insurance.state == 'confirmed' else 'info' if insurance.state == 'draft' else 'secondary')">
                                        <t t-esc="insurance.state.title()"/>
                                    </span>
                                </td>
                                <td><t t-esc="insurance.supplier_name"/></td>
                            </tr>
                        </t>
                    </tbody>
                </t>
            </t>
        </template>

        <!-- Medical Insurance Detail Template -->
        <template id="portal_medical_insurance_detail" name="Medical Insurance Detail">
            <t t-call="portal.portal_layout">
                <t t-set="o_portal_fullwidth_alert" t-value="True"/>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">
                                    <i class="fa fa-medkit text-primary"/> Medical Insurance Request
                                    <small class="text-muted">- <t t-esc="insurance.name"/></small>
                                </h4>
                                <span t-att-class="'badge badge-lg badge-%s' % ('success' if insurance.state == 'confirmed' else 'info' if insurance.state == 'draft' else 'secondary')">
                                    <t t-esc="insurance.state.title()"/>
                                </span>
                            </div>
                            <div class="card-body">
                                <!-- Success Message -->
                                <t t-if="success_message">
                                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                                        <t t-esc="success_message"/>
                                        <button type="button" class="close" data-dismiss="alert">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                    </div>
                                </t>

                                <!-- Travel Information -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <h5>Travel Information</h5>
                                        <p><strong>Destination Country:</strong> <span t-field="insurance.travel_country"/></p>
                                        <p><strong>Nationality:</strong> <span t-field="insurance.nationality"/></p>
                                        <p><strong>Arrival Date:</strong> <span t-field="insurance.arrive_date"/></p>
                                        <p><strong>Departure Date:</strong> <span t-field="insurance.departure_date"/></p>
                                        <p><strong>Duration:</strong> <span t-field="insurance.duration"/> days</p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Personal Information</h5>
                                        <p><strong>Passport Number:</strong> <span t-field="insurance.passport_no"/></p>
                                        <p><strong>Contact Phone:</strong> <span t-field="insurance.tel_no"/></p>
                                        <t t-if="insurance.address">
                                            <p><strong>Address:</strong> <span t-field="insurance.address"/></p>
                                        </t>
                                    </div>
                                </div>

                                <!-- Insurance Details -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <h5>Insurance Details</h5>
                                        <t t-if="insurance.insurance_type">
                                            <p><strong>Insurance Type:</strong> <span t-field="insurance.insurance_type"/></p>
                                        </t>
                                        <t t-if="insurance.coverage_amount">
                                            <p><strong>Coverage Amount:</strong> <span t-field="insurance.coverage_amount" t-options="{'widget': 'monetary'}"/></p>
                                        </t>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Supplier</h5>
                                        <p><strong>Insurance Provider:</strong> <span t-esc="insurance.supplier_name"/></p>
                                    </div>
                                </div>

                                <!-- Special Conditions -->
                                <t t-if="insurance.special_conditions">
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <h5>Special Conditions</h5>
                                            <p t-field="insurance.special_conditions"/>
                                        </div>
                                    </div>
                                </t>

                                <!-- Customer Notes -->
                                <t t-if="insurance.customer_notes">
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <h5>Customer Notes</h5>
                                            <p t-field="insurance.customer_notes"/>
                                        </div>
                                    </div>
                                </t>

                                <!-- Pricing Information -->
                                <t t-if="insurance.customer_price">
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <h5>Pricing</h5>
                                            <p><strong>Total Price:</strong> <span t-field="insurance.customer_price" t-options="{'widget': 'monetary'}"/></p>
                                        </div>
                                    </div>
                                </t>

                                <!-- Action Buttons -->
                                <div class="row">
                                    <div class="col-12">
                                        <a href="/my/medical_insurance" class="btn btn-secondary">
                                            <i class="fa fa-arrow-left"/> Back to Medical Insurance
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

        <!-- Medical Insurance Creation Form Template -->
        <template id="portal_medical_insurance_new" name="New Medical Insurance">
            <t t-call="portal.portal_layout">
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="mb-0">
                                    <i class="fa fa-medkit text-primary"/> Create New Medical Insurance Request
                                </h4>
                            </div>
                            <div class="card-body">
                                <!-- Error Message -->
                                <t t-if="error_message">
                                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                        <t t-esc="error_message"/>
                                        <button type="button" class="close" data-dismiss="alert">
                                            <span aria-hidden="true">×</span>
                                        </button>
                                    </div>
                                </t>

                                <form action="/my/medical_insurance/create" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                    <!-- Travel Information -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="travel_country">Destination Country *</label>
                                                <input type="text" name="travel_country" class="form-control" required="required"
                                                       placeholder="Country you are traveling to" t-att-value="travel_country or ''"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="nationality">Nationality *</label>
                                                <input type="text" name="nationality" class="form-control" required="required"
                                                       placeholder="Your nationality" t-att-value="nationality or ''"/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Personal Information -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="passport_no">Passport Number *</label>
                                                <input type="text" name="passport_no" class="form-control" required="required"
                                                       placeholder="Your passport number" t-att-value="passport_no or ''"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="tel_no">Contact Phone *</label>
                                                <input type="tel" name="tel_no" class="form-control" required="required"
                                                       placeholder="Your contact phone number" t-att-value="tel_no or ''"/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Travel Dates -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="arrive_date">Arrival Date *</label>
                                                <input type="date" name="arrive_date" class="form-control" required="required"
                                                       t-att-value="arrive_date or ''"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="departure_date">Departure Date *</label>
                                                <input type="date" name="departure_date" class="form-control" required="required"
                                                       t-att-value="departure_date or ''"/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Insurance Details -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="insurance_type">Insurance Type</label>
                                                <select name="insurance_type" class="form-control">
                                                    <option value="">Select Insurance Type</option>
                                                    <option value="basic" t-att-selected="'selected' if insurance_type == 'basic' else None">Basic Coverage</option>
                                                    <option value="comprehensive" t-att-selected="'selected' if insurance_type == 'comprehensive' else None">Comprehensive Coverage</option>
                                                    <option value="premium" t-att-selected="'selected' if insurance_type == 'premium' else None">Premium Coverage</option>
                                                    <option value="family" t-att-selected="'selected' if insurance_type == 'family' else None">Family Plan</option>
                                                    <option value="senior" t-att-selected="'selected' if insurance_type == 'senior' else None">Senior Plan</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="coverage_amount">Coverage Amount</label>
                                                <input type="number" name="coverage_amount" class="form-control" step="0.01"
                                                       placeholder="Desired coverage amount" t-att-value="coverage_amount or ''"/>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Supplier Selection -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="supplier_id">Preferred Supplier</label>
                                                <select name="supplier_id" class="form-control">
                                                    <option value="">Select Supplier (Optional)</option>
                                                    <t t-foreach="suppliers" t-as="supplier">
                                                        <option t-att-value="supplier.id" t-att-selected="'selected' if str(supplier.id) == str(supplier_id or '') else None">
                                                            <t t-esc="supplier.name"/>
                                                        </option>
                                                    </t>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Address -->
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <label for="address">Address</label>
                                                <textarea name="address" class="form-control" rows="2"
                                                         placeholder="Your address" t-raw="address or ''"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Special Conditions -->
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <label for="special_conditions">Special Conditions</label>
                                                <textarea name="special_conditions" class="form-control" rows="3"
                                                         placeholder="Any special medical conditions or requirements" t-raw="special_conditions or ''"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Customer Notes -->
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <div class="form-group">
                                                <label for="customer_notes">Additional Notes</label>
                                                <textarea name="customer_notes" class="form-control" rows="3"
                                                         placeholder="Any additional information or requests" t-raw="customer_notes or ''"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Submit Button -->
                                    <div class="row">
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-check"/> Submit Medical Insurance Request
                                            </button>
                                            <a href="/my/medical_insurance" class="btn btn-secondary ml-2">
                                                <i class="fa fa-times"/> Cancel
                                            </a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </template>

    </data>
</odoo>
