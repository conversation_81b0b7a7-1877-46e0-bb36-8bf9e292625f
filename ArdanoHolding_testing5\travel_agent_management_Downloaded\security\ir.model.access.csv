id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_travel_ticket_request_user,travel.ticket.request.user,model_travel_ticket_request,group_travel_agent_user,1,1,1,0
access_travel_ticket_request_manager,travel.ticket.request.manager,model_travel_ticket_request,group_travel_agent_manager,1,1,1,1
access_travel_ticket_request_admin,travel.ticket.request.admin,model_travel_ticket_request,group_travel_agent_admin,1,1,1,1
access_travel_ticket_request_accounting,travel.ticket.request.accounting,model_travel_ticket_request,group_travel_agent_accounting,1,0,0,0
access_travel_passenger_user,travel.passenger.user,model_travel_passenger,group_travel_agent_user,1,1,1,0
access_travel_passenger_manager,travel.passenger.manager,model_travel_passenger,group_travel_agent_manager,1,1,1,1
access_travel_passenger_admin,travel.passenger.admin,model_travel_passenger,group_travel_agent_admin,1,1,1,1
access_travel_passenger_accounting,travel.passenger.accounting,model_travel_passenger,group_travel_agent_accounting,1,0,0,0
access_travel_passenger_line_user,travel.passenger.line.user,model_travel_passenger_line,group_travel_agent_user,1,1,1,1
access_travel_passenger_line_manager,travel.passenger.line.manager,model_travel_passenger_line,group_travel_agent_manager,1,1,1,1
access_travel_passenger_line_admin,travel.passenger.line.admin,model_travel_passenger_line,group_travel_agent_admin,1,1,1,1
access_travel_passenger_line_accounting,travel.passenger.line.accounting,model_travel_passenger_line,group_travel_agent_accounting,1,0,0,0
access_travel_ticket_request_portal,travel.ticket.request.portal,model_travel_ticket_request,base.group_portal,1,0,1,0
access_travel_passenger_portal,travel.passenger.portal,model_travel_passenger,base.group_portal,1,0,1,0
access_travel_passenger_line_portal,travel.passenger.line.portal,model_travel_passenger_line,base.group_portal,1,0,1,0
access_travel_flight_segment_user,travel.flight.segment.user,model_travel_flight_segment,group_travel_agent_user,1,1,1,1
access_travel_flight_segment_manager,travel.flight.segment.manager,model_travel_flight_segment,group_travel_agent_manager,1,1,1,1
access_travel_flight_segment_admin,travel.flight.segment.admin,model_travel_flight_segment,group_travel_agent_admin,1,1,1,1
access_travel_flight_segment_accounting,travel.flight.segment.accounting,model_travel_flight_segment,group_travel_agent_accounting,1,0,0,0
access_travel_flight_segment_portal,travel.flight.segment.portal,model_travel_flight_segment,base.group_portal,1,0,1,0
access_travel_ticket_user,travel.ticket.user,model_travel_ticket,group_travel_agent_user,1,1,1,1
access_travel_ticket_manager,travel.ticket.manager,model_travel_ticket,group_travel_agent_manager,1,1,1,1
access_travel_ticket_admin,travel.ticket.admin,model_travel_ticket,group_travel_agent_admin,1,1,1,1
access_travel_ticket_accounting,travel.ticket.accounting,model_travel_ticket,group_travel_agent_accounting,1,0,0,0
access_travel_ticket_portal,travel.ticket.portal,model_travel_ticket,base.group_portal,1,0,1,0
access_visa_country_user,visa.country.user,model_visa_country,group_travel_agent_user,1,1,1,0
access_visa_country_manager,visa.country.manager,model_visa_country,group_travel_agent_manager,1,1,1,1
access_visa_country_admin,visa.country.admin,model_visa_country,group_travel_agent_admin,1,1,1,1
access_visa_document_type_user,visa.document.type.user,model_visa_document_type,group_travel_agent_user,1,1,1,0
access_visa_document_type_manager,visa.document.type.manager,model_visa_document_type,group_travel_agent_manager,1,1,1,1
access_visa_document_type_admin,visa.document.type.admin,model_visa_document_type,group_travel_agent_admin,1,1,1,1
access_visa_field_type_user,visa.field.type.user,model_visa_field_type,group_travel_agent_user,1,1,1,0
access_visa_field_type_manager,visa.field.type.manager,model_visa_field_type,group_travel_agent_manager,1,1,1,1
access_visa_field_type_admin,visa.field.type.admin,model_visa_field_type,group_travel_agent_admin,1,1,1,1
access_visa_type_user,visa.type.user,model_visa_type,group_travel_agent_user,1,1,1,0
access_visa_type_manager,visa.type.manager,model_visa_type,group_travel_agent_manager,1,1,1,1
access_visa_type_admin,visa.type.admin,model_visa_type,group_travel_agent_admin,1,1,1,1
access_visa_request_user,visa.request.user,model_visa_request,group_travel_agent_user,1,1,1,0
access_visa_request_manager,visa.request.manager,model_visa_request,group_travel_agent_manager,1,1,1,1
access_visa_request_admin,visa.request.admin,model_visa_request,group_travel_agent_admin,1,1,1,1
access_visa_request_document_user,visa.request.document.user,model_visa_request_document,group_travel_agent_user,1,1,1,1
access_visa_request_document_manager,visa.request.document.manager,model_visa_request_document,group_travel_agent_manager,1,1,1,1
access_visa_request_document_admin,visa.request.document.admin,model_visa_request_document,group_travel_agent_admin,1,1,1,1
access_visa_request_field_user,visa.request.field.user,model_visa_request_field,group_travel_agent_user,1,1,1,1
access_visa_request_field_manager,visa.request.field.manager,model_visa_request_field,group_travel_agent_manager,1,1,1,1
access_visa_request_field_admin,visa.request.field.admin,model_visa_request_field,group_travel_agent_admin,1,1,1,1
access_visa_country_portal,visa.country.portal,model_visa_country,base.group_portal,1,0,0,0
access_visa_document_type_portal,visa.document.type.portal,model_visa_document_type,base.group_portal,1,0,0,0
access_visa_field_type_portal,visa.field.type.portal,model_visa_field_type,base.group_portal,1,0,0,0
access_visa_type_portal,visa.type.portal,model_visa_type,base.group_portal,1,0,0,0
access_visa_request_portal,visa.request.portal,model_visa_request,base.group_portal,1,1,1,0
access_visa_request_document_portal,visa.request.document.portal,model_visa_request_document,base.group_portal,1,1,1,1
access_visa_request_field_portal,visa.request.field.portal,model_visa_request_field,base.group_portal,1,1,1,1
access_transfer_request_user,transfer.request.user,model_transfer_request,group_travel_agent_user,1,1,1,0
access_transfer_request_manager,transfer.request.manager,model_transfer_request,group_travel_agent_manager,1,1,1,1
access_transfer_request_admin,transfer.request.admin,model_transfer_request,group_travel_agent_admin,1,1,1,1
access_transfer_request_accounting,transfer.request.accounting,model_transfer_request,group_travel_agent_accounting,1,0,0,0
access_transfer_request_portal,transfer.request.portal,model_transfer_request,base.group_portal,1,1,1,0
access_hotel_booking_request_user,hotel.booking.request.user,model_hotel_booking_request,group_travel_agent_user,1,1,1,0
access_hotel_booking_request_manager,hotel.booking.request.manager,model_hotel_booking_request,group_travel_agent_manager,1,1,1,1
access_hotel_booking_request_admin,hotel.booking.request.admin,model_hotel_booking_request,group_travel_agent_admin,1,1,1,1
access_hotel_booking_request_accounting,hotel.booking.request.accounting,model_hotel_booking_request,group_travel_agent_accounting,1,0,0,0
access_hotel_booking_request_portal,hotel.booking.request.portal,model_hotel_booking_request,base.group_portal,1,1,1,0
access_medical_insurance_request_user,medical.insurance.request.user,model_medical_insurance_request,group_travel_agent_user,1,1,1,0
access_medical_insurance_request_manager,medical.insurance.request.manager,model_medical_insurance_request,group_travel_agent_manager,1,1,1,1
access_medical_insurance_request_admin,medical.insurance.request.admin,model_medical_insurance_request,group_travel_agent_admin,1,1,1,1
access_medical_insurance_request_accounting,medical.insurance.request.accounting,model_medical_insurance_request,group_travel_agent_accounting,1,0,0,0
access_medical_insurance_request_portal,medical.insurance.request.portal,model_medical_insurance_request,base.group_portal,1,1,1,0