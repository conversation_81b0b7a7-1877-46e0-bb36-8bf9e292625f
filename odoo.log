2025-09-13 11:10:47,496 84476 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 11:10:47] "GET /web?db=aradno_travel HTTP/1.1" 302 - 1 0.010 0.065
2025-09-13 11:10:47,744 84476 INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-13 11:10:47,760 84476 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-09-13 11:10:47,764 84476 INFO ? odoo.modules.loading: loading 153 modules... 
2025-09-13 11:10:47,825 84476 INFO ? odoo.modules.loading: Loading module travel_agent_management (147/153) 
2025-09-13 11:10:48,023 84476 WARNING ? odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:10:48,023 84476 WARNING ? odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:10:48,023 84476 WARNING ? odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:10:48,023 84476 WARNING ? odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:10:48,023 84476 WARNING ? odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:10:48,023 84476 WARNING ? odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:10:48,048 84476 INFO ? odoo.modules.registry: module travel_agent_management: creating or updating database tables 
2025-09-13 11:10:48,197 84476 WARNING ? odoo.addons.base.models.ir_model: Two fields (departure_date, travel_date) of travel.ticket() have the same label: Departure Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:10:48,197 84476 WARNING ? odoo.addons.base.models.ir_model: Two fields (return_date, return_travel_date) of travel.ticket() have the same label: Return Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:10:48,197 84476 WARNING ? odoo.addons.base.models.ir_model: Two fields (field_type, field_type_id) of visa.request.field() have the same label: Field Type. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:10:48,337 84476 INFO ? odoo.modules.loading: loading travel_agent_management/security/security_groups.xml 
2025-09-13 11:10:48,643 84476 INFO ? odoo.modules.loading: loading travel_agent_management/security/ir.model.access.csv 
2025-09-13 11:10:48,744 84476 INFO ? odoo.modules.loading: loading travel_agent_management/security/portal_security.xml 
2025-09-13 11:10:48,846 84476 INFO ? odoo.modules.loading: loading travel_agent_management/data/sequences.xml 
2025-09-13 11:10:48,854 84476 INFO ? odoo.modules.loading: loading travel_agent_management/data/visa_sequences.xml 
2025-09-13 11:10:48,858 84476 INFO ? odoo.modules.loading: loading travel_agent_management/data/transfer_sequence.xml 
2025-09-13 11:10:48,858 84476 INFO ? odoo.modules.loading: loading travel_agent_management/data/hotel_sequence.xml 
2025-09-13 11:10:48,858 84476 INFO ? odoo.modules.loading: loading travel_agent_management/data/insurance_sequence.xml 
2025-09-13 11:10:48,858 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/ticket_request_views.xml 
2025-09-13 11:10:48,944 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/passenger_views.xml 
2025-09-13 11:10:49,000 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/passenger_line_views.xml 
2025-09-13 11:10:49,025 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/flight_segment_views.xml 
2025-09-13 11:10:49,077 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/travel_ticket_views.xml 
2025-09-13 11:10:49,125 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/menu_views.xml 
2025-09-13 11:10:49,180 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_country_views.xml 
2025-09-13 11:10:49,210 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-globe fa-2x) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\visa_country_views.xml',
 'line': 12,
 'name': 'visa.country.kanban',
 'view': ir.ui.view(2509,),
 'view.model': 'visa.country',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_visa_country_kanban'} 
2025-09-13 11:10:49,237 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_document_type_views.xml 
2025-09-13 11:10:49,277 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_field_type_views.xml 
2025-09-13 11:10:49,322 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_type_views.xml 
2025-09-13 11:10:49,382 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_request_views.xml 
2025-09-13 11:10:49,478 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_menu_views.xml 
2025-09-13 11:10:49,542 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/transfer_request_views.xml 
2025-09-13 11:10:49,604 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-route) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 36,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:10:49,604 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 39,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:10:49,604 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 40,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:10:49,604 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 43,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:10:49,628 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/hotel_booking_views.xml 
2025-09-13 11:10:49,696 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-building) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 37,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:10:49,696 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 40,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:10:49,696 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-moon-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 43,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:10:49,696 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 44,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:10:49,725 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/insurance_views.xml 
2025-09-13 11:10:49,800 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-shield) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 37,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:10:49,800 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 40,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:10:49,800 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 43,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:10:49,800 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-tag) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 45,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:10:49,825 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/portal_templates.xml 
2025-09-13 11:10:50,578 84476 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-09-13 11:10:50,578 84476 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'aradno_travel'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 305, in load_module_graph
    module.write({'state': 'installed', 'latest_version': ver})
  File "C:\odoo16\server\odoo\addons\website\models\ir_module_module.py", line 78, in write
    if request and request.db and request.context.get('apply_new_theme'):
                                  ^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1291, in context
    return self.env.context
           ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'context'
2025-09-13 11:10:50,594 84476 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'aradno_travel'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 305, in load_module_graph
    module.write({'state': 'installed', 'latest_version': ver})
  File "C:\odoo16\server\odoo\addons\website\models\ir_module_module.py", line 78, in write
    if request and request.db and request.context.get('apply_new_theme'):
                                  ^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1291, in context
    return self.env.context
           ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'context'
2025-09-13 11:10:50,595 84476 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 11:10:50] "GET /web?db=aradno_travel HTTP/1.1" 500 - 1489 1.327 1.539
2025-09-13 11:11:44,479 84476 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 11:11:44] "GET /web?db=aradno_travel HTTP/1.1" 302 - 1 0.010 0.058
2025-09-13 11:11:44,510 84476 INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-13 11:11:44,517 84476 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-13 11:11:44,530 84476 INFO ? odoo.modules.loading: loading 153 modules... 
2025-09-13 11:11:44,559 84476 INFO ? odoo.modules.loading: Loading module travel_agent_management (147/153) 
2025-09-13 11:11:44,845 84476 WARNING ? odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:11:44,845 84476 WARNING ? odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:11:44,845 84476 WARNING ? odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:11:44,845 84476 WARNING ? odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:11:44,845 84476 WARNING ? odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:11:44,845 84476 WARNING ? odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:11:44,874 84476 INFO ? odoo.modules.registry: module travel_agent_management: creating or updating database tables 
2025-09-13 11:11:45,078 84476 WARNING ? odoo.addons.base.models.ir_model: Two fields (departure_date, travel_date) of travel.ticket() have the same label: Departure Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:11:45,078 84476 WARNING ? odoo.addons.base.models.ir_model: Two fields (return_date, return_travel_date) of travel.ticket() have the same label: Return Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:11:45,078 84476 WARNING ? odoo.addons.base.models.ir_model: Two fields (field_type, field_type_id) of visa.request.field() have the same label: Field Type. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:11:45,226 84476 INFO ? odoo.modules.loading: loading travel_agent_management/security/security_groups.xml 
2025-09-13 11:11:45,535 84476 INFO ? odoo.modules.loading: loading travel_agent_management/security/ir.model.access.csv 
2025-09-13 11:11:45,688 84476 INFO ? odoo.modules.loading: loading travel_agent_management/security/portal_security.xml 
2025-09-13 11:11:45,825 84476 INFO ? odoo.modules.loading: loading travel_agent_management/data/sequences.xml 
2025-09-13 11:11:45,833 84476 INFO ? odoo.modules.loading: loading travel_agent_management/data/visa_sequences.xml 
2025-09-13 11:11:45,837 84476 INFO ? odoo.modules.loading: loading travel_agent_management/data/transfer_sequence.xml 
2025-09-13 11:11:45,842 84476 INFO ? odoo.modules.loading: loading travel_agent_management/data/hotel_sequence.xml 
2025-09-13 11:11:45,850 84476 INFO ? odoo.modules.loading: loading travel_agent_management/data/insurance_sequence.xml 
2025-09-13 11:11:45,850 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/ticket_request_views.xml 
2025-09-13 11:11:45,944 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/passenger_views.xml 
2025-09-13 11:11:46,004 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/passenger_line_views.xml 
2025-09-13 11:11:46,037 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/flight_segment_views.xml 
2025-09-13 11:11:46,093 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/travel_ticket_views.xml 
2025-09-13 11:11:46,145 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/menu_views.xml 
2025-09-13 11:11:46,192 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_country_views.xml 
2025-09-13 11:11:46,228 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-globe fa-2x) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\visa_country_views.xml',
 'line': 12,
 'name': 'visa.country.kanban',
 'view': ir.ui.view(2509,),
 'view.model': 'visa.country',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_visa_country_kanban'} 
2025-09-13 11:11:46,254 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_document_type_views.xml 
2025-09-13 11:11:46,302 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_field_type_views.xml 
2025-09-13 11:11:46,362 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_type_views.xml 
2025-09-13 11:11:46,429 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_request_views.xml 
2025-09-13 11:11:46,525 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_menu_views.xml 
2025-09-13 11:11:46,601 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/transfer_request_views.xml 
2025-09-13 11:11:46,677 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-route) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 36,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:11:46,677 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 39,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:11:46,677 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 40,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:11:46,677 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 43,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:11:46,715 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/hotel_booking_views.xml 
2025-09-13 11:11:46,782 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-building) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 37,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:11:46,782 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 40,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:11:46,787 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-moon-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 43,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:11:46,787 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 44,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:11:46,808 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/insurance_views.xml 
2025-09-13 11:11:46,876 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-shield) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 37,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:11:46,878 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 40,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:11:46,878 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 43,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:11:46,878 84476 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-tag) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 45,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:11:46,894 84476 INFO ? odoo.modules.loading: loading travel_agent_management/views/portal_templates.xml 
2025-09-13 11:11:47,710 84476 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-09-13 11:11:47,710 84476 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'aradno_travel'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 305, in load_module_graph
    module.write({'state': 'installed', 'latest_version': ver})
  File "C:\odoo16\server\odoo\addons\website\models\ir_module_module.py", line 78, in write
    if request and request.db and request.context.get('apply_new_theme'):
                                  ^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1291, in context
    return self.env.context
           ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'context'
2025-09-13 11:11:47,725 84476 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'aradno_travel'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 305, in load_module_graph
    module.write({'state': 'installed', 'latest_version': ver})
  File "C:\odoo16\server\odoo\addons\website\models\ir_module_module.py", line 78, in write
    if request and request.db and request.context.get('apply_new_theme'):
                                  ^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1291, in context
    return self.env.context
           ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'context'
2025-09-13 11:11:47,733 84476 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 11:11:47] "GET /web?db=aradno_travel HTTP/1.1" 500 - 1489 1.573 1.675
