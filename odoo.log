2025-09-13 10:47:02,121 80756 INFO ? odoo: Odoo version 16.0-20250210 
2025-09-13 10:47:02,121 80756 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-09-13 10:47:02,121 80756 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\ardanoholding_testing5'] 
2025-09-13 10:47:02,121 80756 INFO ? odoo: database: openpg@localhost:5432 
2025-09-13 10:47:02,257 80756 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-09-13 10:47:03,779 80756 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8016 
2025-09-13 10:47:04,593 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_accounts_chart', defaulting to LGPL-3 
2025-09-13 10:47:04,593 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_approval', defaulting to LGPL-3 
2025-09-13 10:47:04,593 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_bank', defaulting to LGPL-3 
2025-09-13 10:47:04,595 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_contract_bonus', defaulting to LGPL-3 
2025-09-13 10:47:04,595 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_hr_customization', defaulting to LGPL-3 
2025-09-13 10:47:04,595 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_user_login_history', defaulting to LGPL-3 
2025-09-13 10:47:04,598 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_accounting_reports', defaulting to LGPL-3 
2025-09-13 10:47:04,598 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_accounting_reports_old', defaulting to LGPL-3 
2025-09-13 10:47:04,598 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_attendance', defaulting to LGPL-3 
2025-09-13 10:47:04,600 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_biotime', defaulting to LGPL-3 
2025-09-13 10:47:04,600 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_biotime_works', defaulting to LGPL-3 
2025-09-13 10:47:04,600 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_inventory', defaulting to LGPL-3 
2025-09-13 10:47:04,600 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_product_custom', defaulting to LGPL-3 
2025-09-13 10:47:04,602 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_sale_order_analytic_account', defaulting to LGPL-3 
2025-09-13 10:47:04,602 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'date_field_no_translation', defaulting to LGPL-3 
2025-09-13 10:47:04,605 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'employee_report_kpi', defaulting to LGPL-3 
2025-09-13 10:47:04,605 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'manufact_customize', defaulting to LGPL-3 
2025-09-13 10:47:04,608 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'project_location', defaulting to LGPL-3 
2025-09-13 10:47:04,613 80756 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'service_provider', defaulting to LGPL-3 
2025-09-13 10:47:14,579 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=fania_1 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,588 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi18 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,588 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_7 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,593 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_4 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,596 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_5 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,597 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_6 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,600 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,600 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_ERP host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,606 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_benghazi host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,606 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_1 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,608 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_2 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,608 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_main host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,615 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptous_1 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,615 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_tesing host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,619 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_testing host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,621 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=linda host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,623 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=martinlion_2 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,626 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=matrinlive host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,628 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moaslive_6 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,628 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,628 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass2025 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,635 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass3 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,637 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=41/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass5 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,641 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=40/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass6 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,642 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=39/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moassLive25_2 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,646 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=38/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,646 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=37/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_2 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,648 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=36/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,653 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=35/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_7 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,655 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=34/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_latest host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,657 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=33/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,659 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=32/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,661 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=31/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18_cubes host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,664 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=30/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_1 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,665 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=29/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_2 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,667 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=28/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,670 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=27/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18live host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,672 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=26/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=t18_8 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,675 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=25/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=taibat host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,675 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=24/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,677 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=23/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,679 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=22/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test18 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,681 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=21/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test1818 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,683 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=20/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,687 80756 INFO ? odoo.sql_db: ConnectionPool(used=2/count=19/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_pos_18 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:47:14,805 80756 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 10:47:14] "GET /web/database/selector HTTP/1.1" 200 - 235 2.679 8.287
2025-09-13 10:47:22,018 80756 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 10:47:22] "GET /web?db=ardano_live7 HTTP/1.1" 302 - 1 0.035 0.083
2025-09-13 10:47:22,124 80756 INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-13 10:47:22,140 80756 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-09-13 10:47:22,178 80756 INFO ? odoo.modules.loading: loading 215 modules... 
2025-09-13 10:47:22,223 80756 WARNING ? odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-09-13 10:47:24,310 80756 WARNING ? odoo.api.create: The model odoo.addons.cubes_attendance.models.models is not overriding the create method in batch 
2025-09-13 10:47:26,585 80756 WARNING ? odoo.models: The model hr.employee.certificate has no _description 
2025-09-13 10:47:26,585 80756 WARNING ? odoo.models: The model job.category has no _description 
2025-09-13 10:47:26,590 80756 WARNING ? odoo.models: The model contract.classification has no _description 
2025-09-13 10:47:26,590 80756 WARNING ? odoo.models: The model hr.bounce has no _description 
2025-09-13 10:47:26,590 80756 WARNING ? odoo.models: The model bounce.line has no _description 
2025-09-13 10:47:26,591 80756 WARNING ? odoo.models: The model hr.ardano_department has no _description 
2025-09-13 10:47:26,592 80756 WARNING ? odoo.models: The model hr.team has no _description 
2025-09-13 10:47:26,592 80756 WARNING ? odoo.models: The model hr.unit has no _description 
2025-09-13 10:47:26,609 80756 WARNING ? odoo.models: The model analytic.wizard.report has no _description 
2025-09-13 10:47:27,499 80756 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.ticket_request is not overriding the create method in batch 
2025-09-13 10:47:27,716 80756 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.travel_ticket is not overriding the create method in batch 
2025-09-13 10:47:27,725 80756 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.visa_models is not overriding the create method in batch 
2025-09-13 10:47:27,727 80756 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.visa_models is not overriding the create method in batch 
2025-09-13 10:47:27,729 80756 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.transfer_models is not overriding the create method in batch 
2025-09-13 10:47:27,734 80756 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.hotel_models is not overriding the create method in batch 
2025-09-13 10:47:27,739 80756 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.insurance_models is not overriding the create method in batch 
2025-09-13 10:47:27,974 80756 WARNING ? odoo.api.create: The model odoo.addons.project_location.models.task_points is not overriding the create method in batch 
2025-09-13 10:47:27,994 80756 WARNING ? odoo.api.create: The model odoo.addons.project_location.models.project_create is not overriding the create method in batch 
2025-09-13 10:47:28,010 80756 WARNING ? odoo.api.create: The model odoo.addons.project_location.models.approval_request is not overriding the create method in batch 
2025-09-13 10:47:28,041 80756 WARNING ? odoo.api.create: The model odoo.addons.project_location.models.account_account is not overriding the create method in batch 
2025-09-13 10:47:28,099 80756 WARNING ? odoo.models: The model report.project_location.closing_report_template has no _description 
2025-09-13 10:47:28,099 80756 WARNING ? odoo.models: The model work.measurement has no _description 
2025-09-13 10:47:28,099 80756 WARNING ? odoo.models: The model project.work.description has no _description 
2025-09-13 10:47:28,099 80756 WARNING ? odoo.models: The model project_point.description has no _description 
2025-09-13 10:47:28,099 80756 WARNING ? odoo.models: The model task.signers has no _description 
2025-09-13 10:47:28,099 80756 WARNING ? odoo.models: The model task.material has no _description 
2025-09-13 10:47:28,099 80756 WARNING ? odoo.models: The model task.purchases has no _description 
2025-09-13 10:47:28,099 80756 WARNING ? odoo.models: The model task.operations has no _description 
2025-09-13 10:47:28,099 80756 WARNING ? odoo.models: The model request.discount has no _description 
2025-09-13 10:47:28,105 80756 WARNING ? odoo.models: The model approval.signers has no _description 
2025-09-13 10:47:28,106 80756 WARNING ? odoo.models: The model move.signers has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model task.mat_purchase_wizard has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model task.purchases_wizard_line has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model project.closing_wizard has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model check.duplication_wizard has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model check.duplication_wizard_lines has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model approval.bill has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model approval.daily_bill_wizard_line has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model approval.daily_bill_wizard has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model project.canceling_wizard has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model project.measurement.import.wizard.line has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model project.measurement.import.wizard has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model project.measurement.import.sample.wizard has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model project.measurement.export.wizard has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model task.cancel_work_order.wizard has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model approval_request.cancel has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model project.report_wizard_line has no _description 
2025-09-13 10:47:28,107 80756 WARNING ? odoo.models: The model project.report_wizard has no _description 
2025-09-13 10:47:28,155 80756 INFO ? odoo.modules.loading: 215 modules loaded in 5.97s, 0 queries (+0 extra) 
2025-09-13 10:47:28,409 80756 WARNING ? odoo.fields: account.analytic.line.cubes_type: selection attribute will be ignored as the field is related 
2025-09-13 10:47:28,431 80756 WARNING ? odoo.fields: product.template.cubes_type: selection attribute will be ignored as the field is related 
2025-09-13 10:47:28,488 80756 WARNING ? odoo.fields: project.project.stage_status: selection attribute will be ignored as the field is related 
2025-09-13 10:47:28,498 80756 WARNING ? odoo.fields: project.task.contractor_status: selection attribute will be ignored as the field is related 
2025-09-13 10:47:28,604 80756 WARNING ? odoo.fields: Redundant default on product.template.cubes_type 
2025-09-13 10:47:28,604 80756 WARNING ? odoo.fields: Redundant default on account.analytic.line.cubes_type 
2025-09-13 10:47:28,613 80756 WARNING ? odoo.fields: Field approval.request.signer_ids: unknown parameter 'ondelete', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:47:28,661 80756 WARNING ? odoo.fields: Field job.category.job_category: unknown parameter 'Index', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:47:28,661 80756 WARNING ? odoo.fields: Field contract.classification.contract_name: unknown parameter 'Index', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:47:28,661 80756 WARNING ? odoo.fields: Field hr.bounce.amount_percentage: unknown parameter 'widget', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:47:28,670 80756 WARNING ? odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:47:28,670 80756 WARNING ? odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:47:28,672 80756 WARNING ? odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:47:28,672 80756 WARNING ? odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:47:28,672 80756 WARNING ? odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:47:28,672 80756 WARNING ? odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:47:28,821 80756 INFO ? odoo.modules.loading: Modules loaded. 
2025-09-13 10:47:28,845 80756 INFO ? odoo.modules.registry: Registry loaded in 6.813s 
2025-09-13 10:47:28,880 80756 INFO ardano_live7 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-09-13 10:47:29,988 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:47:29] "GET /web?db=ardano_live7 HTTP/1.1" 303 - 26 0.152 7.807
2025-09-13 10:47:30,409 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang.get_available at 0x0000023EFA3491C0>, (1,))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 3

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.lang(3,).flag_image_url'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 3

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.lang(3,).flag_image'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-09-13 10:47:30,506 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:47:30] "GET /web/login HTTP/1.1" 303 - 14 0.058 0.147
2025-09-13 10:47:31,139 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'ardano_bank', defaulting to LGPL-3 
2025-09-13 10:47:31,226 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'cubes_sale_order_analytic_account', defaulting to LGPL-3 
2025-09-13 10:47:31,226 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'employee_report_kpi', defaulting to LGPL-3 
2025-09-13 10:47:31,263 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'cubes_product_custom', defaulting to LGPL-3 
2025-09-13 10:47:31,280 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'cubes_attendance', defaulting to LGPL-3 
2025-09-13 10:47:31,280 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'project_location', defaulting to LGPL-3 
2025-09-13 10:47:31,355 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'ardano_hr_customization', defaulting to LGPL-3 
2025-09-13 10:47:31,388 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'ardano_approval', defaulting to LGPL-3 
2025-09-13 10:47:31,407 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'cubes_accounting_reports', defaulting to LGPL-3 
2025-09-13 10:47:31,439 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'service_provider', defaulting to LGPL-3 
2025-09-13 10:47:31,461 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'ardano_user_login_history', defaulting to LGPL-3 
2025-09-13 10:47:31,466 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'ardano_accounts_chart', defaulting to LGPL-3 
2025-09-13 10:47:31,474 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'manufact_customize', defaulting to LGPL-3 
2025-09-13 10:47:31,482 80756 WARNING ardano_live7 odoo.modules.module: Missing `license` key in manifest for 'date_field_no_translation', defaulting to LGPL-3 
2025-09-13 10:47:32,272 80756 INFO ardano_live7 odoo.addons.base.models.assetsbundle: Failed to find attachment for assets /web/assets/%-9424b3f/1/rtl/web.assets_frontend.min.css 
2025-09-13 10:47:32,976 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\eb/ebdd65114b0d4a1fe30dcde116520198fb1f32c5 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.qweb', <function IrQWeb._generate_asset_nodes_cache at 0x0000023EF7892CA0>, 'web.assets_frontend', True, False, '1', False, False, False, False, ('ar_001', None, None, None, None, None, None, None, 1))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1766

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1766,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\eb/ebdd65114b0d4a1fe30dcde116520198fb1f32c5'
2025-09-13 10:47:32,976 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\a6/a65d881bacea4fb18ba367db1f57e5799bceb426 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.qweb', <function IrQWeb._generate_asset_nodes_cache at 0x0000023EF7892CA0>, 'web.assets_frontend', True, False, '1', False, False, False, False, ('ar_001', None, None, None, None, None, None, None, 1))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1708

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1708,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\a6/a65d881bacea4fb18ba367db1f57e5799bceb426'
2025-09-13 10:47:47,624 80756 WARNING ardano_live7 odoo.addons.base.models.assetsbundle: You need https://rtlcss.com/ to convert css file to right to left compatiblity. Use: npm install -g rtlcss 
2025-09-13 10:47:48,312 80756 WARNING ardano_live7 py.warnings: C:\odoo16\server\odoo\fields.py:809: UserWarning: Field 'stock.picking.move_ids_without_package' in dependency of stock.picking.total_quantity should be searchable. This is necessary to determine which records to recompute when stock.move.quantity_done is modified. You should either make the field searchable, or simplify the field dependency.
  File "C:\Odoo16\python\Lib\threading.py", line 1030, in _bootstrap
    self._bootstrap_inner()
  File "C:\Odoo16\python\Lib\threading.py", line 1073, in _bootstrap_inner
    self.run()
  File "C:\Odoo16\python\Lib\threading.py", line 1010, in run
    self._target(*self._args, **self._kwargs)
  File "C:\Odoo16\python\Lib\socketserver.py", line 692, in process_request_thread
    self.finish_request(request, client_address)
  File "C:\Odoo16\python\Lib\socketserver.py", line 362, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "C:\Odoo16\python\Lib\socketserver.py", line 761, in __init__
    self.handle()
  File "C:\Odoo16\python\Lib\site-packages\werkzeug\serving.py", line 342, in handle
    BaseHTTPRequestHandler.handle(self)
  File "C:\Odoo16\python\Lib\http\server.py", line 436, in handle
    self.handle_one_request()
  File "C:\Odoo16\python\Lib\site-packages\werkzeug\serving.py", line 374, in handle_one_request
    self.run_wsgi()
  File "C:\Odoo16\python\Lib\site-packages\werkzeug\serving.py", line 319, in run_wsgi
    execute(self.server.app)
  File "C:\Odoo16\python\Lib\site-packages\werkzeug\serving.py", line 308, in execute
    application_iter = app(environ, start_response)
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 156, in _dispatch
    result.flatten()
  File "C:\odoo16\server\odoo\http.py", line 1182, in flatten
    self.response.append(self.render())
  File "C:\odoo16\server\odoo\http.py", line 1174, in render
    return request.env["ir.ui.view"]._render_template(self.template, self.qcontext)
  File "C:\odoo16\server\odoo\addons\website\models\ir_ui_view.py", line 419, in _render_template
    return super()._render_template(template, values=values)
  File "C:\odoo16\server\odoo\addons\base\models\ir_ui_view.py", line 2135, in _render_template
    return self.env['ir.qweb']._render(template, values)
  File "C:\odoo16\server\odoo\tools\profiler.py", line 294, in _tracked_method_render
    return method_render(self, template, values, **options)
  File "C:\odoo16\server\odoo\addons\base\models\ir_qweb.py", line 593, in _render
    result = ''.join(rendering)
  File "<185>", line 242, in template_185
  File "<185>", line 231, in template_185_content
  File "<184>", line 51, in template_184
  File "<184>", line 40, in template_184_content
  File "<2687>", line 1333, in template_2687
  File "<2687>", line 949, in template_2687_content
  File "C:\odoo16\server\odoo\addons\base\models\ir_qweb.py", line 2466, in _load_values
    return get_value()
  File "<2687>", line 939, in template_2687_t_cache_0_cache
  File "<2687>", line 426, in template_2687_t_cache_0
  File "C:\odoo16\server\odoo\addons\website\models\ir_qweb.py", line 122, in _get_asset_nodes
    return super(IrQWeb, self_website)._get_asset_nodes(bundle, css=css, js=js, debug=debug, async_load=async_load, defer_load=defer_load, lazy_load=lazy_load, media=media)
  File "C:\odoo16\server\odoo\addons\base\models\ir_qweb.py", line 2438, in _get_asset_nodes
    return self._generate_asset_nodes_cache(bundle, css, js, debug, async_load, defer_load, lazy_load, media)
  File "<decorator-gen-71>", line 2, in _generate_asset_nodes_cache
  File "C:\odoo16\server\odoo\tools\cache.py", line 90, in lookup
    value = d[key] = self.method(*args, **kwargs)
  File "C:\odoo16\server\odoo\addons\base\models\ir_qweb.py", line 2493, in _generate_asset_nodes_cache
    return self._generate_asset_nodes(bundle, css, js, debug, async_load, defer_load, lazy_load, media)
  File "C:\odoo16\server\odoo\addons\base\models\ir_qweb.py", line 2567, in _generate_asset_nodes
    return remains + asset.to_node(css=css, js=js, debug=debug, async_load=async_load, defer_load=defer_load, lazy_load=lazy_load)
  File "C:\odoo16\server\odoo\addons\base\models\assetsbundle.py", line 159, in to_node
    css_attachments = self.css(is_minified=not is_debug_assets) or []
  File "C:\odoo16\server\odoo\addons\base\models\assetsbundle.py", line 627, in css
    self.save_attachment(extension, css)
  File "C:\odoo16\server\odoo\addons\base\models\assetsbundle.py", line 373, in save_attachment
    attachment = ira.with_user(SUPERUSER_ID).create(values)
  File "<decorator-gen-254>", line 2, in create
  File "C:\odoo16\server\odoo\api.py", line 414, in _model_create_multi
    return create(self, [arg])
  File "C:\odoo16\server\odoo\addons\website\models\ir_attachment.py", line 23, in create
    return super().create(vals_list)
  File "<decorator-gen-60>", line 2, in create
  File "C:\odoo16\server\odoo\api.py", line 415, in _model_create_multi
    return create(self, arg)
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 673, in create
    return super().create(vals_list)
  File "<decorator-gen-68>", line 2, in create
  File "C:\odoo16\server\odoo\api.py", line 415, in _model_create_multi
    return create(self, arg)
  File "C:\odoo16\server\odoo\addons\base\models\ir_fields.py", line 670, in create
    recs = super().create(vals_list)
  File "<decorator-gen-15>", line 2, in create
  File "C:\odoo16\server\odoo\api.py", line 415, in _model_create_multi
    return create(self, arg)
  File "C:\odoo16\server\odoo\models.py", line 4002, in create
    records = self._create(data_list)
  File "C:\odoo16\server\odoo\models.py", line 4233, in _create
    records.modified(self._fields, create=True)
  File "C:\odoo16\server\odoo\models.py", line 6131, in modified
    todo = [self._modified([self._fields[fname] for fname in fnames], create)]
  File "C:\odoo16\server\odoo\models.py", line 6182, in _modified
    tree = self.pool.get_trigger_tree(fields, select=select)
  File "C:\odoo16\server\odoo\modules\registry.py", line 345, in get_trigger_tree
    if field in self._field_triggers
  File "C:\odoo16\server\odoo\tools\func.py", line 28, in __get__
    value = self.fget(obj)
  File "C:\odoo16\server\odoo\modules\registry.py", line 439, in _field_triggers
    dependencies = list(field.resolve_depends(self))
  File "C:\odoo16\server\odoo\fields.py", line 809, in resolve_depends
    warnings.warn(
 
2025-09-13 10:47:48,357 80756 INFO ardano_live7 odoo.addons.base.models.assetsbundle: Deleting ir.attachment [2372] (from bundle web.assets_frontend) 
2025-09-13 10:47:49,988 80756 INFO ardano_live7 odoo.addons.base.models.assetsbundle: Failed to find attachment for assets /web/assets/%-748a8fa/1/web.assets_frontend_lazy.min.js 
2025-09-13 10:48:09,317 80756 INFO ardano_live7 odoo.addons.base.models.assetsbundle: Deleting ir.attachment [2373] (from bundle web.assets_frontend_lazy) 
2025-09-13 10:48:10,791 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang.get_available at 0x0000023EFA3491C0>, (1,))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 3

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.lang(3,).flag_image_url'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 3

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.lang(3,).flag_image'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-09-13 10:48:10,840 80756 INFO ardano_live7 odoo.modules.registry: At least one model cache has been invalidated, signaling through the database. 
2025-09-13 10:48:10,840 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:48:10] "GET /ar/web/login HTTP/1.1" 200 - 211 1.180 39.105
2025-09-13 10:48:10,891 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:48:10] "GET /web/assets/2376-9424b3f/1/rtl/web.assets_frontend.min.css HTTP/1.1" 200 - 9 0.017 0.017
2025-09-13 10:48:11,275 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/37/37db4d649b09ca55779e81bc4d6d7eebca017503'
2025-09-13 10:48:11,334 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:48:11,350 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:48:11] "GET /web/assets/2377-748a8fa/1/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 4 0.000 0.062
2025-09-13 10:48:11,350 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:48:11] "GET /base/static/img/country_flags/001.png?height=25 HTTP/1.1" 303 - 2 0.016 0.046
2025-09-13 10:48:11,366 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:48:11] "GET /web/image/website/1/logo/My%20Website?unique=fc46e51 HTTP/1.1" 500 - 5 0.012 0.099
2025-09-13 10:48:11,366 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:48:11] "GET /web/image/website/1/favicon?unique=fc46e51 HTTP/1.1" 500 - 5 0.033 0.077
2025-09-13 10:48:11,536 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:48:11] "GET /website/translations/9eb369cb198cd3750374c9d1bbef135c3e9f4542?lang=ar_001 HTTP/1.1" 303 - 2 0.000 0.013
2025-09-13 10:48:11,696 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:48:11] "GET /website/translations/9eb369cb198cd3750374c9d1bbef135c3e9f4542?lang=ar_001 HTTP/1.1" 303 - 1 0.000 0.006
2025-09-13 10:48:12,929 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:48:12] "GET /ar/base/static/img/country_flags/001.png?height=25 HTTP/1.1" 404 - 78 0.197 0.940
2025-09-13 10:49:53,980 80756 INFO ardano_live7 odoo.addons.base.models.res_users: Login successful for db:ardano_live7 login:admin from 127.0.0.1 
2025-09-13 10:49:54,056 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:49:54] "POST /web/login HTTP/1.1" 303 - 44 0.179 1.618
2025-09-13 10:50:06,290 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\39/39d88146710e6d85aff4287cb71114b74bec53af 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\39/39d88146710e6d85aff4287cb71114b74bec53af'
2025-09-13 10:50:06,290 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\08/081747f5968b73aa876fca57024276a362363936 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\08/081747f5968b73aa876fca57024276a362363936'
2025-09-13 10:50:06,301 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\09/09d936e4381cd68c1a005bbdce51370298aa6df2 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\09/09d936e4381cd68c1a005bbdce51370298aa6df2'
2025-09-13 10:50:06,305 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\b2/b2256e07ff068458711ea094a13b4f8ecd678195 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\b2/b2256e07ff068458711ea094a13b4f8ecd678195'
2025-09-13 10:50:06,308 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\dd/ddd45ea2940f27f66d4ae16909d59c1044c34eda 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\dd/ddd45ea2940f27f66d4ae16909d59c1044c34eda'
2025-09-13 10:50:06,309 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\c4/c4cd08e1cc65b073a1c111c6e59815598eb8193a 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\c4/c4cd08e1cc65b073a1c111c6e59815598eb8193a'
2025-09-13 10:50:06,309 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\31/313d70285613905dc4c02f635a2c038429e2e8b2 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\31/313d70285613905dc4c02f635a2c038429e2e8b2'
2025-09-13 10:50:06,309 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\52/52e3458c9adf9c1a27e48d88a1cad5aef141f3ca 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\52/52e3458c9adf9c1a27e48d88a1cad5aef141f3ca'
2025-09-13 10:50:06,318 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\a2/a277ccbfa6dbad73170fed25aa7670e785ff2008 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\a2/a277ccbfa6dbad73170fed25aa7670e785ff2008'
2025-09-13 10:50:06,318 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\e0/e047400df1f1dd9235b99592a6dd4527aba3229c 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\e0/e047400df1f1dd9235b99592a6dd4527aba3229c'
2025-09-13 10:50:06,323 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\20/201313fe28d56fefeb12f29e2d93a9e5f16d1b17 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\20/201313fe28d56fefeb12f29e2d93a9e5f16d1b17'
2025-09-13 10:50:06,326 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\e3/e3d8df8535447007b4d158630e7a78e677e5063c 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\e3/e3d8df8535447007b4d158630e7a78e677e5063c'
2025-09-13 10:50:06,326 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\1f/1ff08ec60897e2e3d5c5a690128deebdc80bd2b3 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\1f/1ff08ec60897e2e3d5c5a690128deebdc80bd2b3'
2025-09-13 10:50:06,332 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\02/02cfe6b6c2dc876dadecb43449a1c4beb948bbcf 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\02/02cfe6b6c2dc876dadecb43449a1c4beb948bbcf'
2025-09-13 10:50:06,334 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\1a/1a8c2ad8d2577f04c2fd4d31f3fe2ede63c0c13f 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\1a/1a8c2ad8d2577f04c2fd4d31f3fe2ede63c0c13f'
2025-09-13 10:50:06,334 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\71/71c3cb0970bade62eb3128ea399627b530480b14 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\71/71c3cb0970bade62eb3128ea399627b530480b14'
2025-09-13 10:50:06,340 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\11/116da2379e342b5e35b514a7778e6d06ce33a607 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\11/116da2379e342b5e35b514a7778e6d06ce33a607'
2025-09-13 10:50:06,342 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\59/59f76a9b296a2e8e4732f4e69b4b7ace8a5057c9 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\59/59f76a9b296a2e8e4732f4e69b4b7ace8a5057c9'
2025-09-13 10:50:06,348 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\6a/6a5b448a0e0b9b61d0f519949af275b96008a96b 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\6a/6a5b448a0e0b9b61d0f519949af275b96008a96b'
2025-09-13 10:50:06,348 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\a3/a330920abcef2433621d6bb985bc24828d77f77b 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\a3/a330920abcef2433621d6bb985bc24828d77f77b'
2025-09-13 10:50:06,348 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\b8/b8f9001425cfd0ef0315797909281b912817643a 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\b8/b8f9001425cfd0ef0315797909281b912817643a'
2025-09-13 10:50:09,798 80756 INFO ardano_live7 odoo.addons.base.models.assetsbundle: Failed to find attachment for assets /web/assets/%-5992501/rtl/web.assets_backend.min.css 
2025-09-13 10:50:24,790 80756 WARNING ardano_live7 odoo.addons.base.models.assetsbundle: You need https://rtlcss.com/ to convert css file to right to left compatiblity. Use: npm install -g rtlcss 
2025-09-13 10:50:25,009 80756 INFO ardano_live7 odoo.addons.base.models.assetsbundle: Deleting ir.attachment [2374] (from bundle web.assets_backend) 
2025-09-13 10:50:26,090 80756 INFO ardano_live7 odoo.addons.base.models.assetsbundle: Failed to find attachment for assets /web/assets/%-5992501/web.assets_backend.min.js 
2025-09-13 10:51:20,035 80756 INFO ardano_live7 odoo.addons.base.models.assetsbundle: Deleting ir.attachment [2375] (from bundle web.assets_backend) 
2025-09-13 10:51:20,238 80756 INFO ardano_live7 odoo.modules.registry: At least one model cache has been invalidated, signaling through the database. 
2025-09-13 10:51:20,253 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:20] "GET /web HTTP/1.1" 200 - 161 0.641 85.554
2025-09-13 10:51:20,620 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:20] "GET /web/assets/2379-5992501/web.assets_backend.min.js HTTP/1.1" 200 - 9 0.010 0.027
2025-09-13 10:51:20,645 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:20] "GET /web/assets/2378-5992501/rtl/web.assets_backend.min.css HTTP/1.1" 200 - 6 0.029 0.032
2025-09-13 10:51:20,728 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\39/39d88146710e6d85aff4287cb71114b74bec53af 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\39/39d88146710e6d85aff4287cb71114b74bec53af'
2025-09-13 10:51:20,728 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\08/081747f5968b73aa876fca57024276a362363936 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\08/081747f5968b73aa876fca57024276a362363936'
2025-09-13 10:51:20,728 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\09/09d936e4381cd68c1a005bbdce51370298aa6df2 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\09/09d936e4381cd68c1a005bbdce51370298aa6df2'
2025-09-13 10:51:20,741 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\b2/b2256e07ff068458711ea094a13b4f8ecd678195 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\b2/b2256e07ff068458711ea094a13b4f8ecd678195'
2025-09-13 10:51:20,741 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\dd/ddd45ea2940f27f66d4ae16909d59c1044c34eda 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\dd/ddd45ea2940f27f66d4ae16909d59c1044c34eda'
2025-09-13 10:51:20,741 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\c4/c4cd08e1cc65b073a1c111c6e59815598eb8193a 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\c4/c4cd08e1cc65b073a1c111c6e59815598eb8193a'
2025-09-13 10:51:20,741 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\31/313d70285613905dc4c02f635a2c038429e2e8b2 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\31/313d70285613905dc4c02f635a2c038429e2e8b2'
2025-09-13 10:51:20,741 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\52/52e3458c9adf9c1a27e48d88a1cad5aef141f3ca 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\52/52e3458c9adf9c1a27e48d88a1cad5aef141f3ca'
2025-09-13 10:51:20,741 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\a2/a277ccbfa6dbad73170fed25aa7670e785ff2008 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\a2/a277ccbfa6dbad73170fed25aa7670e785ff2008'
2025-09-13 10:51:20,741 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\e0/e047400df1f1dd9235b99592a6dd4527aba3229c 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\e0/e047400df1f1dd9235b99592a6dd4527aba3229c'
2025-09-13 10:51:20,741 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\20/201313fe28d56fefeb12f29e2d93a9e5f16d1b17 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\20/201313fe28d56fefeb12f29e2d93a9e5f16d1b17'
2025-09-13 10:51:20,741 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\e3/e3d8df8535447007b4d158630e7a78e677e5063c 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\e3/e3d8df8535447007b4d158630e7a78e677e5063c'
2025-09-13 10:51:20,741 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\1f/1ff08ec60897e2e3d5c5a690128deebdc80bd2b3 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\1f/1ff08ec60897e2e3d5c5a690128deebdc80bd2b3'
2025-09-13 10:51:20,741 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\02/02cfe6b6c2dc876dadecb43449a1c4beb948bbcf 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\02/02cfe6b6c2dc876dadecb43449a1c4beb948bbcf'
2025-09-13 10:51:20,756 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\1a/1a8c2ad8d2577f04c2fd4d31f3fe2ede63c0c13f 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\1a/1a8c2ad8d2577f04c2fd4d31f3fe2ede63c0c13f'
2025-09-13 10:51:20,756 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\71/71c3cb0970bade62eb3128ea399627b530480b14 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\71/71c3cb0970bade62eb3128ea399627b530480b14'
2025-09-13 10:51:20,756 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\11/116da2379e342b5e35b514a7778e6d06ce33a607 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\11/116da2379e342b5e35b514a7778e6d06ce33a607'
2025-09-13 10:51:20,756 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\59/59f76a9b296a2e8e4732f4e69b4b7ace8a5057c9 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\59/59f76a9b296a2e8e4732f4e69b4b7ace8a5057c9'
2025-09-13 10:51:20,756 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\6a/6a5b448a0e0b9b61d0f519949af275b96008a96b 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\6a/6a5b448a0e0b9b61d0f519949af275b96008a96b'
2025-09-13 10:51:20,756 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\a3/a330920abcef2433621d6bb985bc24828d77f77b 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\a3/a330920abcef2433621d6bb985bc24828d77f77b'
2025-09-13 10:51:20,756 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\b8/b8f9001425cfd0ef0315797909281b912817643a 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\b8/b8f9001425cfd0ef0315797909281b912817643a'
2025-09-13 10:51:20,852 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:20] "GET /web/webclient/load_menus/c1ae45ae54868a5988314fd559b535d643fe2798e8f684db71c1c95870f22b40 HTTP/1.1" 200 - 48 0.121 0.147
2025-09-13 10:51:23,115 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:23] "GET /bus/websocket_worker_bundle?v=1.0.5 HTTP/1.1" 304 - 5 0.019 0.039
2025-09-13 10:51:23,768 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:23] "GET /websocket HTTP/1.1" 101 - 1 0.000 0.010
2025-09-13 10:51:23,813 80756 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-09-13 10:51:23,900 80756 ERROR ardano_live7 odoo.sql_db: bad query: SELECT "hr_employee".id FROM "hr_employee" WHERE ((("hr_employee"."active" = true) AND ("hr_employee"."user_id" in (2, 1, 11, 47, 46, 31, 53, 62))) AND ("hr_employee"."company_id" = 1)) ORDER BY  COALESCE("hr_employee"."name"->>'ar_EG', "hr_employee"."name"->>'en_US')  
ERROR: operator does not exist: character varying ->> unknown
LINE 1: ..._id" = 1)) ORDER BY  COALESCE("hr_employee"."name"->>'ar_EG'...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
 
2025-09-13 10:51:24,051 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.users(2,).leave_date_to'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.users(2,).employee_id'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1889, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\mail\controllers\discuss.py", line 196, in mail_init_messaging
    return request.env.user.sudo(request.env.user.has_group('base.group_portal'))._init_messaging()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\mail_bot\models\res_users.py", line 28, in _init_messaging
    return super()._init_messaging()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\mail\models\res_users.py", line 183, in _init_messaging
    'channels': self.partner_id._get_channels_as_member().channel_info(),
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\mail\models\mail_channel.py", line 754, in channel_info
    all_needed_members.partner_id.sudo().mail_partner_format()  # prefetch in batch
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\hr_holidays\models\res_partner.py", line 35, in mail_partner_format
    dates = partner.mapped('user_ids.leave_date_to')
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 5446, in mapped
    recs = recs._fields[name].mapped(recs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\fields.py", line 1284, in mapped
    self.__get__(first(remaining), type(remaining))
  File "C:\odoo16\server\odoo\fields.py", line 1210, in __get__
    self.compute_value(recs)
  File "C:\odoo16\server\odoo\fields.py", line 1392, in compute_value
    records._compute_field_value(self)
  File "C:\odoo16\server\odoo\models.py", line 4259, in _compute_field_value
    fields.determine(field.compute, self)
  File "C:\odoo16\server\odoo\fields.py", line 101, in determine
    return needle(records, *args)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\fields.py", line 690, in _compute_related
    values = [first(value[name]) for value in values]
                    ~~~~~^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 5975, in __getitem__
    return self._fields[key].__get__(self, self.env.registry[self._name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\fields.py", line 2812, in __get__
    return super().__get__(records, owner)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\fields.py", line 1210, in __get__
    self.compute_value(recs)
  File "C:\odoo16\server\odoo\fields.py", line 1392, in compute_value
    records._compute_field_value(self)
  File "C:\odoo16\server\odoo\models.py", line 4259, in _compute_field_value
    fields.determine(field.compute, self)
  File "C:\odoo16\server\odoo\fields.py", line 98, in determine
    return needle(*args)
           ^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\hr\models\res_users.py", line 274, in _compute_company_employee
    for employee in self.env['hr.employee'].search([('user_id', 'in', self.ids), ('company_id', '=', self.env.company.id)])
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 1527, in search
    return res if count else self.browse(res)
                             ^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 5167, in browse
    if not ids:
           ^^^
  File "C:\odoo16\server\odoo\tools\query.py", line 217, in __bool__
    return bool(self._result)
                ^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\func.py", line 28, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\query.py", line 210, in _result
    self._cr.execute(query_str, params)
  File "C:\odoo16\server\odoo\sql_db.py", line 321, in execute
    res = self._obj.execute(query, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedFunction: operator does not exist: character varying ->> unknown
LINE 1: ..._id" = 1)) ORDER BY  COALESCE("hr_employee"."name"->>'ar_EG'...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

2025-09-13 10:51:24,051 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:24] "POST /mail/init_messaging HTTP/1.1" 200 - 32 0.168 0.195
2025-09-13 10:51:24,306 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:24] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 57 0.328 0.286
2025-09-13 10:51:41,291 80756 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:41] "GET /mail/static/src/audio/ting.ogg HTTP/1.1" 206 - 0 0.000 0.008
2025-09-13 10:51:41,691 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:51:41,691 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:41] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.008 0.009
2025-09-13 10:51:41,728 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:41] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 16 0.032 0.005
2025-09-13 10:51:41,939 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:51:41,939 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:41] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.000 0.006
2025-09-13 10:51:42,006 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:51:42,021 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:42] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.000 0.015
2025-09-13 10:51:42,270 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:51:42,270 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:42] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.000 0.014
2025-09-13 10:51:42,349 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:51:42,349 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:42] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.006 0.010
2025-09-13 10:51:44,076 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:44] "POST /web/action/load HTTP/1.1" 200 - 11 0.059 0.011
2025-09-13 10:51:44,552 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:44] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 48 0.094 0.051
2025-09-13 10:51:44,773 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:44] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 52 0.086 0.032
2025-09-13 10:51:44,901 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:44] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.011 0.002
2025-09-13 10:51:45,091 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang.get_available at 0x0000023EFA3491C0>, (1,))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 3

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.lang(3,).flag_image_url'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 3

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.lang(3,).flag_image'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-09-13 10:51:45,100 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:45] "GET /hotel_room_dashboard_view/static/description/icon.png HTTP/1.1" 303 - 9 0.017 0.016
2025-09-13 10:51:45,603 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:45] "GET /sale_enhancement/static/description/icon.png HTTP/1.1" 303 - 2 0.002 0.195
2025-09-13 10:51:45,607 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:51:45,607 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:45] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.083 0.119
2025-09-13 10:51:45,607 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:45] "GET /delivery_fedex_rest/static/description/icon.png HTTP/1.1" 303 - 2 0.000 0.201
2025-09-13 10:51:45,607 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:45] "GET /hotel/static/description/icon.png HTTP/1.1" 303 - 2 0.005 0.179
2025-09-13 10:51:45,618 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:45] "GET /hotel_restaurant_pos/static/description/icon.png HTTP/1.1" 303 - 2 0.016 0.125
2025-09-13 10:51:45,942 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:51:45,944 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:45] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.017 0.006
2025-09-13 10:51:46,347 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:46] "GET /delivery_usps_rest/static/description/icon.png HTTP/1.1" 303 - 2 0.010 0.030
2025-09-13 10:51:46,499 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:46] "GET /ar/hotel_restaurant_pos/static/description/icon.png HTTP/1.1" 404 - 69 0.285 0.292
2025-09-13 10:51:46,506 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:46] "GET /ar/delivery_fedex_rest/static/description/icon.png HTTP/1.1" 404 - 70 0.269 0.316
2025-09-13 10:51:46,508 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:46] "GET /ar/hotel/static/description/icon.png HTTP/1.1" 404 - 75 0.213 0.374
2025-09-13 10:51:46,508 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:46] "GET /ar/hotel_room_dashboard_view/static/description/icon.png HTTP/1.1" 404 - 158 0.389 0.728
2025-09-13 10:51:46,508 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:46] "GET /ar/sale_enhancement/static/description/icon.png HTTP/1.1" 404 - 74 0.308 0.278
2025-09-13 10:51:46,706 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:46] "GET /ar/delivery_usps_rest/static/description/icon.png HTTP/1.1" 404 - 13 0.014 0.025
2025-09-13 10:51:47,358 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:47] "POST /web/action/load HTTP/1.1" 200 - 10 0.022 0.013
2025-09-13 10:51:47,635 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:47] "POST /web/dataset/call_kw/base.module.update/get_views HTTP/1.1" 200 - 9 0.034 0.029
2025-09-13 10:51:47,693 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:47] "POST /web/dataset/call_kw/base.module.update/onchange HTTP/1.1" 200 - 2 0.017 0.000
2025-09-13 10:51:49,023 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:49] "POST /web/dataset/call_kw/base.module.update/create HTTP/1.1" 200 - 4 0.027 0.003
2025-09-13 10:51:49,371 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:49] "POST /web/dataset/call_kw/base.module.update/read HTTP/1.1" 200 - 3 0.015 0.000
2025-09-13 10:51:49,607 80756 INFO ardano_live7 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user admin #2 via 127.0.0.1 
2025-09-13 10:51:51,135 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:51:51] "POST /web/dataset/call_button HTTP/1.1" 200 - 2100 0.983 0.550
2025-09-13 10:51:53,326 80756 INFO ardano_live7 odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_upgrade on ['Travel Agent Management'] to user admin #2 via 127.0.0.1 
2025-09-13 10:51:53,326 80756 INFO ardano_live7 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Travel Agent Management'] to user admin #2 via 127.0.0.1 
2025-09-13 10:51:53,326 80756 INFO ardano_live7 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Travel Agent Management'] to user admin #2 via 127.0.0.1 
2025-09-13 10:51:55,987 80756 INFO ardano_live7 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user admin #2 via 127.0.0.1 
2025-09-13 10:51:56,272 80756 INFO ardano_live7 odoo.modules.loading: loading 1 modules... 
2025-09-13 10:51:56,293 80756 INFO ardano_live7 odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-09-13 10:51:56,456 80756 INFO ardano_live7 odoo.modules.loading: updating modules list 
2025-09-13 10:51:56,456 80756 INFO ardano_live7 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-13 10:52:01,837 80756 INFO ardano_live7 odoo.modules.loading: loading 215 modules... 
2025-09-13 10:52:02,036 80756 WARNING ardano_live7 odoo.models: The model hr.employee.certificate has no _description 
2025-09-13 10:52:02,037 80756 WARNING ardano_live7 odoo.models: The model job.category has no _description 
2025-09-13 10:52:02,037 80756 WARNING ardano_live7 odoo.models: The model contract.classification has no _description 
2025-09-13 10:52:02,037 80756 WARNING ardano_live7 odoo.models: The model hr.bounce has no _description 
2025-09-13 10:52:02,037 80756 WARNING ardano_live7 odoo.models: The model bounce.line has no _description 
2025-09-13 10:52:02,037 80756 WARNING ardano_live7 odoo.models: The model hr.ardano_department has no _description 
2025-09-13 10:52:02,037 80756 WARNING ardano_live7 odoo.models: The model hr.team has no _description 
2025-09-13 10:52:02,037 80756 WARNING ardano_live7 odoo.models: The model hr.unit has no _description 
2025-09-13 10:52:02,039 80756 WARNING ardano_live7 odoo.models: The model analytic.wizard.report has no _description 
2025-09-13 10:52:02,066 80756 INFO ardano_live7 odoo.modules.loading: Loading module travel_agent_management (198/215) 
2025-09-13 10:52:02,205 80756 WARNING ardano_live7 odoo.fields: account.analytic.line.cubes_type: selection attribute will be ignored as the field is related 
2025-09-13 10:52:02,220 80756 WARNING ardano_live7 odoo.fields: product.template.cubes_type: selection attribute will be ignored as the field is related 
2025-09-13 10:52:02,300 80756 WARNING ardano_live7 odoo.fields: Redundant default on product.template.cubes_type 
2025-09-13 10:52:02,300 80756 WARNING ardano_live7 odoo.fields: Redundant default on account.analytic.line.cubes_type 
2025-09-13 10:52:02,336 80756 WARNING ardano_live7 odoo.fields: Field job.category.job_category: unknown parameter 'Index', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,336 80756 WARNING ardano_live7 odoo.fields: Field contract.classification.contract_name: unknown parameter 'Index', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,336 80756 WARNING ardano_live7 odoo.fields: Field hr.bounce.amount_percentage: unknown parameter 'widget', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,507 80756 WARNING ardano_live7 odoo.fields: account.analytic.line.cubes_type: selection attribute will be ignored as the field is related 
2025-09-13 10:52:02,527 80756 WARNING ardano_live7 odoo.fields: product.template.cubes_type: selection attribute will be ignored as the field is related 
2025-09-13 10:52:02,667 80756 WARNING ardano_live7 odoo.fields: Redundant default on product.template.cubes_type 
2025-09-13 10:52:02,667 80756 WARNING ardano_live7 odoo.fields: Redundant default on account.analytic.line.cubes_type 
2025-09-13 10:52:02,720 80756 WARNING ardano_live7 odoo.fields: Field job.category.job_category: unknown parameter 'Index', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,723 80756 WARNING ardano_live7 odoo.fields: Field contract.classification.contract_name: unknown parameter 'Index', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,724 80756 WARNING ardano_live7 odoo.fields: Field hr.bounce.amount_percentage: unknown parameter 'widget', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,727 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,727 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,727 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,727 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,727 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,727 80756 WARNING ardano_live7 odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:02,851 80756 INFO ardano_live7 odoo.modules.registry: module travel_agent_management: creating or updating database tables 
2025-09-13 10:52:03,295 80756 WARNING ardano_live7 odoo.addons.base.models.ir_model: Two fields (departure_date, travel_date) of travel.ticket() have the same label: Departure Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 10:52:03,295 80756 WARNING ardano_live7 odoo.addons.base.models.ir_model: Two fields (return_date, return_travel_date) of travel.ticket() have the same label: Return Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 10:52:03,299 80756 WARNING ardano_live7 odoo.addons.base.models.ir_model: Two fields (field_type, field_type_id) of visa.request.field() have the same label: Field Type. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 10:52:03,667 80756 INFO ardano_live7 odoo.modules.registry: Models have no table: contract.classification, job.category, inventory.wizard.report, bounce.line. 
2025-09-13 10:52:03,667 80756 INFO ardano_live7 odoo.modules.registry: Recreate table of model contract.classification. 
2025-09-13 10:52:03,667 80756 INFO ardano_live7 odoo.modules.registry: Recreate table of model job.category. 
2025-09-13 10:52:03,667 80756 INFO ardano_live7 odoo.modules.registry: Recreate table of model inventory.wizard.report. 
2025-09-13 10:52:03,667 80756 INFO ardano_live7 odoo.modules.registry: Recreate table of model bounce.line. 
2025-09-13 10:52:03,694 80756 ERROR ardano_live7 odoo.modules.registry: Model inventory.wizard.report has no table. 
2025-09-13 10:52:03,696 80756 ERROR ardano_live7 odoo.modules.registry: Model bounce.line has no table. 
2025-09-13 10:52:03,696 80756 ERROR ardano_live7 odoo.modules.registry: Model contract.classification has no table. 
2025-09-13 10:52:03,696 80756 ERROR ardano_live7 odoo.modules.registry: Model job.category has no table. 
2025-09-13 10:52:04,386 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/security/security_groups.xml 
2025-09-13 10:52:04,942 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/security/ir.model.access.csv 
2025-09-13 10:52:06,372 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/security/portal_security.xml 
2025-09-13 10:52:06,657 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/data/sequences.xml 
2025-09-13 10:52:06,674 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/data/visa_sequences.xml 
2025-09-13 10:52:06,689 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/data/transfer_sequence.xml 
2025-09-13 10:52:06,703 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/data/hotel_sequence.xml 
2025-09-13 10:52:06,710 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/data/insurance_sequence.xml 
2025-09-13 10:52:06,712 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/ticket_request_views.xml 
2025-09-13 10:52:06,998 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/passenger_views.xml 
2025-09-13 10:52:07,143 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/passenger_line_views.xml 
2025-09-13 10:52:07,201 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/flight_segment_views.xml 
2025-09-13 10:52:07,323 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/travel_ticket_views.xml 
2025-09-13 10:52:07,458 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/menu_views.xml 
2025-09-13 10:52:07,806 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/visa_country_views.xml 
2025-09-13 10:52:07,891 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-globe fa-2x) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\visa_country_views.xml',
 'line': 12,
 'name': 'visa.country.kanban',
 'view': ir.ui.view(3042,),
 'view.model': 'visa.country',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_visa_country_kanban'} 
2025-09-13 10:52:07,939 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/visa_document_type_views.xml 
2025-09-13 10:52:08,036 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/visa_field_type_views.xml 
2025-09-13 10:52:08,120 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/visa_type_views.xml 
2025-09-13 10:52:08,282 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/visa_request_views.xml 
2025-09-13 10:52:08,444 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/visa_menu_views.xml 
2025-09-13 10:52:08,539 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/transfer_request_views.xml 
2025-09-13 10:52:08,698 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-route) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 36,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(3062,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 10:52:08,701 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 39,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(3062,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 10:52:08,703 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 40,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(3062,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 10:52:08,703 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 43,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(3062,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 10:52:08,753 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/hotel_booking_views.xml 
2025-09-13 10:52:08,872 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-building) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 37,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(3066,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 10:52:08,874 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 40,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(3066,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 10:52:08,874 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-moon-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 43,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(3066,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 10:52:08,874 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 44,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(3066,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 10:52:08,908 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/insurance_views.xml 
2025-09-13 10:52:09,033 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-shield) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 37,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(3070,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 10:52:09,033 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 40,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(3070,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 10:52:09,033 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 43,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(3070,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 10:52:09,033 80756 WARNING ardano_live7 odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-tag) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 45,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(3070,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 10:52:09,084 80756 INFO ardano_live7 odoo.modules.loading: loading travel_agent_management/views/portal_templates.xml 
2025-09-13 10:52:10,121 80756 INFO ardano_live7 odoo.addons.base.models.ir_module: module travel_agent_management: loading base translation file ar for language ar_001 
2025-09-13 10:52:10,122 80756 INFO ardano_live7 odoo.addons.base.models.ir_module: module travel_agent_management: no translation for language ar_001 
2025-09-13 10:52:10,122 80756 INFO ardano_live7 odoo.addons.base.models.ir_module: module travel_agent_management: loading base translation file ar for language ar_EG 
2025-09-13 10:52:10,140 80756 INFO ardano_live7 odoo.addons.base.models.ir_module: module travel_agent_management: no translation for language ar_EG 
2025-09-13 10:52:10,476 80756 INFO ardano_live7 odoo.modules.loading: Module travel_agent_management loaded in 8.41s, 1504 queries (+1504 other) 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model report.project_location.closing_report_template has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model work.measurement has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model project.work.description has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model project_point.description has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model task.signers has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model task.material has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model task.purchases has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model task.operations has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model request.discount has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model approval.signers has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model move.signers has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model task.mat_purchase_wizard has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model task.purchases_wizard_line has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model project.closing_wizard has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model check.duplication_wizard has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model check.duplication_wizard_lines has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model approval.bill has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model approval.daily_bill_wizard_line has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model approval.daily_bill_wizard has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model project.canceling_wizard has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model project.measurement.import.wizard.line has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model project.measurement.import.wizard has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model project.measurement.import.sample.wizard has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model project.measurement.export.wizard has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model task.cancel_work_order.wizard has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model approval_request.cancel has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model project.report_wizard_line has no _description 
2025-09-13 10:52:10,478 80756 WARNING ardano_live7 odoo.models: The model project.report_wizard has no _description 
2025-09-13 10:52:10,478 80756 INFO ardano_live7 odoo.modules.loading: 215 modules loaded in 8.64s, 1504 queries (+1504 extra) 
2025-09-13 10:52:10,565 80756 WARNING ardano_live7 odoo.fields: account.analytic.line.cubes_type: selection attribute will be ignored as the field is related 
2025-09-13 10:52:10,565 80756 WARNING ardano_live7 odoo.fields: product.template.cubes_type: selection attribute will be ignored as the field is related 
2025-09-13 10:52:10,626 80756 WARNING ardano_live7 odoo.fields: project.project.stage_status: selection attribute will be ignored as the field is related 
2025-09-13 10:52:10,635 80756 WARNING ardano_live7 odoo.fields: project.task.contractor_status: selection attribute will be ignored as the field is related 
2025-09-13 10:52:10,756 80756 WARNING ardano_live7 odoo.fields: Redundant default on product.template.cubes_type 
2025-09-13 10:52:10,756 80756 WARNING ardano_live7 odoo.fields: Redundant default on account.analytic.line.cubes_type 
2025-09-13 10:52:10,789 80756 WARNING ardano_live7 odoo.fields: Field approval.request.signer_ids: unknown parameter 'ondelete', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:10,848 80756 WARNING ardano_live7 odoo.fields: Field job.category.job_category: unknown parameter 'Index', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:10,848 80756 WARNING ardano_live7 odoo.fields: Field contract.classification.contract_name: unknown parameter 'Index', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:10,848 80756 WARNING ardano_live7 odoo.fields: Field hr.bounce.amount_percentage: unknown parameter 'widget', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:10,853 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:10,853 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:10,853 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:10,853 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:10,853 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:10,853 80756 WARNING ardano_live7 odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:11,263 80756 WARNING ardano_live7 odoo.fields: account.analytic.line.cubes_type: selection attribute will be ignored as the field is related 
2025-09-13 10:52:11,293 80756 WARNING ardano_live7 odoo.fields: product.template.cubes_type: selection attribute will be ignored as the field is related 
2025-09-13 10:52:11,351 80756 WARNING ardano_live7 odoo.fields: project.project.stage_status: selection attribute will be ignored as the field is related 
2025-09-13 10:52:11,351 80756 WARNING ardano_live7 odoo.fields: project.task.contractor_status: selection attribute will be ignored as the field is related 
2025-09-13 10:52:11,456 80756 WARNING ardano_live7 odoo.fields: Redundant default on product.template.cubes_type 
2025-09-13 10:52:11,456 80756 WARNING ardano_live7 odoo.fields: Redundant default on account.analytic.line.cubes_type 
2025-09-13 10:52:11,456 80756 WARNING ardano_live7 odoo.fields: Field approval.request.signer_ids: unknown parameter 'ondelete', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:11,514 80756 WARNING ardano_live7 odoo.fields: Field job.category.job_category: unknown parameter 'Index', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:11,514 80756 WARNING ardano_live7 odoo.fields: Field contract.classification.contract_name: unknown parameter 'Index', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:11,514 80756 WARNING ardano_live7 odoo.fields: Field hr.bounce.amount_percentage: unknown parameter 'widget', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:11,516 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:11,516 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:11,516 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:11,516 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:11,516 80756 WARNING ardano_live7 odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:11,516 80756 WARNING ardano_live7 odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:52:13,425 80756 INFO ardano_live7 odoo.addons.base.models.ir_model: Deleting <EMAIL> (travel_agent_management.portal_my_home_passengers) 
2025-09-13 10:52:14,338 80756 INFO ardano_live7 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [844169] 
2025-09-13 10:52:14,340 80756 INFO ardano_live7 odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [3030] 
2025-09-13 10:52:14,354 80756 INFO ardano_live7 odoo.addons.base.models.ir_model: Deleting <EMAIL> (travel_agent_management.portal_my_home_travel_requests) 
2025-09-13 10:52:14,439 80756 INFO ardano_live7 odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [844162] 
2025-09-13 10:52:14,439 80756 INFO ardano_live7 odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [3023] 
2025-09-13 10:52:14,472 80756 INFO ardano_live7 odoo.modules.registry: verifying fields for every extended model 
2025-09-13 10:52:14,563 80756 INFO ardano_live7 odoo.modules.registry: Models have no table: contract.classification, job.category, inventory.wizard.report, bounce.line. 
2025-09-13 10:52:14,565 80756 INFO ardano_live7 odoo.modules.registry: Recreate table of model contract.classification. 
2025-09-13 10:52:14,565 80756 INFO ardano_live7 odoo.modules.registry: Recreate table of model job.category. 
2025-09-13 10:52:14,565 80756 INFO ardano_live7 odoo.modules.registry: Recreate table of model inventory.wizard.report. 
2025-09-13 10:52:14,565 80756 INFO ardano_live7 odoo.modules.registry: Recreate table of model bounce.line. 
2025-09-13 10:52:14,580 80756 ERROR ardano_live7 odoo.modules.registry: Model inventory.wizard.report has no table. 
2025-09-13 10:52:14,580 80756 ERROR ardano_live7 odoo.modules.registry: Model bounce.line has no table. 
2025-09-13 10:52:14,580 80756 ERROR ardano_live7 odoo.modules.registry: Model contract.classification has no table. 
2025-09-13 10:52:14,580 80756 ERROR ardano_live7 odoo.modules.registry: Model job.category has no table. 
2025-09-13 10:52:20,297 80756 INFO ardano_live7 odoo.modules.loading: Modules loaded. 
2025-09-13 10:52:20,329 80756 INFO ardano_live7 odoo.modules.registry: Registry loaded in 24.120s 
2025-09-13 10:52:20,332 80756 INFO ardano_live7 odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-09-13 10:52:20,348 80756 INFO ardano_live7 odoo.modules.registry: Registry changed, signaling through the database 
2025-09-13 10:52:20,365 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:20] "POST /web/dataset/call_button HTTP/1.1" 200 - 7717 16.638 10.401
2025-09-13 10:52:20,700 80756 INFO ardano_live7 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-09-13 10:52:21,166 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\39/39d88146710e6d85aff4287cb71114b74bec53af 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\39/39d88146710e6d85aff4287cb71114b74bec53af'
2025-09-13 10:52:21,166 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\08/081747f5968b73aa876fca57024276a362363936 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\08/081747f5968b73aa876fca57024276a362363936'
2025-09-13 10:52:21,168 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\09/09d936e4381cd68c1a005bbdce51370298aa6df2 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\09/09d936e4381cd68c1a005bbdce51370298aa6df2'
2025-09-13 10:52:21,170 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\b2/b2256e07ff068458711ea094a13b4f8ecd678195 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\b2/b2256e07ff068458711ea094a13b4f8ecd678195'
2025-09-13 10:52:21,170 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\dd/ddd45ea2940f27f66d4ae16909d59c1044c34eda 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\dd/ddd45ea2940f27f66d4ae16909d59c1044c34eda'
2025-09-13 10:52:21,170 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\c4/c4cd08e1cc65b073a1c111c6e59815598eb8193a 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\c4/c4cd08e1cc65b073a1c111c6e59815598eb8193a'
2025-09-13 10:52:21,173 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\31/313d70285613905dc4c02f635a2c038429e2e8b2 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\31/313d70285613905dc4c02f635a2c038429e2e8b2'
2025-09-13 10:52:21,174 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\52/52e3458c9adf9c1a27e48d88a1cad5aef141f3ca 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\52/52e3458c9adf9c1a27e48d88a1cad5aef141f3ca'
2025-09-13 10:52:21,174 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\a2/a277ccbfa6dbad73170fed25aa7670e785ff2008 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\a2/a277ccbfa6dbad73170fed25aa7670e785ff2008'
2025-09-13 10:52:21,174 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\e0/e047400df1f1dd9235b99592a6dd4527aba3229c 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\e0/e047400df1f1dd9235b99592a6dd4527aba3229c'
2025-09-13 10:52:21,174 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\20/201313fe28d56fefeb12f29e2d93a9e5f16d1b17 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\20/201313fe28d56fefeb12f29e2d93a9e5f16d1b17'
2025-09-13 10:52:21,174 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\e3/e3d8df8535447007b4d158630e7a78e677e5063c 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\e3/e3d8df8535447007b4d158630e7a78e677e5063c'
2025-09-13 10:52:21,174 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\1f/1ff08ec60897e2e3d5c5a690128deebdc80bd2b3 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\1f/1ff08ec60897e2e3d5c5a690128deebdc80bd2b3'
2025-09-13 10:52:21,174 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\02/02cfe6b6c2dc876dadecb43449a1c4beb948bbcf 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\02/02cfe6b6c2dc876dadecb43449a1c4beb948bbcf'
2025-09-13 10:52:21,174 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\1a/1a8c2ad8d2577f04c2fd4d31f3fe2ede63c0c13f 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\1a/1a8c2ad8d2577f04c2fd4d31f3fe2ede63c0c13f'
2025-09-13 10:52:21,182 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\71/71c3cb0970bade62eb3128ea399627b530480b14 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\71/71c3cb0970bade62eb3128ea399627b530480b14'
2025-09-13 10:52:21,184 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\11/116da2379e342b5e35b514a7778e6d06ce33a607 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\11/116da2379e342b5e35b514a7778e6d06ce33a607'
2025-09-13 10:52:21,186 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\59/59f76a9b296a2e8e4732f4e69b4b7ace8a5057c9 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\59/59f76a9b296a2e8e4732f4e69b4b7ace8a5057c9'
2025-09-13 10:52:21,188 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\6a/6a5b448a0e0b9b61d0f519949af275b96008a96b 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\6a/6a5b448a0e0b9b61d0f519949af275b96008a96b'
2025-09-13 10:52:21,190 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\a3/a330920abcef2433621d6bb985bc24828d77f77b 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\a3/a330920abcef2433621d6bb985bc24828d77f77b'
2025-09-13 10:52:21,192 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\b8/b8f9001425cfd0ef0315797909281b912817643a 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000023EF72BFC40>, 2, '1', ('ar_EG',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1685

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1685,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\b8/b8f9001425cfd0ef0315797909281b912817643a'
2025-09-13 10:52:21,999 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:21] "GET /web HTTP/1.1" 200 - 123 0.155 1.155
2025-09-13 10:52:22,825 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:22] "GET /bus/websocket_worker_bundle?v=1.0.5 HTTP/1.1" 304 - 6 0.003 0.025
2025-09-13 10:52:22,927 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:22] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 17 0.030 0.030
2025-09-13 10:52:23,192 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:23] "GET /websocket HTTP/1.1" 101 - 1 0.000 0.000
2025-09-13 10:52:23,323 80756 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:23] "GET /mail/static/src/audio/ting.ogg HTTP/1.1" 206 - 0 0.000 0.015
2025-09-13 10:52:23,329 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:52:23,336 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:23] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.012 0.019
2025-09-13 10:52:23,422 80756 ERROR ardano_live7 odoo.sql_db: bad query: SELECT "hr_employee".id FROM "hr_employee" WHERE ((("hr_employee"."active" = true) AND ("hr_employee"."user_id" in (2, 1, 11, 47, 46, 31, 53, 62))) AND ("hr_employee"."company_id" = 1)) ORDER BY  COALESCE("hr_employee"."name"->>'ar_EG', "hr_employee"."name"->>'en_US')  
ERROR: operator does not exist: character varying ->> unknown
LINE 1: ..._id" = 1)) ORDER BY  COALESCE("hr_employee"."name"->>'ar_EG'...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
 
2025-09-13 10:52:23,422 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.users(2,).leave_date_to'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.users(2,).employee_id'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1889, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\mail\controllers\discuss.py", line 196, in mail_init_messaging
    return request.env.user.sudo(request.env.user.has_group('base.group_portal'))._init_messaging()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\mail_bot\models\res_users.py", line 28, in _init_messaging
    return super()._init_messaging()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\mail\models\res_users.py", line 183, in _init_messaging
    'channels': self.partner_id._get_channels_as_member().channel_info(),
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\mail\models\mail_channel.py", line 754, in channel_info
    all_needed_members.partner_id.sudo().mail_partner_format()  # prefetch in batch
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\hr_holidays\models\res_partner.py", line 35, in mail_partner_format
    dates = partner.mapped('user_ids.leave_date_to')
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 5446, in mapped
    recs = recs._fields[name].mapped(recs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\fields.py", line 1284, in mapped
    self.__get__(first(remaining), type(remaining))
  File "C:\odoo16\server\odoo\fields.py", line 1210, in __get__
    self.compute_value(recs)
  File "C:\odoo16\server\odoo\fields.py", line 1392, in compute_value
    records._compute_field_value(self)
  File "C:\odoo16\server\odoo\models.py", line 4259, in _compute_field_value
    fields.determine(field.compute, self)
  File "C:\odoo16\server\odoo\fields.py", line 101, in determine
    return needle(records, *args)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\fields.py", line 690, in _compute_related
    values = [first(value[name]) for value in values]
                    ~~~~~^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 5975, in __getitem__
    return self._fields[key].__get__(self, self.env.registry[self._name])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\fields.py", line 2812, in __get__
    return super().__get__(records, owner)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\fields.py", line 1210, in __get__
    self.compute_value(recs)
  File "C:\odoo16\server\odoo\fields.py", line 1392, in compute_value
    records._compute_field_value(self)
  File "C:\odoo16\server\odoo\models.py", line 4259, in _compute_field_value
    fields.determine(field.compute, self)
  File "C:\odoo16\server\odoo\fields.py", line 98, in determine
    return needle(*args)
           ^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\hr\models\res_users.py", line 274, in _compute_company_employee
    for employee in self.env['hr.employee'].search([('user_id', 'in', self.ids), ('company_id', '=', self.env.company.id)])
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 1527, in search
    return res if count else self.browse(res)
                             ^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 5167, in browse
    if not ids:
           ^^^
  File "C:\odoo16\server\odoo\tools\query.py", line 217, in __bool__
    return bool(self._result)
                ^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\func.py", line 28, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\query.py", line 210, in _result
    self._cr.execute(query_str, params)
  File "C:\odoo16\server\odoo\sql_db.py", line 321, in execute
    res = self._obj.execute(query, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedFunction: operator does not exist: character varying ->> unknown
LINE 1: ..._id" = 1)) ORDER BY  COALESCE("hr_employee"."name"->>'ar_EG'...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

2025-09-13 10:52:23,441 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:23] "POST /mail/init_messaging HTTP/1.1" 200 - 30 0.080 0.075
2025-09-13 10:52:23,560 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:52:23,560 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:23] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.003 0.021
2025-09-13 10:52:24,010 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:24] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 60 0.294 0.425
2025-09-13 10:52:34,827 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:34] "POST /web/action/load HTTP/1.1" 200 - 10 0.015 0.017
2025-09-13 10:52:35,453 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:35] "POST /web/dataset/call_kw/res.config.settings/get_views HTTP/1.1" 200 - 82 0.115 0.196
2025-09-13 10:52:35,944 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\d0/d09086a0794cf3070f12e742f27126254b4e2b5a 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: <NewId 0x23efec3e320>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.config.settings(<NewId 0x23efec3e320>,).favicon'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'website(1,).favicon'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1522

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1522,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1522

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1522,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:52:36,140 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:36] "POST /web/dataset/call_kw/res.config.settings/onchange HTTP/1.1" 200 - 253 0.482 0.113
2025-09-13 10:52:36,457 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:36] "POST /web/dataset/call_kw/res.lang/read HTTP/1.1" 200 - 3 0.000 0.000
2025-09-13 10:52:36,723 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:36] "POST /base_setup/demo_active HTTP/1.1" 200 - 3 0.000 0.000
2025-09-13 10:52:36,952 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:36] "POST /base_setup/data HTTP/1.1" 200 - 6 0.007 0.006
2025-09-13 10:52:37,100 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:52:37,108 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:37] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.000 0.028
2025-09-13 10:52:37,439 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:52:37,440 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:37] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.000 0.016
2025-09-13 10:52:38,695 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\d0/d09086a0794cf3070f12e742f27126254b4e2b5a 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'website(1,).favicon'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1522

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1522,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1522

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1522,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:52:38,806 80756 INFO ardano_live7 odoo.modules.registry: At least one model cache has been invalidated, signaling through the database. 
2025-09-13 10:52:38,816 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:38] "POST /web/dataset/call_kw/res.config.settings/create HTTP/1.1" 200 - 53 0.096 0.047
2025-09-13 10:52:39,240 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\d0/d09086a0794cf3070f12e742f27126254b4e2b5a 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 381

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.config.settings(381,).favicon'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'website(1,).favicon'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1522

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1522,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1522

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1522,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:52:39,474 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:39] "POST /web/dataset/call_kw/res.config.settings/read HTTP/1.1" 200 - 72 0.258 0.081
2025-09-13 10:52:39,499 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:39] "POST /web/dataset/call_kw/res.lang/read HTTP/1.1" 200 - 3 0.007 0.004
2025-09-13 10:52:39,930 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:39] "POST /web/action/load HTTP/1.1" 200 - 56 0.091 0.020
2025-09-13 10:52:40,530 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:40] "POST /web/dataset/call_kw/res.users/get_views HTTP/1.1" 200 - 156 0.259 0.187
2025-09-13 10:52:40,620 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:40] "POST /web/dataset/call_kw/res.users/web_search_read HTTP/1.1" 200 - 15 0.048 0.023
2025-09-13 10:52:40,857 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:40] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 2 0.000 0.000
2025-09-13 10:52:41,349 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:52:41,349 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:41] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 9 0.016 0.000
2025-09-13 10:52:41,602 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:52:41,608 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:41] "GET /web/image/res.company/1/favicon HTTP/1.1" 500 - 4 0.009 0.009
2025-09-13 10:52:43,148 80756 ERROR ardano_live7 odoo.sql_db: bad query: SELECT "hr_employee".id FROM "hr_employee" WHERE (("hr_employee"."company_id" in (1, 1)) AND ("hr_employee"."user_id" in (2))) AND (("hr_employee"."company_id" in (1)) OR "hr_employee"."company_id" IS NULL) ORDER BY  COALESCE("hr_employee"."name"->>'ar_EG', "hr_employee"."name"->>'en_US')  
ERROR: operator does not exist: character varying ->> unknown
LINE 1: ..." IS NULL) ORDER BY  COALESCE("hr_employee"."name"->>'ar_EG'...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
 
2025-09-13 10:52:43,189 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1889, in dispatch
    result = self.request.registry['ir.http']._dispatch(endpoint)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\dataset.py", line 42, in call_kw
    return self._call_kw(model, method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\dataset.py", line 33, in _call_kw
    return call_kw(request.env[model], method, args, kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 468, in call_kw
    result = _call_kw_multi(method, model, args, kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 453, in _call_kw_multi
    result = method(recs, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\res_users.py", line 1854, in read
    res = super(UsersView, self).read(other_fields, load=load)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\res_users.py", line 632, in read
    return super(Users, self).read(fields=fields, load=load)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 3019, in read
    self._read(stored_fields)
  File "C:\odoo16\server\odoo\addons\base\models\res_users.py", line 530, in _read
    super(Users, self)._read(fields)
  File "C:\odoo16\server\odoo\models.py", line 3338, in _read
    field.read(fetched)
  File "C:\odoo16\server\odoo\fields.py", line 4391, in read
    lines = comodel.search(domain)
            ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 1527, in search
    return res if count else self.browse(res)
                             ^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\models.py", line 5167, in browse
    if not ids:
           ^^^
  File "C:\odoo16\server\odoo\tools\query.py", line 217, in __bool__
    return bool(self._result)
                ^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\func.py", line 28, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\query.py", line 210, in _result
    self._cr.execute(query_str, params)
  File "C:\odoo16\server\odoo\sql_db.py", line 321, in execute
    res = self._obj.execute(query, params)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedFunction: operator does not exist: character varying ->> unknown
LINE 1: ..." IS NULL) ORDER BY  COALESCE("hr_employee"."name"->>'ar_EG'...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

2025-09-13 10:52:43,190 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:43] "POST /web/dataset/call_kw/res.users/read HTTP/1.1" 200 - 6 0.002 0.076
2025-09-13 10:52:43,568 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:52:43] "POST /web/dataset/call_kw/res.users/web_search_read HTTP/1.1" 200 - 8 0.010 0.036
2025-09-13 10:53:10,252 80756 INFO ardano_live7 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo16\sessions\filestore\ardano_live7\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\tools\cache.py", line 85, in lookup
    r = d[key]
        ~^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang.get_available at 0x0000023EFA3491C0>, (1,))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 3

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.lang(3,).flag_image_url'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 3

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'res.lang(3,).flag_image'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1,).datas'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\api.py", line 997, in get
    cache_value = field_cache[record._ids[0]]
                  ~~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyError: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\fields.py", line 1161, in __get__
    value = env.cache.get(record, self)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\api.py", line 1004, in get
    raise CacheMiss(record, field)
odoo.exceptions.CacheMiss: 'ir.attachment(1,).raw'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\addons\base\models\ir_attachment.py", line 121, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo16\\sessions\\filestore\\ardano_live7\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-09-13 10:53:10,276 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:10] "GET /web/session/logout HTTP/1.1" 303 - 12 0.034 0.019
2025-09-13 10:53:10,573 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:10] "GET /web HTTP/1.1" 303 - 5 0.016 0.015
2025-09-13 10:53:10,902 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:10] "GET /web/login HTTP/1.1" 303 - 1 0.003 0.005
2025-09-13 10:53:12,093 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:12] "GET /ar/web/login HTTP/1.1" 200 - 174 0.194 0.746
2025-09-13 10:53:12,252 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/37/37db4d649b09ca55779e81bc4d6d7eebca017503'
2025-09-13 10:53:12,259 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:12] "GET /web/image/website/1/logo/My%20Website?unique=993eabb HTTP/1.1" 500 - 5 0.016 0.017
2025-09-13 10:53:12,532 80756 ERROR ardano_live7 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1657, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1685, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1799, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_http.py", line 237, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_http.py", line 154, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 734, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\web\controllers\binary.py", line 145, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 489, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo16\\sessions\\filestore\\ardano_live7/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-09-13 10:53:12,547 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:12] "GET /web/image/website/1/favicon?unique=993eabb HTTP/1.1" 500 - 4 0.000 0.015
2025-09-13 10:53:12,564 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:12] "GET /website/translations/9eb369cb198cd3750374c9d1bbef135c3e9f4542?lang=ar_001 HTTP/1.1" 303 - 1 0.000 0.000
2025-09-13 10:53:12,676 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:12] "GET /website/translations/9eb369cb198cd3750374c9d1bbef135c3e9f4542?lang=ar_001 HTTP/1.1" 303 - 1 0.000 0.000
2025-09-13 10:53:12,911 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:12] "GET /base/static/img/country_flags/001.png?height=25 HTTP/1.1" 303 - 2 0.000 0.011
2025-09-13 10:53:13,391 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:13] "GET /ar/base/static/img/country_flags/001.png?height=25 HTTP/1.1" 404 - 34 0.037 0.124
2025-09-13 10:53:23,514 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=fania_1 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,514 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi18 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,514 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_7 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,514 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_4 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,514 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_5 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,514 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_6 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,523 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,525 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_ERP host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,527 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_benghazi host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,528 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_1 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,530 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_2 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,534 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_main host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,537 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptous_1 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,539 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_tesing host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,542 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_testing host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,546 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=linda host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,550 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=martinlion_2 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,552 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=matrinlive host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,554 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moaslive_6 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,556 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,556 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass2025 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,558 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass3 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,561 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=41/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass5 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,563 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=40/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass6 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,565 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=39/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moassLive25_2 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,567 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=38/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,567 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=37/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_2 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,567 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=36/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,574 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=35/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_7 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,577 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=34/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_latest host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,577 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=33/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,579 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=32/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,579 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=31/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18_cubes host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,583 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=30/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_1 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,585 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=29/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_2 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,587 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=28/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,587 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=27/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18live host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,587 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=26/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=t18_8 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,593 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=25/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=taibat host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,593 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=24/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,596 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=23/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,598 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=22/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test18 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,600 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=21/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test1818 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,600 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=20/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,600 80756 INFO ardano_live7 odoo.sql_db: ConnectionPool(used=3/count=19/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_pos_18 host=localhost port=5432 application_name=odoo-80756 sslmode=prefer' 
2025-09-13 10:53:23,625 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:23] "GET /web/database/selector HTTP/1.1" 200 - 235 2.766 7.055
2025-09-13 10:53:30,327 80756 INFO ardano_live7 werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:30] "GET /web?db=aradno_travel HTTP/1.1" 302 - 1 0.000 0.071
2025-09-13 10:53:30,739 80756 INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-13 10:53:30,752 80756 INFO ? odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-13 10:53:30,783 80756 INFO ? odoo.modules.loading: loading 153 modules... 
2025-09-13 10:53:31,326 80756 INFO ? odoo.modules.loading: 153 modules loaded in 0.54s, 0 queries (+0 extra) 
2025-09-13 10:53:31,474 80756 WARNING ? odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:53:31,474 80756 WARNING ? odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:53:31,474 80756 WARNING ? odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:53:31,474 80756 WARNING ? odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:53:31,474 80756 WARNING ? odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:53:31,474 80756 WARNING ? odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:53:31,776 80756 INFO ? odoo.modules.loading: Modules loaded. 
2025-09-13 10:53:31,796 80756 INFO ? odoo.modules.registry: Registry loaded in 1.212s 
2025-09-13 10:53:31,808 80756 INFO aradno_travel odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-09-13 10:53:32,164 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:32] "GET /web?db=aradno_travel HTTP/1.1" 303 - 24 0.207 1.384
2025-09-13 10:53:33,056 80756 INFO aradno_travel odoo.addons.base.models.assetsbundle: Failed to find attachment for assets /web/assets/%-8ede46f/1/web.assets_frontend.min.css 
2025-09-13 10:53:36,339 80756 INFO aradno_travel odoo.addons.base.models.assetsbundle: Deleting ir.attachment [960] (from bundle web.assets_frontend) 
2025-09-13 10:53:36,999 80756 INFO aradno_travel odoo.addons.base.models.assetsbundle: Failed to find attachment for assets /web/assets/%-8023350/1/web.assets_frontend_lazy.min.js 
2025-09-13 10:53:39,436 80756 INFO aradno_travel odoo.addons.base.models.assetsbundle: Deleting ir.attachment [961] (from bundle web.assets_frontend_lazy) 
2025-09-13 10:53:39,631 80756 INFO aradno_travel odoo.modules.registry: At least one model cache has been invalidated, signaling through the database. 
2025-09-13 10:53:39,672 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:39] "GET /web/login HTTP/1.1" 200 - 176 0.878 6.305
2025-09-13 10:53:39,710 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:39] "GET /web/assets/983-8ede46f/1/web.assets_frontend.min.css HTTP/1.1" 200 - 6 0.007 0.023
2025-09-13 10:53:40,041 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:40] "GET /web/assets/369-aafacf8/1/web.assets_frontend_minimal.min.js HTTP/1.1" 200 - 4 0.000 0.056
2025-09-13 10:53:40,082 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:40] "GET /web/image/website/1/logo/My%20Website?unique=832e5d2 HTTP/1.1" 200 - 5 0.000 0.020
2025-09-13 10:53:40,387 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:40] "GET /web/assets/984-8023350/1/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 4 0.005 0.008
2025-09-13 10:53:40,427 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:40] "GET /web/image/website/1/favicon?unique=832e5d2 HTTP/1.1" 200 - 6 0.010 0.043
2025-09-13 10:53:40,866 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:40] "GET /website/translations/5468d8450c6b91798fe0746e756df0c12eb7f27a?lang=en_US HTTP/1.1" 200 - 4 0.016 0.011
2025-09-13 10:53:42,861 80756 INFO aradno_travel odoo.addons.base.models.res_users: Login successful for db:aradno_travel login:admin from 127.0.0.1 
2025-09-13 10:53:42,925 80756 INFO aradno_travel odoo.modules.registry: At least one model cache has been invalidated, signaling through the database. 
2025-09-13 10:53:42,925 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:42] "POST /web/login HTTP/1.1" 303 - 56 0.129 0.521
2025-09-13 10:53:44,373 80756 INFO aradno_travel odoo.addons.base.models.assetsbundle: Failed to find attachment for assets /web/assets/%-dbf62b1/web.assets_backend.min.css 
2025-09-13 10:53:50,690 80756 INFO aradno_travel odoo.addons.base.models.assetsbundle: Deleting ir.attachment [975, 957] (from bundle web.assets_backend) 
2025-09-13 10:53:51,835 80756 INFO aradno_travel odoo.addons.base.models.assetsbundle: Failed to find attachment for assets /web/assets/%-dbf62b1/web.assets_backend.min.js 
2025-09-13 10:53:57,015 80756 INFO aradno_travel odoo.addons.base.models.assetsbundle: Deleting ir.attachment [976, 959] (from bundle web.assets_backend) 
2025-09-13 10:53:57,189 80756 INFO aradno_travel odoo.modules.registry: At least one model cache has been invalidated, signaling through the database. 
2025-09-13 10:53:57,189 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:57] "GET /web HTTP/1.1" 200 - 155 0.765 13.484
2025-09-13 10:53:57,715 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:57] "GET /web/assets/985-dbf62b1/web.assets_backend.min.css HTTP/1.1" 200 - 9 0.004 0.180
2025-09-13 10:53:57,724 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:57] "GET /web/webclient/translations/fb2e5d49ed91a302caca3233dab1c0a22698f713?lang=en_US HTTP/1.1" 200 - 2 0.000 0.188
2025-09-13 10:53:57,752 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:57] "GET /web/assets/956-0a3a6ec/web.assets_common.min.css HTTP/1.1" 200 - 4 0.016 0.205
2025-09-13 10:53:57,894 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:57] "GET /web/webclient/load_menus/388c85cb5f26cc49d734df9e3fedc7191bc822c146b1e924cf4ce94382289817 HTTP/1.1" 200 - 34 0.109 0.251
2025-09-13 10:53:57,925 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:57] "GET /web/assets/986-dbf62b1/web.assets_backend.min.js HTTP/1.1" 200 - 4 0.001 0.137
2025-09-13 10:53:57,980 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:57] "GET /web/assets/958-0a3a6ec/web.assets_common.min.js HTTP/1.1" 200 - 9 0.008 0.440
2025-09-13 10:53:58,119 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:53:58] "GET /web/assets/366-f740d57/web.assets_backend_prod_only.min.js HTTP/1.1" 200 - 4 0.007 0.029
2025-09-13 10:54:00,397 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:54:00] "GET /bus/websocket_worker_bundle?v=1.0.5 HTTP/1.1" 200 - 5 0.004 0.050
2025-09-13 10:54:01,172 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:54:01] "GET /websocket HTTP/1.1" 101 - 1 0.000 0.011
2025-09-13 10:54:01,253 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:54:01] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 50 0.147 0.014
2025-09-13 10:54:01,278 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:54:01] "POST /mail/init_messaging HTTP/1.1" 200 - 56 0.173 0.013
2025-09-13 10:54:01,375 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:54:01] "POST /mail/load_message_failures HTTP/1.1" 200 - 10 0.030 0.000
2025-09-13 10:54:02,776 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Invoice OCR: Parse Invoices`. 
2025-09-13 10:54:02,792 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Invoice OCR: Parse Invoices` done. 
2025-09-13 10:54:02,808 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Procurement: run scheduler`. 
2025-09-13 10:54:03,093 80756 INFO aradno_travel odoo.addons.stock.models.stock_orderpoint: A batch of 4 orderpoints is processed and committed 
2025-09-13 10:54:03,119 80756 INFO aradno_travel odoo.addons.stock.models.stock_rule: A batch of 4 moves are assigned and committed 
2025-09-13 10:54:03,129 80756 INFO aradno_travel odoo.addons.stock.models.stock_rule: _run_scheduler_tasks is finished and committed 
2025-09-13 10:54:03,129 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Procurement: run scheduler` done. 
2025-09-13 10:54:03,156 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `payment: post-process transactions`. 
2025-09-13 10:54:03,166 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `payment: post-process transactions` done. 
2025-09-13 10:54:03,173 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Digest Emails`. 
2025-09-13 10:54:03,557 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Digest Emails` done. 
2025-09-13 10:54:03,567 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Notification: Delete Notifications older than 6 Month`. 
2025-09-13 10:54:03,571 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Notification: Delete Notifications older than 6 Month` done. 
2025-09-13 10:54:03,577 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Base: Auto-vacuum internal data`. 
2025-09-13 10:54:03,636 80756 INFO aradno_travel odoo.addons.base.models.ir_attachment: filestore gc 10 checked, 4 removed 
2025-09-13 10:54:03,650 80756 INFO aradno_travel odoo.models.unlink: User #1 deleted ir.cron.trigger records with IDs: [1, 2] 
2025-09-13 10:54:03,757 80756 INFO aradno_travel odoo.addons.base.models.res_users: GC'd 1 user log entries 
2025-09-13 10:54:03,810 80756 INFO aradno_travel odoo.addons.auth_totp.models.auth_totp: GC'd 0 totp devices entries 
2025-09-13 10:54:03,840 80756 INFO aradno_travel odoo.models.unlink: User #1 deleted bus.bus records with IDs: [713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 820, 821, 822, 823, 824] 
2025-09-13 10:54:04,452 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Base: Auto-vacuum internal data` done. 
2025-09-13 10:54:04,458 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Project: Create Recurring Tasks`. 
2025-09-13 10:54:04,458 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Project: Create Recurring Tasks` done. 
2025-09-13 10:54:04,469 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Users: Notify About Unregistered Users`. 
2025-09-13 10:54:04,473 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Users: Notify About Unregistered Users` done. 
2025-09-13 10:54:04,473 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Account: Post draft entries with auto_post enabled and accounting date up to today`. 
2025-09-13 10:54:04,499 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Account: Post draft entries with auto_post enabled and accounting date up to today` done. 
2025-09-13 10:54:04,505 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Website Visitor : clean inactive visitors`. 
2025-09-13 10:54:04,505 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Website Visitor : clean inactive visitors` done. 
2025-09-13 10:54:04,505 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Invoice OCR: Update All Status`. 
2025-09-13 10:54:04,521 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Invoice OCR: Update All Status` done. 
2025-09-13 10:54:04,541 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Invoice OCR: Validate Invoices`. 
2025-09-13 10:54:04,541 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Invoice OCR: Validate Invoices` done. 
2025-09-13 10:54:04,554 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Calendar: Event Reminder`. 
2025-09-13 10:54:04,569 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Calendar: Event Reminder` done. 
2025-09-13 10:54:04,578 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Mail: Email Queue Manager`. 
2025-09-13 10:54:04,584 80756 WARNING aradno_travel odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using "My Company (San Francisco)" <<EMAIL>> as fallback 
2025-09-13 10:54:04,584 80756 WARNING aradno_travel odoo.addons.base.models.ir_mail_server: The from filter of the CLI configuration does not match the notification email or the user email, using "My Company (San Francisco)" <<EMAIL>> as fallback 
2025-09-13 10:54:08,681 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Mail: Email Queue Manager` done. 
2025-09-13 10:54:08,700 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Account: Journal online sync`. 
2025-09-13 10:54:08,700 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Account: Journal online sync` done. 
2025-09-13 10:54:08,723 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Gamification: Karma tracking consolidation`. 
2025-09-13 10:54:08,740 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Gamification: Karma tracking consolidation` done. 
2025-09-13 10:54:08,745 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Account Report Followup; Execute followup`. 
2025-09-13 10:54:08,937 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Account Report Followup; Execute followup` done. 
2025-09-13 10:54:08,953 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Notification: Send scheduled message notifications`. 
2025-09-13 10:54:08,970 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Notification: Send scheduled message notifications` done. 
2025-09-13 10:54:08,982 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Currency: rate update`. 
2025-09-13 10:54:08,985 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Currency: rate update` done. 
2025-09-13 10:54:09,001 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Account automatic transfers : Perform transfers`. 
2025-09-13 10:54:09,001 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Account automatic transfers : Perform transfers` done. 
2025-09-13 10:54:09,017 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Snailmail: process letters queue`. 
2025-09-13 10:54:09,017 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Snailmail: process letters queue` done. 
2025-09-13 10:54:09,033 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Try to reconcile automatically your statement lines`. 
2025-09-13 10:54:09,506 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Try to reconcile automatically your statement lines` done. 
2025-09-13 10:54:09,535 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `SMS: SMS Queue Manager`. 
2025-09-13 10:54:09,544 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `SMS: SMS Queue Manager` done. 
2025-09-13 10:54:09,544 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,578 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,578 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,581 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,581 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,585 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,585 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,585 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,591 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,591 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,591 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,591 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,591 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,591 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,591 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,591 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,591 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,606 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,606 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,606 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,606 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,606 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,606 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,606 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,606 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,606 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,622 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,622 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,622 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,622 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,622 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,622 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,622 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,638 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,638 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,638 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,638 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,638 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,638 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,654 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,654 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,654 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,654 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,654 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,654 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,654 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,654 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,670 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,670 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,670 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,670 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,670 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,681 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,681 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,686 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,686 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,686 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,686 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,686 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,686 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,686 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,702 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,702 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,702 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,702 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,702 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,702 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,702 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,718 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,718 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,718 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,718 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,718 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,718 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,718 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,734 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,734 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,734 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,734 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,734 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,744 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,744 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,744 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,750 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,750 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,750 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,750 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,750 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,797 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Project: Send rating`. 
2025-09-13 10:54:09,797 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,797 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Project: Send rating` done. 
2025-09-13 10:54:09,813 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,813 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,818 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,820 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,823 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,825 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,826 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,829 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,829 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,836 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,836 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,836 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,845 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,848 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,848 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,848 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,848 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Disable unused snippets assets`. 
2025-09-13 10:54:09,848 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,861 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,861 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,866 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,866 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,872 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,872 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,872 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,877 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,881 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Purchase reminder`. 
2025-09-13 10:54:09,881 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Purchase reminder` done. 
2025-09-13 10:54:09,893 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Partner Autocomplete : Sync with remote DB`. 
2025-09-13 10:54:09,893 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Partner Autocomplete : Sync with remote DB` done. 
2025-09-13 10:54:09,909 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Planning: generate next recurring shifts`. 
2025-09-13 10:54:10,074 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Planning: generate next recurring shifts` done. 
2025-09-13 10:54:10,084 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Gamification: Goal Challenge Check`. 
2025-09-13 10:54:10,162 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Gamification: Goal Challenge Check` done. 
2025-09-13 10:54:10,162 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `HR Employee: check work permit validity`. 
2025-09-13 10:54:10,169 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `HR Employee: check work permit validity` done. 
2025-09-13 10:54:10,182 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Mail Marketing: Process queue`. 
2025-09-13 10:54:10,186 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Mail Marketing: Process queue` done. 
2025-09-13 10:54:10,195 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `Publisher: Update Notification`. 
2025-09-13 10:54:10,716 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Publisher: Update Notification` done. 
2025-09-13 10:54:10,716 80756 INFO aradno_travel odoo.modules.registry: At least one model cache has been invalidated, signaling through the database. 
2025-09-13 10:54:11,114 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `Disable unused snippets assets` done. 
2025-09-13 10:55:41,679 80756 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:41] "GET /mail/static/src/audio/ting.ogg HTTP/1.1" 206 - 0 0.000 0.005
2025-09-13 10:55:41,710 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:41] "GET /web/image/res.company/1/favicon HTTP/1.1" 200 - 16 0.023 0.028
2025-09-13 10:55:41,761 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:41] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 7 0.012 0.034
2025-09-13 10:55:41,930 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:41] "GET /web/image/res.company/1/favicon HTTP/1.1" 304 - 5 0.004 0.007
2025-09-13 10:55:42,034 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:42] "GET /web/image/res.company/1/favicon HTTP/1.1" 304 - 5 0.006 0.001
2025-09-13 10:55:42,259 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:42] "GET /web/image/res.company/1/favicon HTTP/1.1" 304 - 5 0.008 0.003
2025-09-13 10:55:50,019 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:50] "POST /web/action/load HTTP/1.1" 200 - 15 0.049 0.008
2025-09-13 10:55:50,450 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:50] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 46 0.056 0.054
2025-09-13 10:55:50,674 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:50] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 48 0.050 0.024
2025-09-13 10:55:50,798 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:50] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.000 0.016
2025-09-13 10:55:56,694 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:56] "GET /web?debug=1 HTTP/1.1" 200 - 96 0.106 0.808
2025-09-13 10:55:56,727 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:56] "GET /web/webclient/load_menus/0857c6f11e0f584561da55f842e668c3365b8b02f5a0543dfb88908db2051dc1 HTTP/1.1" 200 - 1 0.000 0.020
2025-09-13 10:55:57,667 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:57] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 7 0.000 0.024
2025-09-13 10:55:57,680 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:57] "POST /web/action/load HTTP/1.1" 200 - 10 0.024 0.013
2025-09-13 10:55:57,730 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:57] "GET /bus/websocket_worker_bundle?v=1.0.5 HTTP/1.1" 304 - 5 0.015 0.008
2025-09-13 10:55:58,058 80756 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:58] "GET /mail/static/src/audio/ting.ogg HTTP/1.1" 206 - 0 0.000 0.007
2025-09-13 10:55:58,068 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:58] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 3 0.015 0.020
2025-09-13 10:55:58,080 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:58] "GET /websocket HTTP/1.1" 101 - 1 0.001 0.014
2025-09-13 10:55:58,090 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:58] "POST /mail/init_messaging HTTP/1.1" 200 - 52 0.167 0.016
2025-09-13 10:55:58,123 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:58] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 49 0.062 0.027
2025-09-13 10:55:58,168 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:58] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 46 0.031 0.050
2025-09-13 10:55:58,443 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:58] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.012 0.022
2025-09-13 10:55:58,463 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:55:58] "POST /mail/load_message_failures HTTP/1.1" 200 - 10 0.027 0.027
2025-09-13 10:56:00,073 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:00] "POST /web/action/load HTTP/1.1" 200 - 10 0.011 0.009
2025-09-13 10:56:00,363 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:00] "POST /web/dataset/call_kw/base.module.update/get_views HTTP/1.1" 200 - 9 0.037 0.011
2025-09-13 10:56:00,403 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:00] "POST /web/dataset/call_kw/base.module.update/onchange HTTP/1.1" 200 - 2 0.005 0.004
2025-09-13 10:56:01,514 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:01] "POST /web/dataset/call_kw/base.module.update/create HTTP/1.1" 200 - 4 0.011 0.013
2025-09-13 10:56:01,833 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:01] "POST /web/dataset/call_kw/base.module.update/read HTTP/1.1" 200 - 3 0.009 0.000
2025-09-13 10:56:02,092 80756 INFO aradno_travel odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user admin #2 via 127.0.0.1 
2025-09-13 10:56:04,355 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:04] "POST /web/dataset/call_button HTTP/1.1" 200 - 2048 1.774 0.494
2025-09-13 10:56:08,842 80756 INFO aradno_travel odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_upgrade on ['Travel Agent Management'] to user admin #2 via 127.0.0.1 
2025-09-13 10:56:08,842 80756 INFO aradno_travel odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Travel Agent Management'] to user admin #2 via 127.0.0.1 
2025-09-13 10:56:08,844 80756 INFO aradno_travel odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Travel Agent Management'] to user admin #2 via 127.0.0.1 
2025-09-13 10:56:10,672 80756 INFO aradno_travel odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user admin #2 via 127.0.0.1 
2025-09-13 10:56:10,748 80756 INFO aradno_travel odoo.modules.loading: loading 1 modules... 
2025-09-13 10:56:10,754 80756 INFO aradno_travel odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-09-13 10:56:10,757 80756 INFO aradno_travel odoo.modules.loading: updating modules list 
2025-09-13 10:56:10,757 80756 INFO aradno_travel odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-09-13 10:56:13,840 80756 INFO aradno_travel odoo.modules.loading: loading 153 modules... 
2025-09-13 10:56:14,494 80756 INFO aradno_travel odoo.modules.loading: Loading module travel_agent_management (147/153) 
2025-09-13 10:56:15,285 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:15,285 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:15,285 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:15,285 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:15,285 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:15,285 80756 WARNING aradno_travel odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:15,434 80756 INFO aradno_travel odoo.modules.registry: module travel_agent_management: creating or updating database tables 
2025-09-13 10:56:15,814 80756 INFO aradno_travel odoo.models: Prepare computation of transfer.request.partner_id 
2025-09-13 10:56:15,855 80756 INFO aradno_travel odoo.models: Prepare computation of hotel.booking.request.partner_id 
2025-09-13 10:56:15,894 80756 INFO aradno_travel odoo.models: Prepare computation of medical.insurance.request.partner_id 
2025-09-13 10:56:15,914 80756 WARNING aradno_travel odoo.addons.base.models.ir_model: Two fields (departure_date, travel_date) of travel.ticket() have the same label: Departure Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 10:56:15,918 80756 WARNING aradno_travel odoo.addons.base.models.ir_model: Two fields (return_date, return_travel_date) of travel.ticket() have the same label: Return Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 10:56:15,918 80756 WARNING aradno_travel odoo.addons.base.models.ir_model: Two fields (field_type, field_type_id) of visa.request.field() have the same label: Field Type. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 10:56:16,674 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/security/security_groups.xml 
2025-09-13 10:56:16,925 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/security/ir.model.access.csv 
2025-09-13 10:56:17,469 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/security/portal_security.xml 
2025-09-13 10:56:17,624 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/data/sequences.xml 
2025-09-13 10:56:17,638 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/data/visa_sequences.xml 
2025-09-13 10:56:17,639 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/data/transfer_sequence.xml 
2025-09-13 10:56:17,642 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/data/hotel_sequence.xml 
2025-09-13 10:56:17,642 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/data/insurance_sequence.xml 
2025-09-13 10:56:17,656 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/ticket_request_views.xml 
2025-09-13 10:56:17,742 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/passenger_views.xml 
2025-09-13 10:56:17,858 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/passenger_line_views.xml 
2025-09-13 10:56:17,907 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/flight_segment_views.xml 
2025-09-13 10:56:18,001 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/travel_ticket_views.xml 
2025-09-13 10:56:18,107 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/menu_views.xml 
2025-09-13 10:56:18,215 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/visa_country_views.xml 
2025-09-13 10:56:18,327 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-globe fa-2x) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\visa_country_views.xml',
 'line': 12,
 'name': 'visa.country.kanban',
 'view': ir.ui.view(2509,),
 'view.model': 'visa.country',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_visa_country_kanban'} 
2025-09-13 10:56:18,384 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/visa_document_type_views.xml 
2025-09-13 10:56:18,526 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/visa_field_type_views.xml 
2025-09-13 10:56:18,657 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/visa_type_views.xml 
2025-09-13 10:56:18,851 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/visa_request_views.xml 
2025-09-13 10:56:19,126 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/visa_menu_views.xml 
2025-09-13 10:56:19,286 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/transfer_request_views.xml 
2025-09-13 10:56:19,364 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-route) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 36,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 10:56:19,364 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 39,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 10:56:19,364 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 40,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 10:56:19,364 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 43,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 10:56:19,406 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/hotel_booking_views.xml 
2025-09-13 10:56:19,558 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-building) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 37,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 10:56:19,558 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 40,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 10:56:19,558 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-moon-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 43,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 10:56:19,558 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 44,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 10:56:19,614 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/insurance_views.xml 
2025-09-13 10:56:19,678 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-shield) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 37,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 10:56:19,678 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 40,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 10:56:19,678 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 43,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 10:56:19,678 80756 WARNING aradno_travel odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-tag) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 45,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 10:56:19,717 80756 INFO aradno_travel odoo.modules.loading: loading travel_agent_management/views/portal_templates.xml 
2025-09-13 10:56:20,965 80756 INFO aradno_travel odoo.modules.loading: Module travel_agent_management loaded in 6.47s, 1471 queries (+1471 other) 
2025-09-13 10:56:20,965 80756 INFO aradno_travel odoo.modules.loading: 153 modules loaded in 7.12s, 1471 queries (+1471 extra) 
2025-09-13 10:56:21,244 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:21,244 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:21,244 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:21,244 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:21,247 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:21,247 80756 WARNING aradno_travel odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:21,666 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:21,673 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:21,673 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:21,674 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:21,674 80756 WARNING aradno_travel odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:21,674 80756 WARNING aradno_travel odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 10:56:22,970 80756 INFO aradno_travel odoo.addons.base.models.ir_model: Deleting <EMAIL> (travel_agent_management.portal_my_hotel_booking) 
2025-09-13 10:56:23,657 80756 INFO aradno_travel odoo.models.unlink: User #1 deleted ir.model.data records with IDs: [141042] 
2025-09-13 10:56:23,657 80756 INFO aradno_travel odoo.models.unlink: User #1 deleted ir.ui.view records with IDs: [2586] 
2025-09-13 10:56:23,673 80756 INFO aradno_travel odoo.modules.registry: verifying fields for every extended model 
2025-09-13 10:56:26,623 80756 INFO aradno_travel odoo.modules.loading: Modules loaded. 
2025-09-13 10:56:26,633 80756 INFO aradno_travel odoo.modules.registry: Registry loaded in 15.923s 
2025-09-13 10:56:26,633 80756 INFO aradno_travel odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-09-13 10:56:26,648 80756 INFO aradno_travel odoo.modules.registry: Registry changed, signaling through the database 
2025-09-13 10:56:26,651 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:26] "POST /web/dataset/call_button HTTP/1.1" 200 - 6730 10.763 7.065
2025-09-13 10:56:27,000 80756 INFO aradno_travel odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-09-13 10:56:28,331 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:28] "GET /web HTTP/1.1" 200 - 108 0.143 1.200
2025-09-13 10:56:30,963 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:30] "GET /bus/websocket_worker_bundle?v=1.0.5 HTTP/1.1" 304 - 6 0.002 0.027
2025-09-13 10:56:31,132 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:31] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 8 0.016 0.011
2025-09-13 10:56:31,573 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:31] "GET /websocket HTTP/1.1" 101 - 1 0.000 0.011
2025-09-13 10:56:31,671 80756 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:31] "GET /mail/static/src/audio/ting.ogg HTTP/1.1" 206 - 0 0.000 0.006
2025-09-13 10:56:31,744 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:31] "POST /mail/init_messaging HTTP/1.1" 200 - 53 0.059 0.041
2025-09-13 10:56:31,757 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:31] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 53 0.088 0.026
2025-09-13 10:56:31,956 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:56:31] "POST /mail/load_message_failures HTTP/1.1" 200 - 10 0.027 0.026
2025-09-13 10:57:10,810 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Starting job `payment: post-process transactions`. 
2025-09-13 10:57:10,817 80756 INFO aradno_travel odoo.addons.base.models.ir_cron: Job `payment: post-process transactions` done. 
2025-09-13 10:58:42,561 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:42] "POST /web/action/load HTTP/1.1" 200 - 12 0.005 0.017
2025-09-13 10:58:42,921 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:42] "POST /web/dataset/call_kw/travel.ticket.request/get_views HTTP/1.1" 200 - 47 0.053 0.074
2025-09-13 10:58:42,967 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:42] "POST /web/dataset/call_kw/travel.ticket.request/web_search_read HTTP/1.1" 200 - 7 0.003 0.009
2025-09-13 10:58:43,273 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:43] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 2 0.000 0.000
2025-09-13 10:58:45,674 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:45] "POST /web/dataset/call_kw/travel.ticket.request/read HTTP/1.1" 200 - 10 0.034 0.016
2025-09-13 10:58:45,893 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:45] "POST /web/dataset/call_kw/res.partner/read HTTP/1.1" 200 - 2 0.000 0.006
2025-09-13 10:58:46,040 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:46] "POST /web/dataset/call_kw/travel.passenger.line/read HTTP/1.1" 200 - 5 0.031 0.003
2025-09-13 10:58:46,234 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:46] "POST /mail/thread/messages HTTP/1.1" 200 - 25 0.075 0.035
2025-09-13 10:58:46,488 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:46] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.032 0.025
2025-09-13 10:58:46,498 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:46] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 200 - 7 0.007 0.017
2025-09-13 10:58:46,648 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:46] "GET /web/image/res.partner/3/avatar_128 HTTP/1.1" 200 - 7 0.009 0.020
2025-09-13 10:58:48,126 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:48] "POST /web/dataset/call_kw/travel.passenger.line/read HTTP/1.1" 200 - 9 0.026 0.009
2025-09-13 10:58:48,353 80756 INFO aradno_travel werkzeug: 127.0.0.1 - - [13/Sep/2025 10:58:48] "POST /web/dataset/call_kw/travel.ticket/read HTTP/1.1" 200 - 3 0.008 0.008
2025-09-13 11:02:55,841 73168 INFO ? odoo: Odoo version 16.0-20250210 
2025-09-13 11:02:55,841 73168 INFO ? odoo: Using configuration file at C:\odoo16\server\odoo.conf 
2025-09-13 11:02:55,841 73168 INFO ? odoo: addons paths: ['C:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\sessions\\addons\\16.0', 'c:\\odoo16\\server\\odoo\\addons', 'c:\\odoo16\\server\\enterprise16', 'c:\\odoo16\\server\\ardanoholding_testing5'] 
2025-09-13 11:02:55,841 73168 INFO ? odoo: database: openpg@localhost:5432 
2025-09-13 11:02:56,162 73168 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo16\thirdparty\wkhtmltopdf.exe 
2025-09-13 11:02:57,787 73168 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8016 
2025-09-13 11:02:59,559 73168 INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-13 11:02:59,579 73168 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-09-13 11:02:59,802 73168 INFO ? odoo.modules.loading: loading 153 modules... 
2025-09-13 11:02:59,830 73168 WARNING ? odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-09-13 11:03:02,295 73168 INFO ? odoo.modules.loading: Loading module travel_agent_management (147/153) 
2025-09-13 11:03:02,512 73168 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.ticket_request is not overriding the create method in batch 
2025-09-13 11:03:02,519 73168 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.travel_ticket is not overriding the create method in batch 
2025-09-13 11:03:02,531 73168 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.visa_models is not overriding the create method in batch 
2025-09-13 11:03:02,531 73168 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.visa_models is not overriding the create method in batch 
2025-09-13 11:03:02,533 73168 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.transfer_models is not overriding the create method in batch 
2025-09-13 11:03:02,533 73168 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.hotel_models is not overriding the create method in batch 
2025-09-13 11:03:02,540 73168 WARNING ? odoo.api.create: The model odoo.addons.travel_agent_management.models.insurance_models is not overriding the create method in batch 
2025-09-13 11:03:02,639 73168 WARNING ? odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:03:02,639 73168 WARNING ? odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:03:02,639 73168 WARNING ? odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:03:02,639 73168 WARNING ? odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:03:02,639 73168 WARNING ? odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:03:02,639 73168 WARNING ? odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:03:02,658 73168 INFO ? odoo.modules.registry: module travel_agent_management: creating or updating database tables 
2025-09-13 11:03:02,736 73168 WARNING ? odoo.addons.base.models.ir_model: Two fields (departure_date, travel_date) of travel.ticket() have the same label: Departure Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:03:02,736 73168 WARNING ? odoo.addons.base.models.ir_model: Two fields (return_date, return_travel_date) of travel.ticket() have the same label: Return Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:03:02,736 73168 WARNING ? odoo.addons.base.models.ir_model: Two fields (field_type, field_type_id) of visa.request.field() have the same label: Field Type. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:03:02,924 73168 INFO ? odoo.modules.loading: loading travel_agent_management/security/security_groups.xml 
2025-09-13 11:03:03,035 73168 INFO ? odoo.modules.loading: loading travel_agent_management/security/ir.model.access.csv 
2025-09-13 11:03:03,116 73168 INFO ? odoo.modules.loading: loading travel_agent_management/security/portal_security.xml 
2025-09-13 11:03:03,199 73168 INFO ? odoo.modules.loading: loading travel_agent_management/data/sequences.xml 
2025-09-13 11:03:03,202 73168 INFO ? odoo.modules.loading: loading travel_agent_management/data/visa_sequences.xml 
2025-09-13 11:03:03,205 73168 INFO ? odoo.modules.loading: loading travel_agent_management/data/transfer_sequence.xml 
2025-09-13 11:03:03,209 73168 INFO ? odoo.modules.loading: loading travel_agent_management/data/hotel_sequence.xml 
2025-09-13 11:03:03,212 73168 INFO ? odoo.modules.loading: loading travel_agent_management/data/insurance_sequence.xml 
2025-09-13 11:03:03,215 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/ticket_request_views.xml 
2025-09-13 11:03:03,265 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/passenger_views.xml 
2025-09-13 11:03:03,291 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/passenger_line_views.xml 
2025-09-13 11:03:03,306 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/flight_segment_views.xml 
2025-09-13 11:03:03,327 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/travel_ticket_views.xml 
2025-09-13 11:03:03,358 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/menu_views.xml 
2025-09-13 11:03:03,397 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_country_views.xml 
2025-09-13 11:03:03,428 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-globe fa-2x) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\visa_country_views.xml',
 'line': 12,
 'name': 'visa.country.kanban',
 'view': ir.ui.view(2509,),
 'view.model': 'visa.country',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_visa_country_kanban'} 
2025-09-13 11:03:03,439 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_document_type_views.xml 
2025-09-13 11:03:03,461 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_field_type_views.xml 
2025-09-13 11:03:03,474 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_type_views.xml 
2025-09-13 11:03:03,510 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_request_views.xml 
2025-09-13 11:03:03,574 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_menu_views.xml 
2025-09-13 11:03:03,599 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/transfer_request_views.xml 
2025-09-13 11:03:03,634 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-route) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 36,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:03:03,634 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 39,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:03:03,634 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 40,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:03:03,634 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 43,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:03:03,645 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/hotel_booking_views.xml 
2025-09-13 11:03:03,674 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-building) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 37,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:03:03,674 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 40,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:03:03,674 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-moon-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 43,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:03:03,674 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 44,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:03:03,691 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/insurance_views.xml 
2025-09-13 11:03:03,731 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-shield) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 37,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:03:03,731 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 40,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:03:03,731 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 43,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:03:03,731 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-tag) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 45,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:03:03,744 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/portal_templates.xml 
2025-09-13 11:03:04,047 73168 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-09-13 11:03:04,047 73168 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'aradno_travel'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 305, in load_module_graph
    module.write({'state': 'installed', 'latest_version': ver})
  File "C:\odoo16\server\odoo\addons\website\models\ir_module_module.py", line 78, in write
    if request and request.db and request.context.get('apply_new_theme'):
                                  ^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1291, in context
    return self.env.context
           ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'context'
2025-09-13 11:03:04,074 73168 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 11:03:04] "GET /websocket HTTP/1.1" 404 - 1489 0.871 3.739
2025-09-13 11:03:08,847 73168 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 11:03:08] "GET /websocket HTTP/1.1" 404 - 1 0.056 0.087
2025-09-13 11:03:13,272 73168 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 11:03:13] "GET /web HTTP/1.1" 303 - 2 0.011 0.112
2025-09-13 11:03:14,212 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_accounts_chart', defaulting to LGPL-3 
2025-09-13 11:03:14,212 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_approval', defaulting to LGPL-3 
2025-09-13 11:03:14,212 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_bank', defaulting to LGPL-3 
2025-09-13 11:03:14,212 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_contract_bonus', defaulting to LGPL-3 
2025-09-13 11:03:14,212 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_hr_customization', defaulting to LGPL-3 
2025-09-13 11:03:14,212 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'ardano_user_login_history', defaulting to LGPL-3 
2025-09-13 11:03:14,220 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_accounting_reports', defaulting to LGPL-3 
2025-09-13 11:03:14,220 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_accounting_reports_old', defaulting to LGPL-3 
2025-09-13 11:03:14,224 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_attendance', defaulting to LGPL-3 
2025-09-13 11:03:14,224 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_biotime', defaulting to LGPL-3 
2025-09-13 11:03:14,224 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_biotime_works', defaulting to LGPL-3 
2025-09-13 11:03:14,224 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_inventory', defaulting to LGPL-3 
2025-09-13 11:03:14,227 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_product_custom', defaulting to LGPL-3 
2025-09-13 11:03:14,227 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_sale_order_analytic_account', defaulting to LGPL-3 
2025-09-13 11:03:14,227 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'date_field_no_translation', defaulting to LGPL-3 
2025-09-13 11:03:14,227 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'employee_report_kpi', defaulting to LGPL-3 
2025-09-13 11:03:14,227 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'manufact_customize', defaulting to LGPL-3 
2025-09-13 11:03:14,227 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'project_location', defaulting to LGPL-3 
2025-09-13 11:03:14,240 73168 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'service_provider', defaulting to LGPL-3 
2025-09-13 11:03:16,068 73168 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 11:03:16] "GET /websocket HTTP/1.1" 404 - 1 0.009 0.111
2025-09-13 11:03:24,507 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=fania_1 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,507 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi18 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,511 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_7 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,514 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_4 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,514 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_5 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,518 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_6 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,520 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,520 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_ERP host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,524 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_benghazi host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,527 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_1 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,527 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_2 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,527 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_main host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,527 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptous_1 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,536 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_tesing host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,538 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_testing host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,541 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=linda host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,543 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=martinlion_2 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,545 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=matrinlive host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,548 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moaslive_6 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,550 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,553 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass2025 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,555 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass3 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,557 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=41/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass5 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,559 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=40/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass6 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,561 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=39/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moassLive25_2 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,565 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=38/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,567 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=37/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_2 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,569 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=36/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,571 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=35/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_7 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,573 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=34/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_latest host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,574 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=33/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,578 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=32/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,578 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=31/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18_cubes host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,581 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=30/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_1 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,583 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=29/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_2 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,585 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=28/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,585 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=27/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18live host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,585 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=26/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=t18_8 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,593 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=25/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=taibat host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,594 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=24/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,594 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=23/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,594 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=22/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test18 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,594 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=21/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test1818 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,594 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=20/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,594 73168 INFO ? odoo.sql_db: ConnectionPool(used=2/count=19/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_pos_18 host=localhost port=5432 application_name=odoo-73168 sslmode=prefer' 
2025-09-13 11:03:24,708 73168 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 11:03:24] "GET /web/database/selector HTTP/1.1" 200 - 235 3.058 8.374
2025-09-13 11:04:17,793 73168 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 11:04:17] "GET /web?db=aradno_travel HTTP/1.1" 302 - 1 0.034 0.072
2025-09-13 11:04:18,038 73168 INFO ? odoo.modules.loading: loading 1 modules... 
2025-09-13 11:04:18,058 73168 INFO ? odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-09-13 11:04:18,094 73168 INFO ? odoo.modules.loading: loading 153 modules... 
2025-09-13 11:04:18,266 73168 INFO ? odoo.modules.loading: Loading module travel_agent_management (147/153) 
2025-09-13 11:04:19,220 73168 WARNING ? odoo.fields: Field travel.flight.segment.departure_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:04:19,220 73168 WARNING ? odoo.fields: Field travel.flight.segment.return_route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:04:19,220 73168 WARNING ? odoo.fields: Field travel.flight.segment.flight_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:04:19,220 73168 WARNING ? odoo.fields: Field travel.flight.segment.airline: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:04:19,220 73168 WARNING ? odoo.fields: Field travel.flight.segment.ticket_number: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:04:19,220 73168 WARNING ? odoo.fields: Field travel.ticket.route: unknown parameter 'placeholder', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-09-13 11:04:19,324 73168 INFO ? odoo.modules.registry: module travel_agent_management: creating or updating database tables 
2025-09-13 11:04:19,457 73168 WARNING ? odoo.addons.base.models.ir_model: Two fields (departure_date, travel_date) of travel.ticket() have the same label: Departure Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:04:19,457 73168 WARNING ? odoo.addons.base.models.ir_model: Two fields (return_date, return_travel_date) of travel.ticket() have the same label: Return Date. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:04:19,457 73168 WARNING ? odoo.addons.base.models.ir_model: Two fields (field_type, field_type_id) of visa.request.field() have the same label: Field Type. [Modules: travel_agent_management and travel_agent_management] 
2025-09-13 11:04:19,597 73168 INFO ? odoo.modules.loading: loading travel_agent_management/security/security_groups.xml 
2025-09-13 11:04:19,975 73168 INFO ? odoo.modules.loading: loading travel_agent_management/security/ir.model.access.csv 
2025-09-13 11:04:20,183 73168 INFO ? odoo.modules.loading: loading travel_agent_management/security/portal_security.xml 
2025-09-13 11:04:20,375 73168 INFO ? odoo.modules.loading: loading travel_agent_management/data/sequences.xml 
2025-09-13 11:04:20,384 73168 INFO ? odoo.modules.loading: loading travel_agent_management/data/visa_sequences.xml 
2025-09-13 11:04:20,393 73168 INFO ? odoo.modules.loading: loading travel_agent_management/data/transfer_sequence.xml 
2025-09-13 11:04:20,395 73168 INFO ? odoo.modules.loading: loading travel_agent_management/data/hotel_sequence.xml 
2025-09-13 11:04:20,405 73168 INFO ? odoo.modules.loading: loading travel_agent_management/data/insurance_sequence.xml 
2025-09-13 11:04:20,413 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/ticket_request_views.xml 
2025-09-13 11:04:20,543 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/passenger_views.xml 
2025-09-13 11:04:20,627 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/passenger_line_views.xml 
2025-09-13 11:04:20,669 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/flight_segment_views.xml 
2025-09-13 11:04:20,744 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/travel_ticket_views.xml 
2025-09-13 11:04:20,815 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/menu_views.xml 
2025-09-13 11:04:20,881 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_country_views.xml 
2025-09-13 11:04:20,927 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <span> with fa class (fa fa-globe fa-2x) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\visa_country_views.xml',
 'line': 12,
 'name': 'visa.country.kanban',
 'view': ir.ui.view(2509,),
 'view.model': 'visa.country',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_visa_country_kanban'} 
2025-09-13 11:04:20,958 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_document_type_views.xml 
2025-09-13 11:04:21,027 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_field_type_views.xml 
2025-09-13 11:04:21,092 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_type_views.xml 
2025-09-13 11:04:21,176 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_request_views.xml 
2025-09-13 11:04:21,318 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/visa_menu_views.xml 
2025-09-13 11:04:21,391 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/transfer_request_views.xml 
2025-09-13 11:04:21,488 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-route) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 36,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:04:21,491 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 39,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:04:21,492 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 40,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:04:21,492 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\transfer_request_views.xml',
 'line': 43,
 'name': 'transfer.request.kanban',
 'view': ir.ui.view(2576,),
 'view.model': 'transfer.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_transfer_request_kanban'} 
2025-09-13 11:04:21,523 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/hotel_booking_views.xml 
2025-09-13 11:04:21,616 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-building) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 37,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:04:21,616 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 40,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:04:21,616 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-moon-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 43,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:04:21,616 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-users) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\hotel_booking_views.xml',
 'line': 44,
 'name': 'hotel.booking.request.kanban',
 'view': ir.ui.view(2580,),
 'view.model': 'hotel.booking.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_hotel_booking_request_kanban'} 
2025-09-13 11:04:21,642 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/insurance_views.xml 
2025-09-13 11:04:21,770 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-shield) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 37,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:04:21,770 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-calendar) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 40,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:04:21,775 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-clock-o) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 43,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:04:21,776 73168 WARNING ? odoo.addons.base.models.ir_ui_view: A <i> with fa class (fa fa-tag) must have title in its tag, parents, descendants or have text
View error context:
{'file': 'c:\\odoo16\\server\\ardanoholding_testing5\\travel_agent_management\\views\\insurance_views.xml',
 'line': 45,
 'name': 'medical.insurance.request.kanban',
 'view': ir.ui.view(2584,),
 'view.model': 'medical.insurance.request',
 'view.parent': ir.ui.view(),
 'xmlid': 'view_medical_insurance_request_kanban'} 
2025-09-13 11:04:21,824 73168 INFO ? odoo.modules.loading: loading travel_agent_management/views/portal_templates.xml 
2025-09-13 11:04:22,834 73168 WARNING ? odoo.modules.loading: Transient module states were reset 
2025-09-13 11:04:22,834 73168 ERROR ? odoo.modules.registry: Failed to load registry 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'aradno_travel'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 305, in load_module_graph
    module.write({'state': 'installed', 'latest_version': ver})
  File "C:\odoo16\server\odoo\addons\website\models\ir_module_module.py", line 78, in write
    if request and request.db and request.context.get('apply_new_theme'):
                                  ^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1291, in context
    return self.env.context
           ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'context'
2025-09-13 11:04:22,857 73168 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo16\server\odoo\modules\registry.py", line 64, in __new__
    return cls.registries[db_name]
           ~~~~~~~~~~~~~~^^^^^^^^^
  File "<decorator-gen-8>", line 2, in __getitem__
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: 'aradno_travel'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo16\server\odoo\http.py", line 2070, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1635, in _serve_db
    self.registry = Registry(self.db).check_signaling()
                    ^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 66, in __new__
    return cls.new(db_name)
           ^^^^^^^^^^^^^^^^
  File "<decorator-gen-16>", line 2, in new
  File "C:\odoo16\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\registry.py", line 87, in new
    odoo.modules.load_modules(registry, force_demo, status, update_module)
  File "C:\odoo16\server\odoo\modules\loading.py", line 485, in load_modules
    processed_modules += load_marked_modules(cr, graph,
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 373, in load_marked_modules
    loaded, processed = load_module_graph(
                        ^^^^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\modules\loading.py", line 305, in load_module_graph
    module.write({'state': 'installed', 'latest_version': ver})
  File "C:\odoo16\server\odoo\addons\website\models\ir_module_module.py", line 78, in write
    if request and request.db and request.context.get('apply_new_theme'):
                                  ^^^^^^^^^^^^^^^
  File "C:\odoo16\server\odoo\http.py", line 1291, in context
    return self.env.context
           ^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'context'
2025-09-13 11:04:22,857 73168 INFO ? werkzeug: 127.0.0.1 - - [13/Sep/2025 11:04:22] "GET /web?db=aradno_travel HTTP/1.1" 500 - 1489 1.602 3.348
