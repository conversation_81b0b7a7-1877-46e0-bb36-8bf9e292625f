<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Main Menu -->
        <menuitem id="menu_travel_agent_main"
                  name="Travel Agent"
                  sequence="50"
                  groups="group_travel_agent_user"/>
        
        <!-- Operations Menu -->
        <menuitem id="menu_travel_agent_operations"
                  name="Operations"
                  parent="menu_travel_agent_main"
                  sequence="10"/>
        
        <!-- Travel Requests Menu -->
        <menuitem id="menu_travel_ticket_requests"
                  name="Travel Requests"
                  parent="menu_travel_agent_operations"
                  action="action_travel_ticket_request"
                  sequence="10"/>
        
        <!-- Passengers Menu -->
        <menuitem id="menu_travel_passengers"
                  name="Passengers"
                  parent="menu_travel_agent_operations"
                  action="action_travel_passenger"
                  sequence="20"/>

        <!-- Travel Tickets Menu -->
        <menuitem id="menu_travel_tickets"
                  name="Travel Tickets"
                  parent="menu_travel_agent_operations"
                  action="action_travel_ticket"
                  sequence="25"/>
        
        <!-- Flight Tickets Menu -->
        <menuitem id="menu_travel_flight_segments"
                  name="Flight Tickets"
                  parent="menu_travel_agent_operations"
                  action="action_travel_flight_segment"
                  sequence="30"/>
        
        <!-- Configuration Menu -->
        <menuitem id="menu_travel_agent_config"
                  name="Configuration"
                  parent="menu_travel_agent_main"
                  sequence="90"
                  groups="group_travel_agent_manager"/>
        
        <!-- Reports Menu -->
        <menuitem id="menu_travel_agent_reports"
                  name="Reports"
                  parent="menu_travel_agent_main"
                  sequence="80"
                  groups="group_travel_agent_manager"/>
        
    </data>
</odoo> 