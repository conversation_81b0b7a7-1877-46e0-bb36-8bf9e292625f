# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class TravelTicketRequest(models.Model):
    _name = 'travel.ticket.request'
    _description = 'Travel Ticket Request'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'portal.mixin']
    _order = 'request_date desc, id desc'

    # Basic Information
    name = fields.Char(
        string='Request Number',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('New'),
        tracking=True
    )
    
    customer_id = fields.Many2one(
        'res.partner',
        string='Customer',
        required=True,
        domain=[('is_company', '=', True)],
        tracking=True,
        help="Customer company requesting the travel service"
    )
    
    request_date = fields.Date(
        string='Request Date',
        required=True,
        default=fields.Date.context_today,
        tracking=True,
        help="Date when the request was manually entered into the system"
    )
    
    assigned_to = fields.Many2one(
        'res.users',
        string='Assigned To',
        tracking=True,
        help="Team member responsible for processing this request"
    )
    
    # Passenger Information
    passenger_line_ids = fields.One2many(
        'travel.passenger.line',
        'request_id',
        string='Passengers',
        tracking=True,
        help="All passengers for this travel request"
    )
    
    passenger_count = fields.Integer(
        string='Number of Passengers',
        compute='_compute_passenger_count',
        store=True
    )
    
    # Additional Services (general for all passengers)
    additional_services = fields.Text(
        string='Additional Services',
        help="Hotel, transportation, insurance, or other services needed for all passengers"
    )
    
    # Internal Processing Status
    state = fields.Selection([
        ('draft', 'New Request'),
        ('research', 'Vendor Research'),
        ('quotation', 'Pricing Ready'),
        ('booking', 'Booking Confirmed'),
        ('delivered', 'Ticket Delivered'),
        ('invoiced', 'Customer Invoiced'),
        ('done', 'Completed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', required=True, tracking=True)
    
    # Vendor and Pricing Information - MODIFIED for multi-vendor support
    selected_vendor_id = fields.Many2one(
        'res.partner',
        string='Primary Vendor',
        domain=[('is_company', '=', True), ('supplier_rank', '>', 0)],
        tracking=True,
        help="Primary vendor (for legacy support - actual vendors are set per passenger)"
    )
    
    # Multi-vendor computed fields
    vendor_ids = fields.Many2many(
        'res.partner',
        string='All Vendors',
        compute='_compute_vendors',
        store=True,
        help="All vendors used across all passengers"
    )
    
    vendor_count = fields.Integer(
        string='Number of Vendors',
        compute='_compute_vendors',
        store=True
    )
    
    vendor_cost_total = fields.Monetary(
        string='Total Vendor Cost',
        currency_field='cost_currency_id',
        compute='_compute_totals',
        store=True,
        help="Total cost from all vendors for all passengers"
    )
    
    cost_currency_id = fields.Many2one(
        'res.currency',
        string='Cost Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    customer_price_total = fields.Monetary(
        string='Total Customer Price',
        currency_field='price_currency_id',
        compute='_compute_totals',
        store=True,
        help="Total price charged to customer for all passengers"
    )
    
    price_currency_id = fields.Many2one(
        'res.currency',
        string='Price Currency',
        default=lambda self: self.env.company.currency_id
    )
    
    margin_total = fields.Monetary(
        string='Total Margin',
        compute='_compute_totals',
        store=True,
        currency_field='price_currency_id'
    )
    
    margin_percentage = fields.Float(
        string='Margin %',
        compute='_compute_totals',
        store=True,
        digits=(16, 2)
    )
    
    # Integration with Odoo Sales/Purchase - MODIFIED for multi-vendor support
    sale_order_id = fields.Many2one(
        'sale.order',
        string='Sales Order',
        readonly=True,
        help="Customer Sales Order generated from this request"
    )
    
    purchase_order_ids = fields.One2many(
        'purchase.order',
        'travel_request_id',
        string='Purchase Orders',
        readonly=True,
        help="Vendor Purchase Orders for this request (one per vendor)"
    )
    
    purchase_order_count = fields.Integer(
        string='Purchase Order Count',
        compute='_compute_purchase_order_count',
        store=True
    )
    
    # Legacy field for backward compatibility
    purchase_order_id = fields.Many2one(
        'purchase.order',
        string='Primary Purchase Order',
        compute='_compute_primary_purchase_order',
        store=True,
        help="Primary Purchase Order (for legacy support)"
    )
    
    # Internal Notes and Communication
    internal_notes = fields.Html(
        string='Internal Notes',
        help="Internal team notes and comments"
    )
    
    customer_notes = fields.Text(
        string='Customer Notes',
        help="Notes from customer communication"
    )
    
    # NEW: Computed fields for multi-vendor support (updated for new ticket model)
    @api.depends('passenger_line_ids.ticket_ids.vendor_id')
    def _compute_vendors(self):
        for record in self:
            # Get all vendors from all tickets
            all_tickets = record.passenger_line_ids.mapped('ticket_ids')
            vendors = all_tickets.mapped('vendor_id').filtered(lambda v: v)
            record.vendor_ids = [(6, 0, vendors.ids)]
            record.vendor_count = len(vendors)
    
    @api.depends('purchase_order_ids')
    def _compute_purchase_order_count(self):
        for record in self:
            record.purchase_order_count = len(record.purchase_order_ids)
    
    @api.depends('purchase_order_ids')
    def _compute_primary_purchase_order(self):
        for record in self:
            record.purchase_order_id = record.purchase_order_ids[0] if record.purchase_order_ids else False

    # Constraints and Validations
    @api.depends('passenger_line_ids')
    def _compute_passenger_count(self):
        for record in self:
            record.passenger_count = len(record.passenger_line_ids)
    
    @api.depends('passenger_line_ids.vendor_cost', 'passenger_line_ids.customer_price')
    def _compute_totals(self):
        for record in self:
            record.vendor_cost_total = sum(record.passenger_line_ids.mapped('vendor_cost'))
            record.customer_price_total = sum(record.passenger_line_ids.mapped('customer_price'))
            record.margin_total = record.customer_price_total - record.vendor_cost_total
            
            if record.vendor_cost_total > 0:
                record.margin_percentage = (record.margin_total / record.vendor_cost_total) * 100
            else:
                record.margin_percentage = 0
    
    @api.constrains('passenger_line_ids')
    def _check_passenger_lines(self):
        for record in self:
            if not record.passenger_line_ids:
                raise ValidationError(_("At least one passenger is required."))
    
    @api.model
    def create(self, vals):
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('travel.ticket.request') or _('New')
        return super(TravelTicketRequest, self).create(vals)
    
    def action_start_research(self):
        """Move request to vendor research state"""
        self.write({'state': 'research'})
        self.message_post(body=_("Vendor research started."))
    
    def action_quotation_ready(self):
        """Move request to quotation ready state"""
        self.write({'state': 'quotation'})
        self.message_post(body=_("Pricing and quotation ready."))
    
    def action_confirm_booking(self):
        """Move request to booking confirmed state and create purchase orders"""
        # Create purchase orders for each vendor
        self._create_purchase_orders()
        self.write({'state': 'booking'})
        self.message_post(body=_("Booking confirmed and purchase orders created."))
    
    def action_deliver_ticket(self):
        """Move request to ticket delivered state"""
        self.write({'state': 'delivered'})
        self.message_post(body=_("Tickets delivered to customer."))
    
    def action_invoice_customer(self):
        """Move request to invoiced state and create sales order"""
        self._create_sale_order()
        self.write({'state': 'invoiced'})
        self.message_post(body=_("Customer invoiced - sales order created."))
    
    def action_complete(self):
        """Move request to completed state"""
        self.write({'state': 'done'})
        self.message_post(body=_("Travel request completed."))
    
    def action_cancel(self):
        """Cancel the travel request"""
        self.write({'state': 'cancelled'})
        self.message_post(body=_("Travel request cancelled."))
    
    def action_view_sale_order(self):
        """View the sales order"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': 'Sales Order',
            'view_mode': 'form',
            'res_model': 'sale.order',
            'res_id': self.sale_order_id.id,
            'target': 'current',
        }
    
    def action_view_purchase_orders(self):
        """View all purchase orders"""
        self.ensure_one()
        if self.purchase_order_count == 1:
            return {
                'type': 'ir.actions.act_window',
                'name': 'Purchase Order',
                'view_mode': 'form',
                'res_model': 'purchase.order',
                'res_id': self.purchase_order_ids[0].id,
                'target': 'current',
            }
        else:
            return {
                'type': 'ir.actions.act_window',
                'name': 'Purchase Orders',
                'view_mode': 'tree,form',
                'res_model': 'purchase.order',
                'domain': [('id', 'in', self.purchase_order_ids.ids)],
                'target': 'current',
            }
    
    # Legacy method for backward compatibility
    def action_view_purchase_order(self):
        """View primary purchase order (legacy support)"""
        return self.action_view_purchase_orders()
    
    def _create_purchase_orders(self):
        """Create purchase orders for each vendor - UPDATED for new ticket model"""
        if self.purchase_order_ids:
            return

        # Group tickets by vendor (new model)
        vendor_tickets = {}
        vendor_passengers = {}  # For backward compatibility

        for passenger_line in self.passenger_line_ids:
            # New ticket model
            for ticket in passenger_line.ticket_ids:
                if not ticket.vendor_id:
                    continue

                vendor = ticket.vendor_id
                if vendor not in vendor_tickets:
                    vendor_tickets[vendor] = []
                vendor_tickets[vendor].append(ticket)

            # Backward compatibility with old model
            if not passenger_line.ticket_ids and passenger_line.vendor_id:
                vendor = passenger_line.vendor_id
                if vendor not in vendor_passengers:
                    vendor_passengers[vendor] = []
                vendor_passengers[vendor].append(passenger_line)

        if not vendor_tickets and not vendor_passengers:
            raise ValidationError(_("Please assign vendors to all tickets/passengers before confirming booking."))
        
        # Get or create a service product for travel services
        service_product = self.env['product.product'].search([
            ('name', '=', 'Travel Service'),
            ('type', '=', 'service')
        ], limit=1)
        
        if not service_product:
            service_product = self.env['product.product'].create({
                'name': 'Travel Service',
                'type': 'service',
                'categ_id': self.env.ref('product.product_category_all').id,
                'purchase_ok': True,
                'sale_ok': True,
            })
        
        created_orders = []
        
        # Create purchase orders for new ticket model
        for vendor, tickets in vendor_tickets.items():
            order_lines = []
            for ticket in tickets:
                description = f"Travel Ticket - {ticket.passenger_id.name} ({ticket.route})"
                if ticket.travel_date:
                    description += f" - {ticket.travel_date.strftime('%Y-%m-%d')}"
                if ticket.ticket_reference:
                    description += f" - Ref: {ticket.ticket_reference}"

                order_lines.append((0, 0, {
                    'product_id': service_product.id,
                    'name': description,
                    'product_qty': 1,
                    'price_unit': ticket.vendor_cost,
                    'product_uom': service_product.uom_po_id.id,
                    'date_planned': ticket.travel_date or fields.Datetime.now(),
                }))

            po_vals = {
                'partner_id': vendor.id,
                'origin': self.name,
                'order_line': order_lines,
                'currency_id': self.cost_currency_id.id,
                'travel_request_id': self.id,
            }

            purchase_order = self.env['purchase.order'].create(po_vals)
            created_orders.append(purchase_order)

            self.message_post(
                body=_("Purchase Order %s created for vendor %s with %d tickets.") %
                (purchase_order.name, vendor.name, len(tickets))
            )

        # Create purchase orders for old passenger model (backward compatibility)
        for vendor, passenger_lines in vendor_passengers.items():
            order_lines = []
            for passenger_line in passenger_lines:
                description = f"Travel Service - {passenger_line.passenger_id.name} ({passenger_line.departure_route})"
                if passenger_line.return_route:
                    description += f" + Return ({passenger_line.return_route})"
                if passenger_line.ticket_number:
                    description += f" - Ticket: {passenger_line.ticket_number}"

                order_lines.append((0, 0, {
                    'product_id': service_product.id,
                    'name': description,
                    'product_qty': 1,
                    'price_unit': passenger_line.vendor_cost,
                    'product_uom': service_product.uom_po_id.id,
                    'date_planned': passenger_line.departure_date or fields.Datetime.now(),
                }))

            po_vals = {
                'partner_id': vendor.id,
                'origin': self.name,
                'order_line': order_lines,
                'currency_id': self.cost_currency_id.id,
                'travel_request_id': self.id,
            }

            purchase_order = self.env['purchase.order'].create(po_vals)
            created_orders.append(purchase_order)

            self.message_post(
                body=_("Purchase Order %s created for vendor %s with %d passengers (legacy).") %
                (purchase_order.name, vendor.name, len(passenger_lines))
            )
        
        return created_orders

    def _create_sale_order(self):
        """Create sales order for customer billing"""
        if not self.customer_id or self.sale_order_id:
            return
        
        # Get or create a service product for travel services
        service_product = self.env['product.product'].search([
            ('name', '=', 'Travel Service'),
            ('type', '=', 'service')
        ], limit=1)
        
        if not service_product:
            service_product = self.env['product.product'].create({
                'name': 'Travel Service',
                'type': 'service',
                'categ_id': self.env.ref('product.product_category_all').id,
                'purchase_ok': True,
                'sale_ok': True,
            })
        
        order_lines = []
        for passenger_line in self.passenger_line_ids:
            description = f"Travel Service - {passenger_line.passenger_id.name} ({passenger_line.departure_route})"
            if passenger_line.return_route:
                description += f" + Return ({passenger_line.return_route})"
            if passenger_line.ticket_number:
                description += f" - Ticket: {passenger_line.ticket_number}"
            
            order_lines.append((0, 0, {
                'product_id': service_product.id,
                'name': description,
                'product_uom_qty': 1,
                'price_unit': passenger_line.customer_price,
                'product_uom': service_product.uom_id.id,
            }))
        
        so_vals = {
            'partner_id': self.customer_id.id,
            'origin': self.name,
            'order_line': order_lines,
            'currency_id': self.price_currency_id.id,
            'pricelist_id': self.customer_id.property_product_pricelist.id or self.env['product.pricelist'].search([('currency_id', '=', self.price_currency_id.id)], limit=1).id or 1,
        }
        
        sale_order = self.env['sale.order'].create(so_vals)
        self.sale_order_id = sale_order.id
        
        return sale_order

    def _get_portal_return_action(self):
        """ Return the action to redirect to portal after message is posted. """
        self.ensure_one()
        return {
            'type': 'ir.actions.act_url',
            'url': '/my/travel_request/%s' % self.id
        }


class TravelPassengerLine(models.Model):
    _name = 'travel.passenger.line'
    _description = 'Travel Request Passenger Line'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'request_id, sequence, id'

    sequence = fields.Integer(string='Sequence', default=10)
    request_id = fields.Many2one(
        'travel.ticket.request',
        string='Travel Request',
        required=True,
        ondelete='cascade'
    )
    
    passenger_id = fields.Many2one(
        'travel.passenger',
        string='Passenger',
        required=True
    )
    
    # Readonly passport info from passenger
    passenger_passport_number = fields.Char(
        string='Passport Number',
        related='passenger_id.passport_number',
        readonly=True
    )
    
    passenger_nationality = fields.Many2one(
        'res.country',
        string='Nationality',
        related='passenger_id.nationality_id',
        readonly=True
    )
    
    # Travel Details (specific to each passenger)
    travel_purpose = fields.Selection([
        ('business', 'Business'),
        ('personal', 'Personal'),
        ('emergency', 'Emergency'),
        ('medical', 'Medical'),
        ('training', 'Training'),
        ('conference', 'Conference'),
        ('other', 'Other')
    ], string='Travel Purpose', required=True)
    
    departure_route = fields.Char(
        string='Departure Route',
        required=False,
        help="From - To (e.g., New York - London) - Optional, will be filled from tickets"
    )
    
    departure_date = fields.Datetime(
        string='Departure Date',
        required=False,
        help="Departure date and time for this passenger - Optional, will be filled from tickets"
    )
    
    return_route = fields.Char(
        string='Return Route',
        help="From - To for return journey (e.g., London - New York)"
    )
    
    return_date = fields.Datetime(
        string='Return Date',
        help="Return date and time for this passenger (leave empty for one-way)"
    )
    
    # Passenger-specific travel details
    ticket_class = fields.Selection([
        ('economy', 'Economy'),
        ('premium_economy', 'Premium Economy'),
        ('business', 'Business'),
        ('first', 'First Class')
    ], string='Ticket Class', required=True, default='economy')
    
    seat_preference = fields.Selection([
        ('aisle', 'Aisle'),
        ('window', 'Window'),
        ('middle', 'Middle'),
        ('no_preference', 'No Preference')
    ], string='Seat Preference', default='no_preference')
    
    meal_preference = fields.Selection([
        ('regular', 'Regular'),
        ('vegetarian', 'Vegetarian'),
        ('vegan', 'Vegan'),
        ('halal', 'Halal'),
        ('kosher', 'Kosher'),
        ('gluten_free', 'Gluten Free'),
        ('diabetic', 'Diabetic'),
        ('no_meal', 'No Meal')
    ], string='Meal Preference', default='regular')
    
    special_requirements = fields.Text(
        string='Special Requirements',
        help="Wheelchair assistance, medical needs, etc."
    )
    
    # NEW: Vendor information per passenger
    vendor_id = fields.Many2one(
        'res.partner',
        string='Vendor',
        domain=[('is_company', '=', True), ('supplier_rank', '>', 0)],
        help="Vendor who will issue the ticket for this passenger"
    )
    
    # NEW: Travel Tickets (Simplified)
    ticket_ids = fields.One2many(
        'travel.ticket',
        'passenger_line_id',
        string='Travel Tickets',
        help="Travel tickets for this passenger"
    )

    ticket_count = fields.Integer(
        string='Number of Tickets',
        compute='_compute_ticket_count',
        store=True,
        help="Total number of tickets"
    )

    ticket_references = fields.Char(
        string='Ticket References',
        compute='_compute_ticket_references',
        store=True,
        help="All ticket reference numbers for this passenger"
    )

    # Keep old flight segments for backward compatibility (will be deprecated)
    flight_segment_ids = fields.One2many(
        'travel.flight.segment',
        'passenger_line_id',
        string='Flight Segments (Legacy)',
        help="Legacy flight segments - use Travel Tickets instead"
    )

    segment_count = fields.Integer(
        string='Number of Segments (Legacy)',
        compute='_compute_segment_count',
        store=True,
        help="Total number of flight segments (legacy)"
    )

    has_layovers = fields.Boolean(
        string='Has Multiple Tickets',
        compute='_compute_ticket_count',
        store=True,
        help="Whether this journey has multiple tickets"
    )
    
    # Pricing per passenger (computed from tickets)
    vendor_cost = fields.Monetary(
        string='Total Vendor Cost',
        currency_field='cost_currency_id',
        compute='_compute_totals',
        store=True,
        help="Total cost from all tickets for this passenger"
    )

    cost_currency_id = fields.Many2one(
        'res.currency',
        string='Cost Currency',
        related='request_id.cost_currency_id',
        store=True
    )

    customer_price = fields.Monetary(
        string='Total Customer Price',
        currency_field='price_currency_id',
        compute='_compute_totals',
        store=True,
        help="Total price for all tickets for this passenger"
    )

    price_currency_id = fields.Many2one(
        'res.currency',
        string='Price Currency',
        related='request_id.price_currency_id',
        store=True
    )

    margin = fields.Monetary(
        string='Total Margin',
        compute='_compute_totals',
        store=True,
        currency_field='price_currency_id'
    )
    
    # Ticket details
    ticket_number = fields.Char(string='Ticket Number')
    seat_number = fields.Char(string='Seat Number')
    
    # Computed Travel Info
    days_until_departure = fields.Integer(
        string='Days Until Departure',
        compute='_compute_days_until_departure'
    )
    
    # Status
    status = fields.Selection([
        ('draft', 'Draft'),
        ('booked', 'Booked'),
        ('ticketed', 'Ticketed'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', compute='_compute_passenger_status', store=True)
    
    @api.depends('ticket_ids.vendor_cost', 'ticket_ids.customer_price')
    def _compute_totals(self):
        for line in self:
            # Calculate from new ticket model
            if line.ticket_ids:
                line.vendor_cost = sum(line.ticket_ids.mapped('vendor_cost'))
                line.customer_price = sum(line.ticket_ids.mapped('customer_price'))
            else:
                # Backward compatibility: if no tickets, keep existing values
                # This allows gradual migration from old to new model
                pass

            line.margin = line.customer_price - line.vendor_cost

    @api.depends('ticket_ids')
    def _compute_ticket_count(self):
        for line in self:
            line.ticket_count = len(line.ticket_ids)
            line.has_layovers = line.ticket_count > 1

    @api.depends('ticket_ids.ticket_reference')
    def _compute_ticket_references(self):
        """Compute all ticket reference numbers for this passenger"""
        for line in self:
            if line.ticket_ids:
                references = line.ticket_ids.mapped('ticket_reference')
                # Filter out empty references
                references = [ref for ref in references if ref]
                line.ticket_references = ', '.join(references) if references else ''
            else:
                line.ticket_references = ''

    @api.depends('ticket_ids.status')
    def _compute_passenger_status(self):
        """Compute passenger status based on ticket statuses"""
        for line in self:
            if not line.ticket_ids:
                line.status = 'draft'
                continue

            ticket_statuses = line.ticket_ids.mapped('status')

            # If any ticket is cancelled, check if all are cancelled
            if 'cancelled' in ticket_statuses:
                if all(status == 'cancelled' for status in ticket_statuses):
                    line.status = 'cancelled'
                else:
                    # Mixed statuses with some cancelled - keep current logic
                    pass

            # If all tickets are ticketed
            elif all(status == 'ticketed' for status in ticket_statuses):
                line.status = 'ticketed'

            # If all tickets are booked or higher
            elif all(status in ['booked', 'ticketed'] for status in ticket_statuses):
                line.status = 'booked'

            # Otherwise keep as draft
            else:
                line.status = 'draft'

    @api.depends('flight_segment_ids')
    def _compute_segment_count(self):
        for line in self:
            line.segment_count = len(line.flight_segment_ids)
    
    @api.depends('departure_date')
    def _compute_days_until_departure(self):
        today = fields.Date.context_today(self)
        for line in self:
            if line.departure_date:
                departure_date = line.departure_date.date()
                line.days_until_departure = (departure_date - today).days
            else:
                line.days_until_departure = 0
    
    @api.constrains('departure_date', 'return_date')
    def _check_travel_dates(self):
        for line in self:
            if line.departure_date:
                departure_date = line.departure_date.date()
                today = fields.Date.context_today(line)
                
                # Check if departure is not in the past
                if departure_date < today:
                    raise ValidationError(_("Departure date cannot be in the past."))
                
                # Check return date if provided
                if line.return_date:
                    if line.return_date <= line.departure_date:
                        raise ValidationError(_("Return date must be after departure date."))
    
    @api.onchange('request_id')
    def _onchange_request_id(self):
        """Set default values from travel request"""
        if self.request_id:
            # Set default ticket class if available
            pass
    
    @api.onchange('passenger_id')
    def _onchange_passenger_id(self):
        """Set default values from passenger preferences"""
        if self.passenger_id:
            self.seat_preference = self.passenger_id.seat_preference or 'no_preference'
            self.meal_preference = self.passenger_id.meal_preference or 'regular'
            self.special_requirements = self.passenger_id.special_requirements
    
    def name_get(self):
        result = []
        for line in self:
            name = f"{line.passenger_id.name}"
            if line.request_id:
                name = f"{line.request_id.name} - {name}"
            result.append((line.id, name))
        return result

    def _update_travel_summary_from_tickets(self):
        """Update departure route and date from first ticket"""
        for line in self:
            if line.ticket_ids:
                # Get first ticket (by sequence)
                first_ticket = line.ticket_ids.sorted('sequence')[0] if line.ticket_ids else False
                if first_ticket:
                    if first_ticket.route and not line.departure_route:
                        line.departure_route = first_ticket.route
                    if first_ticket.travel_date and not line.departure_date:
                        line.departure_date = first_ticket.travel_date

