# -*- coding: utf-8 -*-

from odoo import models, fields, api


class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'
    
    travel_request_id = fields.Many2one(
        'travel.ticket.request',
        string='Travel Request',
        help="Travel request this purchase order is related to"
    )
    
    travel_passenger_count = fields.Integer(
        string='Passengers Count',
        compute='_compute_travel_info',
        help="Number of passengers in this purchase order"
    )
    
    @api.depends('travel_request_id', 'order_line')
    def _compute_travel_info(self):
        for order in self:
            if order.travel_request_id:
                # Count passengers by counting order lines related to travel services
                passenger_lines = order.travel_request_id.passenger_line_ids.filtered(
                    lambda line: line.vendor_id == order.partner_id
                )
                order.travel_passenger_count = len(passenger_lines)
            else:
                order.travel_passenger_count = 0 