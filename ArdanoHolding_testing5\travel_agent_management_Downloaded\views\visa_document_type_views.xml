<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Visa Document Type Tree View -->
        <record id="view_visa_document_type_tree" model="ir.ui.view">
            <field name="name">visa.document.type.tree</field>
            <field name="model">visa.document.type</field>
            <field name="arch" type="xml">
                <tree string="Document Types" editable="bottom">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="file_type"/>
                    <field name="max_file_size"/>
                    <field name="is_required"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Visa Document Type Form View -->
        <record id="view_visa_document_type_form" model="ir.ui.view">
            <field name="name">visa.document.type.form</field>
            <field name="model">visa.document.type</field>
            <field name="arch" type="xml">
                <form string="Document Type">
                    <sheet>
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                                attrs="{'invisible': [('active', '=', True)]}"/>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Document Type Name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="sequence"/>
                                <field name="is_required"/>
                                <field name="active"/>
                            </group>
                            <group>
                                <field name="file_type"/>
                                <field name="max_file_size"/>
                            </group>
                        </group>
                        
                        <group string="Description">
                            <field name="description" nolabel="1" placeholder="Enter description for this document type..."/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Visa Document Type Search View -->
        <record id="view_visa_document_type_search" model="ir.ui.view">
            <field name="name">visa.document.type.search</field>
            <field name="model">visa.document.type</field>
            <field name="arch" type="xml">
                <search string="Search Document Types">
                    <field name="name"/>
                    <field name="description"/>
                    <separator/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="Required" name="required" domain="[('is_required', '=', True)]"/>
                    <filter string="Optional" name="optional" domain="[('is_required', '=', False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="File Type" name="group_file_type" context="{'group_by': 'file_type'}"/>
                        <filter string="Required" name="group_required" context="{'group_by': 'is_required'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Visa Document Type Action -->
        <record id="action_visa_document_type" model="ir.actions.act_window">
            <field name="name">Document Types</field>
            <field name="res_model">visa.document.type</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_visa_document_type_search"/>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first document type!
                </p>
                <p>
                    Define the types of documents that can be required for visa applications,
                    such as passport copy, personal photo, bank statement, etc.
                </p>
            </field>
        </record>

    </data>
</odoo>
