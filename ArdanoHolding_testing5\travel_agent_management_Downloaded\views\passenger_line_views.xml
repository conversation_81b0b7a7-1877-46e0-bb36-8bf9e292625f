<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Passenger Line Form View for Flight Segment Management -->
        <record id="view_travel_passenger_line_form" model="ir.ui.view">
            <field name="name">travel.passenger.line.form</field>
            <field name="model">travel.passenger.line</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <field name="status" widget="statusbar" statusbar_visible="draft,booked,ticketed"/>
                    </header>
                    
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="passenger_id" readonly="1"/>
                            </h1>
                            <div class="o_row">
                                <field name="request_id" readonly="1" class="oe_inline"/>
                                <span class="oe_grey"> • </span>
                                <field name="travel_purpose" readonly="1" class="oe_inline"/>
                            </div>
                        </div>
                        
                        <group>
                            <group name="passenger_info" string="Passenger Information">
                                <field name="passenger_passport_number"/>
                                <field name="passenger_nationality"/>
                                <field name="departure_route"/>
                                <field name="departure_date"/>
                                <field name="return_route"/>
                                <field name="return_date"/>
                            </group>
                            <group name="booking_info" string="Booking Information">
                                <field name="status"/>
                                <field name="days_until_departure"/>
                                <field name="ticket_references" string="All Ticket Numbers"/>
                            </group>
                        </group>
                        
                        <group>
                            <group name="ticket_summary" string="Ticket Summary">
                                <field name="ticket_count" string="Number of Tickets"/>
                                <field name="has_layovers" string="Multiple Tickets"/>
                            </group>
                            <group name="pricing" string="Total Pricing">
                                <field name="vendor_cost"/>
                                <field name="customer_price"/>
                                <field name="margin"/>
                            </group>
                        </group>
                        
                        <group name="preferences" string="Preferences">
                            <group>
                                <field name="seat_preference"/>
                                <field name="meal_preference"/>
                            </group>
                            <group>
                                <field name="special_requirements"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Travel Tickets" name="travel_tickets">
                                <p class="text-muted">
                                    إضافة تذاكر السفر لهذا المسافر. كل تذكرة تمثل حجز منفصل مع مورد.
                                    مثال: تذكرة 1 (القاهرة→دبي) وتذكرة 2 (دبي→واشنطن) لرحلة من القاهرة إلى واشنطن.
                                </p>

                                <field name="ticket_ids">
                                    <tree editable="bottom" create="true" delete="true">
                                        <field name="sequence" widget="handle"/>
                                        <field name="route" placeholder="القاهرة → دبي"/>
                                        <field name="travel_date"/>
                                        <field name="vendor_id"/>
                                        <field name="vendor_cost"/>
                                        <field name="customer_price"/>
                                        <field name="margin" readonly="1"/>
                                        <field name="ticket_reference" placeholder="رقم التذكرة"/>
                                        <field name="status" widget="badge" decoration-info="status=='draft'" decoration-warning="status=='quoted'" decoration-primary="status=='booked'" decoration-success="status=='ticketed'" decoration-danger="status=='cancelled'"/>
                                        <!-- Smart single button that shows next action -->
                                        <button name="action_set_quoted" type="object" string="→ Quote" title="Set as Quoted" class="btn-sm btn-warning" icon="fa-quote-left" attrs="{'invisible': [('status', '!=', 'draft')]}"/>
                                        <button name="action_set_booked" type="object" string="→ Book" title="Set as Booked" class="btn-sm btn-primary" icon="fa-calendar-check-o" attrs="{'invisible': [('status', '!=', 'quoted')]}"/>
                                        <button name="action_set_ticketed" type="object" string="→ Ticket" title="Set as Ticketed" class="btn-sm btn-success" icon="fa-plane" attrs="{'invisible': [('status', '!=', 'booked')]}"/>
                                        <!-- Cancel button only for non-cancelled tickets -->
                                        <button name="action_cancel" type="object" string="✕" title="Cancel Ticket" class="btn-sm btn-outline-danger" icon="fa-times" attrs="{'invisible': [('status', '=', 'cancelled')]}"/>
                                    </tree>

                                </field>
                                
                                <div class="alert alert-info mt-3" role="alert">
                                    <strong>نصائح لإدخال التذاكر:</strong>
                                    <ul class="mb-0">
                                        <li><strong>الترتيب:</strong> استخدم 10، 20، 30... للحفاظ على ترتيب التذاكر</li>
                                        <li><strong>صيغة المسار:</strong> "المدينة → المدينة" - مثل "القاهرة → دبي"</li>
                                        <li><strong>المورد:</strong> اختر المورد المناسب لكل تذكرة</li>
                                        <li><strong>التسعير:</strong> أدخل سعر المورد وسعر العميل لكل تذكرة منفصلة</li>
                                    </ul>
                                </div>
                            </page>
                            
                            <page string="Journey Overview" name="journey_overview">
                                <div class="row">
                                    <div class="col-6">
                                        <h4>Ticket Statistics</h4>
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Total Tickets:</strong></td>
                                                <td><field name="ticket_count" readonly="1"/></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Multiple Tickets:</strong></td>
                                                <td><field name="has_layovers" readonly="1"/></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Departure Date:</strong></td>
                                                <td><field name="departure_date" readonly="1"/></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-6">
                                        <h4>Route Summary</h4>
                                        <field name="departure_route" readonly="1" placeholder="Simple route description"/>
                                        <field name="return_route" readonly="1" placeholder="Return route (if applicable)"/>
                                        
                                        <h5 class="mt-3">Ticket Summary</h5>
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Number of Tickets:</strong></td>
                                                <td><field name="ticket_count" readonly="1"/></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Ticket References:</strong></td>
                                                <td><field name="ticket_references" readonly="1"/></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Total Cost:</strong></td>
                                                <td><field name="vendor_cost" readonly="1"/></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>
        
    </data>
</odoo> 