# Travel Agent Management Module

## Overview
A comprehensive Odoo 16 module for managing travel agent operations, replacing Excel-based ticket request systems with a fully integrated digital solution.

## Features

### Core Functionality
- **Digital Ticket Requests**: Replace Excel workflows with structured forms
- **Passenger Management**: Comprehensive passenger database with passport tracking
- **Workflow Management**: Track requests from draft to completion
- **Vendor Integration**: Manage vendor relationships and pricing
- **Financial Integration**: Seamless integration with Odoo Sales and Purchase
- **Customer Portal**: Self-service portal for customers to submit and track requests

### Key Models

#### Travel Ticket Request (`travel.ticket.request`)
- Request tracking with auto-generated sequence numbers
- Customer and passenger information
- Travel details (destinations, dates, class, purpose)
- Internal workflow states (draft → research → quotation → booking → delivered → invoiced → done)
- Vendor and pricing management with multi-currency support
- Automatic Sales Order and Purchase Order creation
- Margin calculation and reporting

#### Travel Passenger (`travel.passenger`)
- Personal and contact information
- Passport details with expiration tracking
- Travel preferences (seat, meal, special requirements)
- Travel history and statistics
- Document management for passports, visas, and other documents
- Passport validity warnings and checks

### Security Groups
- **Travel Agent User**: Create and manage own requests
- **Travel Agent Manager**: Full access to all requests and reporting
- **Travel Agent Admin**: System configuration and maintenance
- **Travel Agent Accounting**: Read-only access for financial information

### Business Process

1. **Request Entry**: Team member manually enters customer email request
2. **Vendor Research**: Internal team checks vendors and applies markup
3. **Booking Confirmation**: Create Purchase Order for vendor
4. **Ticket Delivery**: Deliver tickets to customer
5. **Customer Invoicing**: Create Sales Order and invoice customer
6. **Payment Collection**: Standard Odoo payment processing

## Installation

1. Copy the module to your Odoo addons directory
2. Update the module list in Odoo
3. Install the "Travel Agent Management" module
4. Configure user permissions as needed

## Configuration

### Initial Setup
1. **User Groups**: Assign users to appropriate security groups
2. **Vendors**: Set up vendor partners with supplier rank
3. **Customers**: Ensure customer companies are properly configured
4. **Currencies**: Configure required currencies for international pricing

### Sequence Configuration
- Travel request numbers follow format: TR{YEAR}{MONTH}{XXXX}
- Sequence can be modified in Settings > Technical > Sequences

## Usage

### Creating a Travel Request
1. Navigate to Travel Agent > Operations > Travel Requests
2. Click "Create" to start a new request
3. Fill in customer, passenger, and travel details
4. Use workflow buttons to progress through states

### Managing Passengers
1. Navigate to Travel Agent > Operations > Passengers
2. Create passenger profiles with passport information
3. Use the "Check Passport" button to verify validity
4. Track travel history and document uploads

### Workflow States
- **Draft**: Initial request entry
- **Research**: Vendor research and pricing
- **Quotation**: Pricing ready for customer
- **Booking**: Vendor booking confirmed
- **Delivered**: Tickets delivered to customer
- **Invoiced**: Customer invoice created
- **Done**: Process completed

## Technical Details

### Portal Features
The module includes a comprehensive customer portal that allows:

#### For Portal Users (Customers)
- **Travel Request Submission**: Create new travel requests directly from the portal
- **Request Tracking**: View status and details of all submitted requests
- **Passenger Management**: Add and manage passenger profiles with passport information
- **Communication**: Send messages and communicate with the travel team
- **History Tracking**: View complete travel request history

#### Portal Access Control
- Portal users can only access their own company's data
- Secure access through Odoo's standard portal authentication
- Role-based permissions ensure data privacy and security

#### Portal URLs
- `/my/travel_requests` - View all travel requests
- `/my/travel_request/new` - Create new travel request
- `/my/passengers` - Manage passenger profiles
- `/my/passenger/new` - Add new passenger

### Dependencies
- base
- sale
- purchase
- account
- contacts
- mail
- web
- portal
- website

### File Structure
```
travel_agent_management/
├── __init__.py
├── __manifest__.py
├── models/
│   ├── __init__.py
│   ├── ticket_request.py
│   └── passenger.py
├── controllers/
│   ├── __init__.py
│   └── portal.py
├── views/
│   ├── menu_views.xml
│   ├── ticket_request_views.xml
│   ├── passenger_views.xml
│   └── portal_templates.xml
├── security/
│   ├── security_groups.xml
│   ├── portal_security.xml
│   └── ir.model.access.csv
├── data/
│   └── sequences.xml
└── demo/
    └── demo_data.xml
```

### Key Features Implemented
- ✅ Core models with validation
- ✅ Security groups and permissions
- ✅ Complete view definitions
- ✅ Workflow states and actions
- ✅ Multi-currency support
- ✅ Integration with Sales/Purchase
- ✅ Demo data for testing
- ✅ Passport expiration tracking
- ✅ Document management
- ✅ Customer portal integration
- ✅ Portal self-service features
- ✅ Secure portal access control
- ✅ Real-time communication via portal

## Support

For support or questions about this module, please contact the development team at Ardano Holdings.

## License

This module is licensed under LGPL-3. 