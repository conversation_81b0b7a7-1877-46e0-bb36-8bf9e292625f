<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Visa Type Tree View -->
        <record id="view_visa_type_tree" model="ir.ui.view">
            <field name="name">visa.type.tree</field>
            <field name="model">visa.type</field>
            <field name="arch" type="xml">
                <tree string="Visa Types">
                    <field name="sequence" widget="handle"/>
                    <field name="country_id"/>
                    <field name="name"/>
                    <field name="visa_category"/>
                    <field name="price" widget="monetary"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="processing_days"/>
                    <field name="request_count"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Visa Type Form View -->
        <record id="view_visa_type_form" model="ir.ui.view">
            <field name="name">visa.type.form</field>
            <field name="model">visa.type</field>
            <field name="arch" type="xml">
                <form string="Visa Type">
                    <header>
                        <button name="action_view_requests" type="object" 
                                string="View Requests" class="btn-primary"
                                attrs="{'invisible': [('request_count', '=', 0)]}"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_requests" type="object" 
                                    class="oe_stat_button" icon="fa-file-text">
                                <field name="request_count" widget="statinfo" 
                                       string="Requests"/>
                            </button>
                        </div>
                        
                        <widget name="web_ribbon" title="Archived" bg_color="bg-danger" 
                                attrs="{'invisible': [('active', '=', True)]}"/>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Visa Type Name"/>
                            </h1>
                            <h2>
                                <field name="country_id" placeholder="Select Country"/>
                            </h2>
                        </div>
                        
                        <group>
                            <group>
                                <field name="visa_category"/>
                                <field name="sequence"/>
                                <field name="price" widget="monetary"/>
                                <field name="currency_id"/>
                            </group>
                            <group>
                                <field name="processing_days"/>
                                <field name="active"/>
                            </group>
                        </group>
                        
                        <group string="Description">
                            <field name="description" nolabel="1" 
                                   placeholder="Enter description for this visa type..."/>
                        </group>
                        
                        <notebook>
                            <page string="Required Documents" name="documents">
                                <field name="document_type_ids">
                                    <tree>
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="file_type"/>
                                        <field name="max_file_size"/>
                                        <field name="is_required"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Additional Fields" name="fields">
                                <field name="field_type_ids">
                                    <tree>
                                        <field name="sequence" widget="handle"/>
                                        <field name="name"/>
                                        <field name="field_type"/>
                                        <field name="is_required"/>
                                        <field name="placeholder"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Visa Type Kanban View -->
        <record id="view_visa_type_kanban" model="ir.ui.view">
            <field name="name">visa.type.kanban</field>
            <field name="model">visa.type</field>
            <field name="arch" type="xml">
                <kanban default_group_by="country_id">
                    <field name="id"/>
                    <field name="name"/>
                    <field name="country_id"/>
                    <field name="price"/>
                    <field name="currency_id"/>
                    <field name="processing_days"/>
                    <field name="request_count"/>
                    <field name="active"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="oe_kanban_content">
                                    <div class="o_kanban_record_top">
                                        <div class="o_kanban_record_headings">
                                            <strong class="o_kanban_record_title">
                                                <field name="name"/>
                                            </strong>
                                        </div>
                                        <div class="o_kanban_manage_button_section">
                                            <a class="o_kanban_manage_toggle_button" href="#">
                                                <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="o_kanban_record_body">
                                        <div class="o_kanban_record_bottom">
                                            <div class="oe_kanban_bottom_left">
                                                <span class="o_kanban_record_subtitle">
                                                    <field name="price" widget="monetary"/> • 
                                                    <field name="processing_days"/> days
                                                </span>
                                            </div>
                                            <div class="oe_kanban_bottom_right">
                                                <span class="badge badge-pill badge-info">
                                                    <field name="request_count"/> requests
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Visa Type Search View -->
        <record id="view_visa_type_search" model="ir.ui.view">
            <field name="name">visa.type.search</field>
            <field name="model">visa.type</field>
            <field name="arch" type="xml">
                <search string="Search Visa Types">
                    <field name="name"/>
                    <field name="country_id"/>
                    <field name="description"/>
                    <separator/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                    <separator/>
                    <filter string="Has Requests" name="has_requests"
                            domain="[('request_count', '>', 0)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Country" name="group_country" context="{'group_by': 'country_id'}"/>
                        <filter string="Status" name="group_active" context="{'group_by': 'active'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Visa Type Action -->
        <record id="action_visa_type" model="ir.actions.act_window">
            <field name="name">Visa Types</field>
            <field name="res_model">visa.type</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="search_view_id" ref="view_visa_type_search"/>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first visa type!
                </p>
                <p>
                    Define visa types for each country with their specific requirements,
                    pricing, and processing times.
                </p>
            </field>
        </record>

    </data>
</odoo>
