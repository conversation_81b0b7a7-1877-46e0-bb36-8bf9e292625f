<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Flight Segment (Ticket) Tree View -->
        <record id="view_travel_flight_segment_tree" model="ir.ui.view">
            <field name="name">travel.flight.segment.tree</field>
            <field name="model">travel.flight.segment</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="sequence" widget="handle"/>
                    <field name="passenger_id"/>
                    <field name="departure_route"/>
                    <field name="departure_date"/>
                    <field name="return_route" optional="show"/>
                    <field name="return_date" optional="show"/>
                    <field name="flight_number" optional="show"/>
                    <field name="airline" optional="show"/>
                    <field name="ticket_class"/>
                    <field name="vendor_cost"/>
                    <field name="customer_price"/>
                    <field name="margin"/>
                    <field name="status" widget="badge" decoration-success="status=='ticketed'" decoration-warning="status=='booked'" decoration-danger="status=='cancelled'"/>
                </tree>
            </field>
        </record>
        
        <!-- Flight Segment (Ticket) Form View -->
        <record id="view_travel_flight_segment_form" model="ir.ui.view">
            <field name="name">travel.flight.segment.form</field>
            <field name="model">travel.flight.segment</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <field name="status" widget="statusbar" statusbar_visible="draft,booked,ticketed"/>
                    </header>
                    
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="departure_route" placeholder="Route (e.g., New York → London)"/>
                            </h1>
                            <div class="o_row">
                                <field name="flight_number" placeholder="Flight Number" class="oe_inline"/>
                                <span class="oe_grey"> • </span>
                                <field name="airline" placeholder="Airline" class="oe_inline"/>
                            </div>
                        </div>
                        
                        <group>
                            <group name="ticket_info" string="Ticket Information">
                                <field name="sequence"/>
                                <field name="passenger_line_id" readonly="1" attrs="{'invisible': [('passenger_line_id', '=', False)]}"/>
                                <field name="ticket_class"/>
                                <field name="ticket_number"/>
                            </group>
                            <group name="timing" string="Travel Dates">
                                <field name="departure_date"/>
                                <field name="return_date"/>
                            </group>
                        </group>
                        
                        <group>
                            <group name="route_info" string="Route Information">
                                <field name="departure_route"/>
                                <field name="return_route"/>
                            </group>
                            <group name="pricing" string="Pricing">
                                <field name="vendor_cost"/>
                                <field name="customer_price"/>
                                <field name="margin" readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
        
        <!-- Flight Segment (Ticket) Search View -->
        <record id="view_travel_flight_segment_search" model="ir.ui.view">
            <field name="name">travel.flight.segment.search</field>
            <field name="model">travel.flight.segment</field>
            <field name="arch" type="xml">
                <search>
                    <field name="departure_route"/>
                    <field name="flight_number"/>
                    <field name="airline"/>
                    <field name="passenger_id"/>
                    <field name="ticket_number"/>
                    
                    <filter string="Draft" name="draft" domain="[('status', '=', 'draft')]"/>
                    <filter string="Booked" name="booked" domain="[('status', '=', 'booked')]"/>
                    <filter string="Ticketed" name="ticketed" domain="[('status', '=', 'ticketed')]"/>
                    <filter string="Cancelled" name="cancelled" domain="[('status', '=', 'cancelled')]"/>
                    
                    <separator/>
                    <filter string="Today's Departures" name="today" domain="[('departure_date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('departure_date', '&lt;', datetime.datetime.combine(context_today() + datetime.timedelta(days=1), datetime.time(0,0,0)))]"/>
                    <filter string="This Week" name="week" domain="[('departure_date', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')), ('departure_date', '&lt;', (context_today() + datetime.timedelta(days=7-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Passenger" name="group_passenger" context="{'group_by': 'passenger_id'}"/>
                        <filter string="Airline" name="group_airline" context="{'group_by': 'airline'}"/>
                        <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                        <filter string="Class" name="group_class" context="{'group_by': 'ticket_class'}"/>
                        <filter string="Departure Date" name="group_departure" context="{'group_by': 'departure_date:day'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Flight Segment (Ticket) Action -->
        <record id="action_travel_flight_segment" model="ir.actions.act_window">
            <field name="name">Flight Tickets</field>
            <field name="res_model">travel.flight.segment</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_travel_flight_segment_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a flight ticket
                </p>
                <p>
                    Flight tickets represent individual bookings for passengers.
                    Each ticket contains route information, dates, pricing, and booking details.
                    Use multiple tickets for complex journeys with stops.
                </p>
            </field>
        </record>
        
    </data>
</odoo> 