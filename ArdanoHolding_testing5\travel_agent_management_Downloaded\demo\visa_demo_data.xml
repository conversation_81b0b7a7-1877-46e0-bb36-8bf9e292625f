<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Demo Visa Countries -->
        <record id="visa_country_usa" model="visa.country">
            <field name="name">United States</field>
            <field name="code">USA</field>
            <field name="flag_emoji">🇺🇸</field>
        </record>
        
        <record id="visa_country_uk" model="visa.country">
            <field name="name">United Kingdom</field>
            <field name="code">GBR</field>
            <field name="flag_emoji">🇬🇧</field>
        </record>
        
        <record id="visa_country_canada" model="visa.country">
            <field name="name">Canada</field>
            <field name="code">CAN</field>
            <field name="flag_emoji">🇨🇦</field>
        </record>
        
        <record id="visa_country_italy" model="visa.country">
            <field name="name">Italy</field>
            <field name="code">ITA</field>
            <field name="flag_emoji">🇮🇹</field>
        </record>
        
        <!-- Demo Document Types -->
        <record id="doc_type_passport" model="visa.document.type">
            <field name="name">Passport Copy</field>
            <field name="description">Clear copy of passport main page</field>
            <field name="sequence">10</field>
            <field name="is_required">True</field>
            <field name="file_type">image</field>
            <field name="max_file_size">5</field>
        </record>
        
        <record id="doc_type_photo" model="visa.document.type">
            <field name="name">Passport Photo</field>
            <field name="description">Recent passport-size photograph</field>
            <field name="sequence">20</field>
            <field name="is_required">True</field>
            <field name="file_type">image</field>
            <field name="max_file_size">2</field>
        </record>
        
        <record id="doc_type_bank_statement" model="visa.document.type">
            <field name="name">Bank Statement</field>
            <field name="description">Bank statement for last 3 months</field>
            <field name="sequence">30</field>
            <field name="is_required">True</field>
            <field name="file_type">pdf</field>
            <field name="max_file_size">10</field>
        </record>
        
        <record id="doc_type_invitation" model="visa.document.type">
            <field name="name">Invitation Letter</field>
            <field name="description">Official invitation letter</field>
            <field name="sequence">40</field>
            <field name="is_required">False</field>
            <field name="file_type">pdf</field>
            <field name="max_file_size">5</field>
        </record>
        
        <record id="doc_type_hotel_booking" model="visa.document.type">
            <field name="name">Hotel Booking</field>
            <field name="description">Hotel reservation confirmation</field>
            <field name="sequence">50</field>
            <field name="is_required">True</field>
            <field name="file_type">pdf</field>
            <field name="max_file_size">5</field>
        </record>
        
        <record id="doc_type_flight_ticket" model="visa.document.type">
            <field name="name">Flight Ticket</field>
            <field name="description">Round-trip flight reservation</field>
            <field name="sequence">60</field>
            <field name="is_required">True</field>
            <field name="file_type">pdf</field>
            <field name="max_file_size">5</field>
        </record>
        
        <!-- Demo Visa Types -->
        <record id="visa_type_usa_tourist" model="visa.type">
            <field name="name">Tourist Visa</field>
            <field name="country_id" ref="visa_country_usa"/>
            <field name="visa_category">tourist</field>
            <field name="price">160.00</field>
            <field name="processing_days">15</field>
            <field name="description">B-2 Tourist visa for leisure travel</field>
            <field name="document_type_ids" eval="[(6, 0, [ref('doc_type_passport'), ref('doc_type_photo'), ref('doc_type_bank_statement'), ref('doc_type_hotel_booking'), ref('doc_type_flight_ticket')])]"/>
        </record>
        
        <record id="visa_type_usa_business" model="visa.type">
            <field name="name">Business Visa</field>
            <field name="country_id" ref="visa_country_usa"/>
            <field name="visa_category">business</field>
            <field name="price">160.00</field>
            <field name="processing_days">10</field>
            <field name="description">B-1 Business visa for business activities</field>
            <field name="document_type_ids" eval="[(6, 0, [ref('doc_type_passport'), ref('doc_type_photo'), ref('doc_type_bank_statement'), ref('doc_type_invitation')])]"/>
        </record>
        
        <record id="visa_type_uk_tourist" model="visa.type">
            <field name="name">Standard Visitor Visa</field>
            <field name="country_id" ref="visa_country_uk"/>
            <field name="visa_category">tourist</field>
            <field name="price">95.00</field>
            <field name="processing_days">21</field>
            <field name="description">Standard visitor visa for tourism</field>
            <field name="document_type_ids" eval="[(6, 0, [ref('doc_type_passport'), ref('doc_type_photo'), ref('doc_type_bank_statement'), ref('doc_type_hotel_booking')])]"/>
        </record>
        
        <record id="visa_type_canada_tourist" model="visa.type">
            <field name="name">Visitor Visa</field>
            <field name="country_id" ref="visa_country_canada"/>
            <field name="visa_category">tourist</field>
            <field name="price">100.00</field>
            <field name="processing_days">14</field>
            <field name="description">Temporary Resident Visa for tourism</field>
            <field name="document_type_ids" eval="[(6, 0, [ref('doc_type_passport'), ref('doc_type_photo'), ref('doc_type_bank_statement'), ref('doc_type_flight_ticket')])]"/>
        </record>
        
        <record id="visa_type_italy_schengen" model="visa.type">
            <field name="name">Schengen Visa</field>
            <field name="country_id" ref="visa_country_italy"/>
            <field name="visa_category">tourist</field>
            <field name="price">80.00</field>
            <field name="processing_days">15</field>
            <field name="description">Short-stay Schengen visa</field>
            <field name="document_type_ids" eval="[(6, 0, [ref('doc_type_passport'), ref('doc_type_photo'), ref('doc_type_bank_statement'), ref('doc_type_hotel_booking'), ref('doc_type_flight_ticket')])]"/>
        </record>
        
    </data>
</odoo>
